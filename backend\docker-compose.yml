# Docker Compose pour XCapital Backend avec <PERSON>y, Redis et PostgreSQL
version: '3.8'

services:
  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: xcapital_postgres
    environment:
      POSTGRES_DB: xcapital
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - xcapital_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis pour Celery et cache
  redis:
    image: redis:7-alpine
    container_name: xcapital_redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - xcapital_network
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Application Django
  django:
    build: .
    container_name: xcapital_django
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py runserver 0.0.0.0:8000"
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    environment:
      - DEBUG=True
      - DATABASE_URL=*****************************************************/xcapital
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0

      # Configuration Casablanca Stock Exchange Scraper
      - CASABLANCA_USER_AGENT=XCapital-Bot/1.0 (+https://xcapitalterminal.com/robots.txt)
      - CASABLANCA_TIMEOUT=30
      - CASABLANCA_MAX_RETRIES=3
      - CASABLANCA_RETRY_DELAY=60
      - CASABLANCA_RATE_LIMIT=2
      - CASABLANCA_VERIFY_SSL=False
      - CASABLANCA_BATCH_SIZE=10
      - CASABLANCA_AUDIT_TRAIL=True
      - CASABLANCA_SAVE_RAW=True
      - CASABLANCA_DB_BATCH_SIZE=50
      - CASABLANCA_LOG_LEVEL=INFO
      - CASABLANCA_LOG_RESPONSES=False

      # Configuration legacy pour compatibilité
      - TARGET_URLS=https://www.xcapitalterminal.com,https://finance.yahoo.com/news,https://www.investing.com/news
      - SCRAPER_USER_AGENT=XCapital-Bot/1.0 (+https://xcapitalterminal.com/robots.txt)
      - SCRAPER_TIMEOUT=30
      - SCRAPER_MAX_RETRIES=3
      - SCRAPER_RETRY_DELAY=60
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - xcapital_network
    restart: unless-stopped

  # Worker Celery
  celery_worker:
    build: .
    container_name: xcapital_celery_worker
    command: celery -A xcapital_backend worker --loglevel=info --concurrency=2
    volumes:
      - .:/app
      - ./logs:/app/logs
    environment:
      - DEBUG=True
      - DATABASE_URL=*****************************************************/xcapital
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0

      # Configuration Casablanca Stock Exchange Scraper
      - CASABLANCA_USER_AGENT=XCapital-Bot/1.0 (+https://xcapitalterminal.com/robots.txt)
      - CASABLANCA_TIMEOUT=30
      - CASABLANCA_MAX_RETRIES=3
      - CASABLANCA_RETRY_DELAY=60
      - CASABLANCA_RATE_LIMIT=2
      - CASABLANCA_VERIFY_SSL=False
      - CASABLANCA_BATCH_SIZE=10
      - CASABLANCA_AUDIT_TRAIL=True
      - CASABLANCA_SAVE_RAW=True
      - CASABLANCA_DB_BATCH_SIZE=50
      - CASABLANCA_LOG_LEVEL=INFO
      - CASABLANCA_LOG_RESPONSES=False

      # Configuration legacy pour compatibilité
      - TARGET_URLS=https://www.xcapitalterminal.com,https://finance.yahoo.com/news,https://www.investing.com/news
      - SCRAPER_USER_AGENT=XCapital-Bot/1.0 (+https://xcapitalterminal.com/robots.txt)
      - SCRAPER_TIMEOUT=30
      - SCRAPER_MAX_RETRIES=3
      - SCRAPER_RETRY_DELAY=60
    depends_on:
      - django
      - redis
    networks:
      - xcapital_network
    restart: unless-stopped

  # Scheduler Celery Beat
  celery_beat:
    build: .
    container_name: xcapital_celery_beat
    command: celery -A xcapital_backend beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    volumes:
      - .:/app
      - ./logs:/app/logs
    environment:
      - DEBUG=True
      - DATABASE_URL=*****************************************************/xcapital
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0

      # Configuration Casablanca Stock Exchange Scraper
      - CASABLANCA_USER_AGENT=XCapital-Bot/1.0 (+https://xcapitalterminal.com/robots.txt)
      - CASABLANCA_TIMEOUT=30
      - CASABLANCA_MAX_RETRIES=3
      - CASABLANCA_RETRY_DELAY=60
      - CASABLANCA_RATE_LIMIT=2
      - CASABLANCA_VERIFY_SSL=False
      - CASABLANCA_BATCH_SIZE=10
      - CASABLANCA_AUDIT_TRAIL=True
      - CASABLANCA_SAVE_RAW=True
      - CASABLANCA_DB_BATCH_SIZE=50
      - CASABLANCA_LOG_LEVEL=INFO
      - CASABLANCA_LOG_RESPONSES=False
    depends_on:
      - django
      - redis
    networks:
      - xcapital_network
    restart: unless-stopped

  # Flower pour monitoring Celery (optionnel)
  flower:
    build: .
    container_name: xcapital_flower
    command: celery -A xcapital_backend flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
    networks:
      - xcapital_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  static_volume:
  media_volume:

networks:
  xcapital_network:
    driver: bridge
