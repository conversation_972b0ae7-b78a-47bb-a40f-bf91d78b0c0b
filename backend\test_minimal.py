#!/usr/bin/env python3

import os
import sys
from datetime import date
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

import django
django.setup()

print("=== TEST SAUVEGARDE BASE DE DONNÉES ===")

# Test du module improved_database_saver
try:
    from improved_database_saver import save_company_data, get_latest_prices, get_companies_from_database
    
    # Données de test
    test_data = {
        'company_id': 468,
        'symbol': 'BOA',
        'company_name': 'Bank of Africa',
        'date_trade': date.today(),
        'open_price': Decimal('180.00'),
        'high_price': Decimal('185.00'),
        'low_price': Decimal('178.00'),
        'close_price': Decimal('182.50'),
        'volume': 12500,
        'value_mad': Decimal('2281250.00')
    }
    
    print("1. Test sauvegarde Bank of Africa...")
    success = save_company_data(test_data)
    print(f"   Résultat: {'✅ Succès' if success else '❌ Échec'}")
    
    print("2. Vérification entreprises en BDD...")
    companies = get_companies_from_database()
    print(f"   Entreprises trouvées: {len(companies)}")
    
    print("3. Vérification derniers prix...")
    prices = get_latest_prices(5)
    print(f"   Prix trouvés: {len(prices)}")
    
    for price in prices:
        print(f"   {price['symbol']}: {price['price']} MAD ({price['date']})")
    
    print("4. Test orchestrateur...")
    from improved_daily_orchestrator import improved_orchestrator
    
    result = improved_orchestrator.test_single_company(468)
    if result['success']:
        print(f"   ✅ {result['company_name']}: {result['closing_price']} MAD")
        print(f"   Sauvé en BDD: {result['database_saved']}")
    else:
        print(f"   ❌ Erreur: {result['error']}")
        
    print("\n=== RÉSULTAT ===")
    if success and len(companies) > 0 and result['success']:
        print("🎉 SYSTÈME OPÉRATIONNEL! Les données sont correctement sauvées.")
    else:
        print("⚠️ Problèmes détectés.")
        
except Exception as e:
    print(f"❌ Erreur: {e}")
    import traceback
    traceback.print_exc()
