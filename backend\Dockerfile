# Dockerfile pour XCapital Backend avec support Celery
FROM python:3.11-slim

# Définir les variables d'environnement
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DJANGO_SETTINGS_MODULE=xcapital_backend.settings

# Créer et définir le répertoire de travail
WORKDIR /app

# Installer les dépendances système
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copier les fichiers de requirements
COPY requirements.txt .
COPY scraper_requirements.txt .

# Installer les dépendances Python
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir -r scraper_requirements.txt

# Copier le code de l'application
COPY . .

# Créer le répertoire pour les logs
RUN mkdir -p logs

# Créer un utilisateur non-root pour des raisons de sécurité
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

# Exposer le port
EXPOSE 8000

# Script de démarrage par défaut (Django)
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
