"""
Tests pour le système de scraping Casablanca Stock Exchange
"""

import json
from decimal import Decimal
from datetime import date, datetime
from unittest.mock import patch, <PERSON><PERSON>, MagicMock
from django.test import TestCase, TransactionTestCase
from django.urls import reverse
from django.utils import timezone
from django.db import connection
from celery.result import AsyncR<PERSON>ult

from .models import ScrapedItem, ScraperTarget, ScraperExecution, CasablancaApiExecution
from .tasks import (
    run_casablanca_scraper,
    test_casablanca_api_connectivity,
    cleanup_old_data,
    _fetch_casablanca_api_data,
    _parse_company_data,
    _save_company_to_database
)
from market_data.postgres_models import XCapitalCompany, XCapitalCompanyBond


# Test data samples
SAMPLE_CASABLANCA_API_RESPONSE = {
    "data": {
        "data": [
            {
                "id": "468",
                "type": "instrument",
                "attributes": {
                    "symbol": "BOA",
                    "instrument_name": "Bank of Africa",
                    "open_price": "180.00",
                    "close_price": "182.50",
                    "high_price": "185.00",
                    "low_price": "178.00",
                    "current_price": "182.50",
                    "volume": "10000",
                    "shares_traded": "8500",
                    "total_trades": "45",
                    "market_cap": "1825000.00",
                    "variation": "2.50",
                    "variation_percent": "1.39",
                    "currency": "MAD"
                },
                "relationships": {
                    "symbol": {
                        "data": {
                            "id": "BOA",
                            "type": "symbol"
                        }
                    }
                }
            },
            {
                "id": "511",
                "type": "instrument",
                "attributes": {
                    "symbol": "AWB",
                    "instrument_name": "Attijariwafa Bank",
                    "open_price": "520.00",
                    "close_price": "525.00",
                    "high_price": "530.00",
                    "low_price": "518.00",
                    "current_price": "525.00",
                    "volume": "5000",
                    "shares_traded": "4200",
                    "total_trades": "28",
                    "market_cap": "2625000.00",
                    "variation": "5.00",
                    "variation_percent": "0.96",
                    "currency": "MAD"
                }
            }
        ]
    },
    "meta": {
        "total": 2,
        "page": 1
    }
}

SAMPLE_EMPTY_API_RESPONSE = {
    "data": {
        "data": []
    },
    "meta": {
        "total": 0,
        "page": 1
    }
}


class CasablancaScraperModelsTest(TestCase):
    """Tests pour les modèles du scraper Casablanca"""

    def setUp(self):
        self.target = ScraperTarget.objects.create(
            name="Casablanca Stock Exchange",
            url="https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action",
            is_active=True,
            scraper_type="casablanca_api"
        )

    def test_scraper_target_creation(self):
        """Test de création d'une cible de scraping Casablanca"""
        self.assertEqual(self.target.name, "Casablanca Stock Exchange")
        self.assertTrue(self.target.is_active)
        self.assertEqual(self.target.scraper_type, "casablanca_api")

    def test_scraped_item_casablanca_creation(self):
        """Test de création d'un élément scrapé Casablanca"""
        item = ScrapedItem.objects.create(
            source_url="https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action",
            scraper_type="casablanca_api",
            title="Bank of Africa",
            company_symbol="BOA",
            company_id="468",
            close_price=Decimal("182.50"),
            volume=10000,
            status="success",
            target=self.target
        )

        self.assertEqual(item.company_symbol, "BOA")
        self.assertEqual(item.company_id, "468")
        self.assertEqual(item.close_price, Decimal("182.50"))
        self.assertEqual(item.volume, 10000)
        self.assertEqual(item.status, "success")
        self.assertEqual(item.scraper_type, "casablanca_api")

    def test_scraped_item_data_methods(self):
        """Test des méthodes de données brutes et traitées"""
        item = ScrapedItem.objects.create(
            source_url="https://example.com",
            scraper_type="casablanca_api",
            title="Test Company",
            status="success"
        )

        # Test raw_data methods
        item.set_raw_data('api_response', {'id': '468', 'symbol': 'BOA'})
        item.save()

        self.assertEqual(item.get_raw_data('api_response')['symbol'], 'BOA')
        self.assertIsNone(item.get_raw_data('non_existent'))

        # Test processed_data methods
        item.set_processed_data('parsed_price', 182.50)
        item.save()

        self.assertEqual(item.get_processed_data('parsed_price'), 182.50)

        # Test mark_success and mark_failed
        item.mark_success(processing_duration=1.5)
        self.assertEqual(item.status, 'success')
        self.assertEqual(item.processing_duration, 1.5)

        item.mark_failed("Test error", retry_count=1)
        self.assertEqual(item.status, 'failed')
        self.assertEqual(item.error_message, "Test error")
        self.assertEqual(item.retry_count, 1)

    def test_casablanca_api_execution_creation(self):
        """Test de création d'une exécution API Casablanca"""
        execution = CasablancaApiExecution.objects.create(
            task_id="test-casablanca-123",
            execution_date=date.today(),
            total_companies_api=100,
            companies_processed=95,
            companies_saved=90,
            companies_failed=5,
            page1_companies=50,
            page2_companies=50,
            page1_success=True,
            page2_success=True,
            api_urls_used=["https://api1.com", "https://api2.com"],
            user_agent="XCapital-Bot/1.0"
        )

        self.assertEqual(execution.task_id, "test-casablanca-123")
        self.assertEqual(execution.total_companies_api, 100)
        self.assertEqual(execution.companies_saved, 90)
        self.assertEqual(execution.success_rate, 90.0)
        self.assertEqual(execution.api_success_rate, 100.0)

        # Test add_log
        execution.add_log("Test message")
        self.assertIn("Test message", execution.logs)

        # Test complete
        execution.complete(success=True)
        self.assertEqual(execution.status, 'completed')
        self.assertIsNotNone(execution.completed_at)

        # Test update_stats
        execution.update_stats(companies_saved=95, companies_failed=0)
        self.assertEqual(execution.companies_saved, 95)
        self.assertEqual(execution.companies_failed, 0)


class CasablancaScraperTasksTest(TransactionTestCase):
    """Tests pour les tâches Celery du scraper Casablanca"""

    def setUp(self):
        self.target = ScraperTarget.objects.create(
            name="Casablanca Stock Exchange",
            url="https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action",
            is_active=True,
            scraper_type="casablanca_api"
        )

    def test_parse_company_data(self):
        """Test de parsing des données d'entreprise Casablanca"""
        company_data = SAMPLE_CASABLANCA_API_RESPONSE['data']['data'][0]

        result = _parse_company_data(company_data)

        self.assertEqual(result['symbol'], "BOA")
        self.assertEqual(result['name'], "Bank of Africa")
        self.assertEqual(result['close_price'], Decimal("182.50"))
        self.assertEqual(result['volume'], 10000)
        self.assertEqual(result['market_cap'], Decimal("1825000.00"))
        self.assertEqual(result['variation_percent'], Decimal("1.39"))
        self.assertIn('last_updated', result)

    def test_parse_company_data_missing_fields(self):
        """Test de parsing avec des champs manquants"""
        incomplete_data = {
            "id": "468",
            "type": "instrument",
            "attributes": {
                "symbol": "BOA",
                "instrument_name": "Bank of Africa",
                "close_price": "182.50"
                # Champs manquants: volume, market_cap, etc.
            }
        }

        result = _parse_company_data(incomplete_data)

        self.assertEqual(result['symbol'], "BOA")
        self.assertEqual(result['close_price'], Decimal("182.50"))
        self.assertEqual(result['volume'], 0)  # Valeur par défaut
        self.assertEqual(result['market_cap'], Decimal("0"))  # Valeur par défaut

    @patch('scraper.tasks.requests.get')
    def test_fetch_casablanca_api_data(self, mock_get):
        """Test de récupération des données API Casablanca"""
        # Mock de la réponse HTTP
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = SAMPLE_CASABLANCA_API_RESPONSE
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response

        # Test de récupération
        url = "https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action"
        result = _fetch_casablanca_api_data(url, page=1)

        self.assertEqual(result['status'], 'success')
        self.assertEqual(len(result['companies']), 2)
        self.assertEqual(result['companies'][0]['symbol'], 'BOA')
        self.assertEqual(result['companies'][1]['symbol'], 'AWB')

    @patch('scraper.tasks.requests.get')
    def test_fetch_casablanca_api_data_error(self, mock_get):
        """Test de gestion d'erreur API"""
        # Mock d'une erreur HTTP
        mock_get.side_effect = Exception("Connection error")

        url = "https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action"
        result = _fetch_casablanca_api_data(url, page=1)

        self.assertEqual(result['status'], 'error')
        self.assertIn('Connection error', result['error'])

    @patch('scraper.tasks.requests.get')
    def test_test_casablanca_api_connectivity(self, mock_get):
        """Test de connectivité API Casablanca"""
        # Mock de la réponse HTTP
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = SAMPLE_CASABLANCA_API_RESPONSE
        mock_response.content = b'{"data": {"data": []}}'
        mock_get.return_value = mock_response

        # Créer une tâche mock
        class MockTask:
            def __init__(self):
                self.request = Mock()
                self.request.id = "test-connectivity-123"

        task = MockTask()

        # Exécuter le test de connectivité
        result = test_casablanca_api_connectivity.apply(args=[], kwargs={})

        # Vérifier les résultats
        self.assertTrue(result.successful())
        result_data = result.result
        self.assertEqual(result_data['status'], 'success')
        self.assertTrue(result_data['all_apis_available'])

    @patch('scraper.tasks.requests.get')
    @patch('scraper.tasks.cache')
    def test_run_casablanca_scraper_full(self, mock_cache, mock_get):
        """Test complet du scraper Casablanca"""
        # Mock du cache pour le lock
        mock_cache.add.return_value = True  # Lock acquired
        mock_cache.delete.return_value = True

        # Mock de la réponse HTTP
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = SAMPLE_CASABLANCA_API_RESPONSE
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response

        # Exécuter la tâche principale
        result = run_casablanca_scraper.apply(args=[], kwargs={})

        # Vérifier les résultats
        self.assertTrue(result.successful())
        result_data = result.result
        self.assertEqual(result_data['status'], 'success')
        self.assertGreater(result_data['companies_processed'], 0)

        # Vérifier qu'une exécution a été créée
        self.assertTrue(CasablancaApiExecution.objects.filter(
            task_id=result.id
        ).exists())

        # Vérifier que des ScrapedItems ont été créés
        self.assertTrue(ScrapedItem.objects.filter(
            scraper_type='casablanca_api'
        ).exists())

    @patch('scraper.tasks.requests.get')
    @patch('scraper.tasks.cache')
    def test_run_casablanca_scraper_lock_conflict(self, mock_cache, mock_get):
        """Test de conflit de lock"""
        # Mock du cache pour simuler un lock déjà pris
        mock_cache.add.return_value = False  # Lock not acquired
        mock_cache.get.return_value = "existing-task-456"

        # Exécuter la tâche
        result = run_casablanca_scraper.apply(args=[], kwargs={})

        # Vérifier les résultats
        self.assertTrue(result.successful())
        result_data = result.result
        self.assertEqual(result_data['status'], 'skipped')
        self.assertIn('existing-task-456', result_data['reason'])

    def test_cleanup_old_data(self):
        """Test de nettoyage des anciennes données"""
        # Créer des données anciennes
        old_date = timezone.now() - timezone.timedelta(days=35)

        ScrapedItem.objects.create(
            source_url="https://example.com",
            title="Old Item",
            status="success",
            extracted_at=old_date
        )

        CasablancaApiExecution.objects.create(
            task_id="old-task-123",
            execution_date=old_date.date(),
            status='completed',
            started_at=old_date
        )

        # Créer des données récentes
        ScrapedItem.objects.create(
            source_url="https://example.com",
            title="Recent Item",
            status="success"
        )

        # Exécuter le nettoyage
        result = cleanup_old_data.apply(args=[30], kwargs={})

        # Vérifier les résultats
        self.assertTrue(result.successful())
        result_data = result.result
        self.assertEqual(result_data['status'], 'success')
        self.assertGreater(result_data['scraped_items_deleted'], 0)

        # Vérifier que seules les données récentes restent
        self.assertEqual(ScrapedItem.objects.count(), 1)
        self.assertEqual(ScrapedItem.objects.first().title, "Recent Item")


class CasablancaDatabaseIntegrationTest(TransactionTestCase):
    """Tests d'intégration avec la base de données"""

    def setUp(self):
        # Créer des données de test dans les tables XCapital
        with connection.cursor() as cursor:
            # Créer une entreprise de test
            cursor.execute("""
                INSERT INTO "XCapitalTerminal_Companies"
                (symbol, name, sector, market_cap, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING id
            """, [
                'BOA', 'Bank of Africa', 'Banking',
                Decimal('1000000'), timezone.now(), timezone.now()
            ])
            self.company_id = cursor.fetchone()[0]

    def test_save_company_to_database(self):
        """Test de sauvegarde d'entreprise en base"""
        company_data = {
            'symbol': 'BOA',
            'name': 'Bank of Africa',
            'close_price': Decimal('182.50'),
            'volume': 10000,
            'market_cap': Decimal('1825000.00'),
            'variation_percent': Decimal('1.39'),
            'last_updated': timezone.now()
        }

        # Sauvegarder l'entreprise
        result = _save_company_to_database(company_data, 'test-task-123')

        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['action'], 'updated')  # Entreprise existante

        # Vérifier que les données ont été mises à jour
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT market_cap FROM "XCapitalTerminal_Companies"
                WHERE symbol = %s
            """, ['BOA'])
            updated_market_cap = cursor.fetchone()[0]
            self.assertEqual(updated_market_cap, Decimal('1825000.00'))

    def test_save_new_company_to_database(self):
        """Test de sauvegarde d'une nouvelle entreprise"""
        company_data = {
            'symbol': 'AWB',
            'name': 'Attijariwafa Bank',
            'close_price': Decimal('525.00'),
            'volume': 5000,
            'market_cap': Decimal('2625000.00'),
            'variation_percent': Decimal('0.96'),
            'last_updated': timezone.now()
        }

        # Sauvegarder la nouvelle entreprise
        result = _save_company_to_database(company_data, 'test-task-123')

        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['action'], 'created')

        # Vérifier que l'entreprise a été créée
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT COUNT(*) FROM "XCapitalTerminal_Companies"
                WHERE symbol = %s
            """, ['AWB'])
            count = cursor.fetchone()[0]
            self.assertEqual(count, 1)


class CasablancaScraperAPITest(TestCase):
    """Tests pour l'API du scraper Casablanca"""

    def setUp(self):
        self.target = ScraperTarget.objects.create(
            name="Casablanca Stock Exchange",
            url="https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action",
            is_active=True,
            scraper_type="casablanca_api"
        )

        self.item = ScrapedItem.objects.create(
            source_url="https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action",
            title="Bank of Africa",
            company_symbol="BOA",
            company_id="468",
            close_price=Decimal("182.50"),
            volume=10000,
            status="success",
            scraper_type="casablanca_api",
            target=self.target
        )

    def test_scraped_items_list_api(self):
        """Test de l'API de liste des éléments scrapés Casablanca"""
        url = reverse('scraper:scraped-items-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('results', data)
        self.assertEqual(len(data['results']), 1)
        self.assertEqual(data['results'][0]['title'], "Bank of Africa")
        self.assertEqual(data['results'][0]['company_symbol'], "BOA")

    def test_scraped_items_list_api_with_filters(self):
        """Test de l'API avec filtres Casablanca"""
        url = reverse('scraper:scraped-items-list')

        # Test filtre par type de scraper
        response = self.client.get(url + '?scraper_type=casablanca_api')
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data['results']), 1)

        # Test filtre par symbole d'entreprise
        response = self.client.get(url + '?company_symbol=BOA')
        data = response.json()
        self.assertEqual(len(data['results']), 1)

        # Test filtre par symbole inexistant
        response = self.client.get(url + '?company_symbol=UNKNOWN')
        data = response.json()
        self.assertEqual(len(data['results']), 0)

    def test_scraped_item_detail_api(self):
        """Test de l'API de détail d'un élément Casablanca"""
        url = reverse('scraper:scraped-item-detail', args=[self.item.pk])
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['title'], "Bank of Africa")
        self.assertEqual(data['company_symbol'], "BOA")
        self.assertEqual(data['close_price'], "182.50")
        self.assertEqual(data['volume'], 10000)

    def test_scraper_stats_api(self):
        """Test de l'API des statistiques Casablanca"""
        url = reverse('scraper:scraper-stats')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('total_items', data)
        self.assertIn('items_by_status', data)
        self.assertIn('items_by_scraper_type', data)
        self.assertEqual(data['total_items'], 1)
        self.assertEqual(data['items_by_scraper_type']['casablanca_api'], 1)

    @patch('scraper.views.run_casablanca_scraper.delay')
    def test_trigger_casablanca_scraper_api(self, mock_delay):
        """Test de l'API de déclenchement du scraper Casablanca"""
        # Mock de la tâche Celery
        mock_task = Mock()
        mock_task.id = "test-casablanca-123"
        mock_delay.return_value = mock_task

        url = reverse('scraper:trigger-casablanca-scraper')
        response = self.client.post(url)

        self.assertEqual(response.status_code, 202)
        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['task_id'], 'test-casablanca-123')
        mock_delay.assert_called_once()

    @patch('scraper.views.test_casablanca_api_connectivity.delay')
    def test_test_casablanca_connectivity_api(self, mock_delay):
        """Test de l'API de test de connectivité Casablanca"""
        # Mock de la tâche Celery
        mock_task = Mock()
        mock_task.id = "test-connectivity-456"
        mock_delay.return_value = mock_task

        url = reverse('scraper:test-casablanca-connectivity')
        response = self.client.post(url)

        self.assertEqual(response.status_code, 202)
        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['task_id'], 'test-connectivity-456')
        mock_delay.assert_called_once()

    def test_health_check_api(self):
        """Test de l'API de health check"""
        url = reverse('scraper:health-check')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('status', data)
        self.assertIn('checks', data)
        self.assertIn('database', data['checks'])
        self.assertIn('casablanca_api', data['checks'])


class CasablancaScraperAdminTest(TestCase):
    """Tests pour l'interface d'administration Casablanca"""

    def setUp(self):
        from django.contrib.auth.models import User
        self.admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='password'
        )
        self.client.login(username='admin', password='password')

        self.target = ScraperTarget.objects.create(
            name="Casablanca Stock Exchange",
            url="https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action",
            is_active=True,
            scraper_type="casablanca_api"
        )

    def test_admin_scraped_item_list(self):
        """Test de la liste des éléments scrapés Casablanca dans l'admin"""
        ScrapedItem.objects.create(
            source_url="https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action",
            title="Bank of Africa",
            company_symbol="BOA",
            company_id="468",
            close_price=Decimal("182.50"),
            volume=10000,
            status="success",
            scraper_type="casablanca_api",
            target=self.target
        )

        response = self.client.get('/admin/scraper/scrapeditem/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Bank of Africa")
        self.assertContains(response, "BOA")

    def test_admin_casablanca_execution_list(self):
        """Test de la liste des exécutions Casablanca dans l'admin"""
        CasablancaApiExecution.objects.create(
            task_id="test-casablanca-123",
            execution_date=date.today(),
            total_companies_api=100,
            companies_processed=95,
            companies_saved=90,
            status='completed'
        )

        response = self.client.get('/admin/scraper/casablancaapiexecution/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "test-casablanca-123")

    def test_admin_scraper_target_list(self):
        """Test de la liste des cibles dans l'admin"""
        response = self.client.get('/admin/scraper/scrapertarget/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Casablanca Stock Exchange")
