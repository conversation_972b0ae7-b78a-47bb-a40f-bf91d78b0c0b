"""
Orchestrateur pour intégrer le système de mise à jour quotidienne
avec Celery et le système de scraping existant
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

# Ajouter le chemin vers daily_updater
current_dir = Path(__file__).parent
daily_updater_dir = current_dir / "daily_updater"
sys.path.insert(0, str(daily_updater_dir))

try:
    from daily_data_fetcher import CasablancaBourseUpdater
    from config import Config
    DAILY_UPDATER_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Module daily_updater non disponible: {e}")
    DAILY_UPDATER_AVAILABLE = False

class DailyUpdateOrchestrator:
    """
    Orchestrateur principal pour coordonner la mise à jour quotidienne
    avec le système Celery existant
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        if not DAILY_UPDATER_AVAILABLE:
            self.logger.error("❌ Modules daily_updater non disponibles")
            return
        
        # Initialiser le composant principal
        self.updater = None
        self.last_update = None
        self.update_status = "idle"
        
        self.logger.info("🎯 DailyUpdateOrchestrator initialisé")
    
    def initialize_updater(self) -> bool:
        """Initialise l'updater de données"""
        if not DAILY_UPDATER_AVAILABLE:
            return False
        
        try:
            self.updater = CasablancaBourseUpdater()
            self.logger.info("✅ CasablancaBourseUpdater initialisé")
            return True
        except Exception as e:
            self.logger.error(f"❌ Erreur initialisation updater: {e}")
            return False
    
    def run_daily_update(self, target_date: str = None, test_mode: bool = False) -> Dict:
        """
        Exécute la mise à jour quotidienne
        
        Args:
            target_date: Date cible (YYYY-MM-DD)
            test_mode: Mode test avec moins d'entreprises
        
        Returns:
            Dict: Résultat de la mise à jour
        """
        if not DAILY_UPDATER_AVAILABLE:
            return {
                'success': False,
                'error': 'Modules daily_updater non disponibles',
                'timestamp': datetime.now().isoformat()
            }
        
        if not self.updater:
            if not self.initialize_updater():
                return {
                    'success': False,
                    'error': 'Échec initialisation updater',
                    'timestamp': datetime.now().isoformat()
                }
        
        try:
            self.update_status = "running"
            start_time = datetime.now()
            
            self.logger.info(f"🚀 Début mise à jour quotidienne - Mode: {'TEST' if test_mode else 'PRODUCTION'}")
            
            if test_mode:
                # Mode test avec 5 entreprises
                success = self.updater.test_with_sample_companies(5)
            else:
                # Mode production avec toutes les entreprises
                if target_date:
                    success = self.updater.update_all_companies(target_date=target_date)
                else:
                    success = self.updater.update_all_companies()
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            # Créer le rapport
            report_path = ""
            if success:
                report_path = self.updater.create_summary_report(target_date)
            
            # Statistiques finales
            result = {
                'success': success,
                'duration_seconds': duration.total_seconds(),
                'companies_processed': self.updater.companies_processed,
                'companies_successful': self.updater.companies_successful,
                'companies_failed': self.updater.companies_failed,
                'total_records': self.updater.total_records,
                'report_path': report_path,
                'timestamp': start_time.isoformat(),
                'target_date': target_date or Config.get_target_date(),
                'test_mode': test_mode
            }
            
            self.last_update = result
            self.update_status = "completed" if success else "failed"
            
            # Nettoyage
            self.updater.cleanup()
            
            self.logger.info(f"✅ Mise à jour terminée - Succès: {success}")
            return result
            
        except Exception as e:
            self.update_status = "error"
            error_result = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'target_date': target_date or Config.get_target_date(),
                'test_mode': test_mode
            }
            
            self.logger.error(f"❌ Erreur mise à jour: {e}")
            return error_result
    
    def test_single_company(self, company_id: int, target_date: str = None) -> Dict:
        """
        Test avec une seule entreprise
        
        Args:
            company_id: ID de l'entreprise à tester
            target_date: Date cible
        
        Returns:
            Dict: Résultat du test
        """
        if not DAILY_UPDATER_AVAILABLE:
            return {
                'success': False,
                'error': 'Modules daily_updater non disponibles'
            }
        
        if not self.updater:
            if not self.initialize_updater():
                return {
                    'success': False,
                    'error': 'Échec initialisation updater'
                }
        
        try:
            self.logger.info(f"🧪 Test entreprise {company_id}")
            
            success, summary = self.updater.update_single_company(company_id, target_date)
            
            if success:
                # Test sauvegarde Django
                django_stats = self.updater.django_saver.save_all_data([summary])
                
                return {
                    'success': True,
                    'company_id': company_id,
                    'company_name': summary.get('company_name', f'Entreprise_{company_id}'),
                    'closing_price': summary.get('closing_price', 0),
                    'volume': summary.get('volume', 0),
                    'django_save_success': django_stats.get('success', False),
                    'bonds_saved': django_stats.get('bonds_saved', 0),
                    'summary': summary
                }
            else:
                return {
                    'success': False,
                    'company_id': company_id,
                    'error': f'Échec récupération données pour entreprise {company_id}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'company_id': company_id,
                'error': str(e)
            }
    
    def get_system_status(self) -> Dict:
        """Retourne le statut du système"""
        status = {
            'daily_updater_available': DAILY_UPDATER_AVAILABLE,
            'updater_initialized': self.updater is not None,
            'update_status': self.update_status,
            'last_update': self.last_update,
            'timestamp': datetime.now().isoformat()
        }
        
        if DAILY_UPDATER_AVAILABLE and self.updater:
            # Vérifier la connexion API
            try:
                api_connected = self.updater.api_client.test_connection()
                status['api_connection'] = api_connected
            except:
                status['api_connection'] = False
            
            # Vérifier la connexion Django/DB
            try:
                db_connected = self.updater.django_saver.verify_database_connection()
                status['database_connection'] = db_connected
            except:
                status['database_connection'] = False
            
            # Informations sur les dernières données
            try:
                django_info = self.updater.django_saver.get_latest_data_info()
                status['latest_data'] = django_info
            except:
                status['latest_data'] = {'error': 'Non disponible'}
        
        return status
    
    def get_configuration(self) -> Dict:
        """Retourne la configuration du système"""
        if not DAILY_UPDATER_AVAILABLE:
            return {'error': 'Configuration non disponible'}
        
        return {
            'moroccan_companies_count': len(Config.MOROCCAN_COMPANIES),
            'request_delay': Config.REQUEST_DELAY,
            'max_retries': Config.MAX_RETRIES,
            'casablanca_base_url': Config.CASABLANCA_BASE_URL,
            'required_fields': Config.REQUIRED_FIELDS,
            'max_price_variation': Config.MAX_PRICE_VARIATION,
            'data_directories': {
                'base_dir': str(Config.BASE_DIR),
                'data_dir': str(Config.DATA_DIR),
                'logs_dir': str(Config.LOGS_DIR)
            }
        }

# Instance globale pour utilisation dans les tâches Celery
daily_orchestrator = DailyUpdateOrchestrator()
