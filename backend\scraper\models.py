"""
Modèles Django pour le système de scraping
"""

from django.db import models
from django.utils import timezone
import json


class ScraperTarget(models.Model):
    """
    Table pour stocker les URLs cibles à scraper
    """
    name = models.CharField(
        max_length=200, 
        help_text="Nom descriptif de la source"
    )
    url = models.URLField(
        max_length=500,
        help_text="URL à scraper"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Si False, cette URL sera ignorée lors du scraping"
    )
    scraper_type = models.CharField(
        max_length=50,
        choices=[
            ('html', 'HTML Simple'),
            ('javascript', 'Nécessite JavaScript'),
            ('api', 'API Endpoint'),
        ],
        default='html',
        help_text="Type de scraper à utiliser"
    )
    css_selectors = models.JSONField(
        default=dict,
        blank=True,
        help_text="Sélecteurs CSS pour extraire les données (JSON)"
    )
    headers = models.J<PERSON><PERSON>ield(
        default=dict,
        blank=True,
        help_text="Headers HTTP personnalisés (JSON)"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'scraper_targets'
        verbose_name = 'Scraper Target'
        verbose_name_plural = 'Scraper Targets'

    def __str__(self):
        return f"{self.name} - {self.url}"


class ScrapedItem(models.Model):
    """
    Modèle pour stocker les éléments scrapés avec audit trail pour Casablanca Stock Exchange
    """
    STATUS_CHOICES = [
        ('pending', 'En attente'),
        ('processing', 'En cours'),
        ('success', 'Succès'),
        ('failed', 'Échec'),
        ('skipped', 'Ignoré'),
    ]

    SCRAPER_TYPE_CHOICES = [
        ('casablanca_api', 'Casablanca Stock Exchange API'),
        ('html', 'HTML Simple'),
        ('javascript', 'JavaScript Required'),
        ('api', 'Generic API'),
    ]

    source_url = models.URLField(
        max_length=500,
        help_text="URL source de l'élément scrapé"
    )
    scraper_type = models.CharField(
        max_length=50,
        choices=SCRAPER_TYPE_CHOICES,
        default='casablanca_api',
        help_text="Type de scraper utilisé"
    )
    title = models.CharField(
        max_length=500,
        help_text="Titre extrait de la page ou nom de l'entreprise",
        blank=True
    )
    summary = models.TextField(
        blank=True,
        help_text="Résumé ou description extraite"
    )
    raw_data = models.JSONField(
        default=dict,
        blank=True,
        help_text="Données brutes JSON de l'API (pour debug et audit)"
    )
    processed_data = models.JSONField(
        default=dict,
        blank=True,
        help_text="Données traitées et mappées"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text="Statut du scraping"
    )
    extracted_at = models.DateTimeField(
        auto_now_add=True,
        help_text="Date et heure d'extraction"
    )

    # Champs spécifiques pour Casablanca Stock Exchange
    company_symbol = models.CharField(
        max_length=50,
        blank=True,
        help_text="Symbole de l'entreprise (ex: BOA, AWB)"
    )
    company_id = models.CharField(
        max_length=100,
        blank=True,
        help_text="ID de l'entreprise depuis l'API"
    )
    close_price = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        null=True,
        blank=True,
        help_text="Prix de clôture extrait"
    )
    volume = models.BigIntegerField(
        null=True,
        blank=True,
        help_text="Volume extrait"
    )

    # Relations
    target = models.ForeignKey(
        ScraperTarget,
        on_delete=models.CASCADE,
        related_name='scraped_items',
        null=True,
        blank=True
    )

    # Champs pour le suivi des erreurs
    error_message = models.TextField(
        blank=True,
        help_text="Message d'erreur en cas d'échec"
    )
    retry_count = models.IntegerField(
        default=0,
        help_text="Nombre de tentatives de retry"
    )

    # Métadonnées d'audit
    execution_id = models.CharField(
        max_length=255,
        blank=True,
        help_text="ID de l'exécution Celery pour traçabilité"
    )
    processing_duration = models.FloatField(
        null=True,
        blank=True,
        help_text="Durée de traitement en secondes"
    )
    
    class Meta:
        db_table = 'scraped_items'
        verbose_name = 'Scraped Item'
        verbose_name_plural = 'Scraped Items'
        ordering = ['-extracted_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['extracted_at']),
            models.Index(fields=['source_url']),
            models.Index(fields=['scraper_type']),
            models.Index(fields=['company_symbol']),
            models.Index(fields=['execution_id']),
            models.Index(fields=['extracted_at', 'status']),
        ]

    def __str__(self):
        if self.company_symbol:
            return f"{self.company_symbol} - {self.extracted_at.strftime('%Y-%m-%d %H:%M')} - {self.status}"
        return f"{self.title[:50]}... - {self.status}"

    def set_raw_data(self, key, value):
        """Ajouter une donnée brute"""
        if not isinstance(self.raw_data, dict):
            self.raw_data = {}
        self.raw_data[key] = value

    def get_raw_data(self, key, default=None):
        """Récupérer une donnée brute"""
        if not isinstance(self.raw_data, dict):
            return default
        return self.raw_data.get(key, default)

    def set_processed_data(self, key, value):
        """Ajouter une donnée traitée"""
        if not isinstance(self.processed_data, dict):
            self.processed_data = {}
        self.processed_data[key] = value

    def get_processed_data(self, key, default=None):
        """Récupérer une donnée traitée"""
        if not isinstance(self.processed_data, dict):
            return default
        return self.processed_data.get(key, default)

    def mark_success(self, processing_duration=None):
        """Marquer l'item comme traité avec succès"""
        self.status = 'success'
        if processing_duration:
            self.processing_duration = processing_duration
        self.save(update_fields=['status', 'processing_duration'])

    def mark_failed(self, error_message, retry_count=None):
        """Marquer l'item comme échoué"""
        self.status = 'failed'
        self.error_message = error_message
        if retry_count is not None:
            self.retry_count = retry_count
        self.save(update_fields=['status', 'error_message', 'retry_count'])


class ScraperExecution(models.Model):
    """
    Modèle pour suivre les exécutions du scraper
    """
    STATUS_CHOICES = [
        ('running', 'En cours'),
        ('completed', 'Terminé'),
        ('failed', 'Échec'),
        ('cancelled', 'Annulé'),
    ]

    task_id = models.CharField(
        max_length=255,
        unique=True,
        help_text="ID de la tâche Celery"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='running'
    )
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Statistiques
    total_urls = models.IntegerField(default=0)
    successful_scrapes = models.IntegerField(default=0)
    failed_scrapes = models.IntegerField(default=0)
    
    # Logs et erreurs
    logs = models.TextField(blank=True)
    error_details = models.TextField(blank=True)
    
    class Meta:
        db_table = 'scraper_executions'
        verbose_name = 'Scraper Execution'
        verbose_name_plural = 'Scraper Executions'
        ordering = ['-started_at']

    def __str__(self):
        return f"Execution {self.task_id} - {self.status}"

    def add_log(self, message):
        """Ajouter un message de log"""
        timestamp = timezone.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.logs = (self.logs or "") + log_entry
        self.save(update_fields=['logs'])

    def complete(self, success=True, error_message=None):
        """Marquer l'exécution comme terminée"""
        self.completed_at = timezone.now()
        if success:
            self.status = 'completed'
        else:
            self.status = 'failed'
            if error_message:
                self.error_details = error_message
        self.save()

    @property
    def duration(self):
        """Durée d'exécution en secondes"""
        if self.completed_at and self.started_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None

    @property
    def success_rate(self):
        """Taux de succès en pourcentage"""
        if self.total_urls > 0:
            return (self.successful_scrapes / self.total_urls) * 100
        return 0


class CasablancaApiExecution(models.Model):
    """
    Modèle spécialisé pour suivre les exécutions du scraper Casablanca Stock Exchange
    """
    STATUS_CHOICES = [
        ('running', 'En cours'),
        ('completed', 'Terminé'),
        ('failed', 'Échec'),
        ('cancelled', 'Annulé'),
        ('partial', 'Partiellement réussi'),
    ]

    # Identification
    task_id = models.CharField(
        max_length=255,
        unique=True,
        help_text="ID de la tâche Celery"
    )
    execution_date = models.DateField(
        default=timezone.now,
        help_text="Date d'exécution du scraping"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='running'
    )
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Statistiques API
    total_companies_api = models.IntegerField(
        default=0,
        help_text="Nombre total d'entreprises retournées par l'API"
    )
    companies_processed = models.IntegerField(
        default=0,
        help_text="Nombre d'entreprises traitées"
    )
    companies_saved = models.IntegerField(
        default=0,
        help_text="Nombre d'entreprises sauvegardées avec succès"
    )
    companies_failed = models.IntegerField(
        default=0,
        help_text="Nombre d'entreprises échouées"
    )
    companies_skipped = models.IntegerField(
        default=0,
        help_text="Nombre d'entreprises ignorées"
    )

    # Statistiques API par page
    page1_companies = models.IntegerField(
        default=0,
        help_text="Nombre d'entreprises de la page 1"
    )
    page2_companies = models.IntegerField(
        default=0,
        help_text="Nombre d'entreprises de la page 2"
    )
    page1_success = models.BooleanField(
        default=False,
        help_text="Page 1 récupérée avec succès"
    )
    page2_success = models.BooleanField(
        default=False,
        help_text="Page 2 récupérée avec succès"
    )

    # Données financières agrégées
    total_volume_processed = models.BigIntegerField(
        default=0,
        help_text="Volume total traité"
    )
    average_price = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        null=True,
        blank=True,
        help_text="Prix moyen des entreprises traitées"
    )

    # Logs et erreurs
    logs = models.TextField(blank=True)
    error_details = models.TextField(blank=True)
    api_response_times = models.JSONField(
        default=dict,
        blank=True,
        help_text="Temps de réponse des API calls"
    )

    # Configuration utilisée
    api_urls_used = models.JSONField(
        default=list,
        blank=True,
        help_text="URLs API utilisées"
    )
    user_agent = models.CharField(
        max_length=255,
        blank=True,
        help_text="User-Agent utilisé"
    )

    class Meta:
        db_table = 'casablanca_api_executions'
        verbose_name = 'Casablanca API Execution'
        verbose_name_plural = 'Casablanca API Executions'
        ordering = ['-started_at']
        indexes = [
            models.Index(fields=['execution_date']),
            models.Index(fields=['status']),
            models.Index(fields=['started_at']),
            models.Index(fields=['-started_at', 'status']),
        ]

    def __str__(self):
        return f"Casablanca API {self.execution_date} - {self.status} ({self.companies_saved}/{self.total_companies_api})"

    def add_log(self, message):
        """Ajouter un message de log"""
        timestamp = timezone.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.logs = (self.logs or "") + log_entry
        self.save(update_fields=['logs'])

    def complete(self, success=True, error_message=None):
        """Marquer l'exécution comme terminée"""
        self.completed_at = timezone.now()
        if success:
            if self.companies_failed > 0 and self.companies_saved > 0:
                self.status = 'partial'
            else:
                self.status = 'completed'
        else:
            self.status = 'failed'
            if error_message:
                self.error_details = error_message
        self.save()

    @property
    def duration(self):
        """Durée d'exécution en secondes"""
        if self.completed_at and self.started_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None

    @property
    def success_rate(self):
        """Taux de succès en pourcentage"""
        if self.total_companies_api > 0:
            return (self.companies_saved / self.total_companies_api) * 100
        return 0

    @property
    def api_success_rate(self):
        """Taux de succès des appels API"""
        total_pages = 2  # Page 1 et Page 2
        successful_pages = sum([self.page1_success, self.page2_success])
        return (successful_pages / total_pages) * 100 if total_pages > 0 else 0

    def update_stats(self, **kwargs):
        """Mettre à jour les statistiques"""
        for field, value in kwargs.items():
            if hasattr(self, field):
                setattr(self, field, value)
        self.save()
