"""
Utilitaires pour le système de récupération quotidienne
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import pandas as pd

def setup_custom_logger(name: str, log_file: str = None, level: int = logging.INFO) -> logging.Logger:
    """
    Configure un logger personnalisé
    
    Args:
        name: Nom du logger
        log_file: Fichier de log (optionnel)
        level: Niveau de log
    
    Returns:
        Logger configuré
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Éviter la duplication des handlers
    if logger.handlers:
        return logger
    
    # Format
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Handler console
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # Handler fichier si spécifié
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def validate_date_string(date_str: str) -> bool:
    """
    Valide une chaîne de date au format YYYY-MM-DD
    
    Args:
        date_str: Chaîne de date à valider
    
    Returns:
        bool: True si valide
    """
    try:
        datetime.strptime(date_str, '%Y-%m-%d')
        return True
    except ValueError:
        return False

def get_business_days(start_date: str, end_date: str) -> List[str]:
    """
    Retourne la liste des jours ouvrables entre deux dates
    
    Args:
        start_date: Date de début (YYYY-MM-DD)
        end_date: Date de fin (YYYY-MM-DD)
    
    Returns:
        Liste des dates au format YYYY-MM-DD
    """
    try:
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        
        business_days = []
        current_date = start
        
        while current_date <= end:
            # Exclure samedi (5) et dimanche (6)
            if current_date.weekday() < 5:
                business_days.append(current_date.strftime('%Y-%m-%d'))
            current_date += timedelta(days=1)
        
        return business_days
    except ValueError:
        return []

def calculate_statistics(values: List[float]) -> Dict[str, float]:
    """
    Calcule des statistiques de base sur une liste de valeurs
    
    Args:
        values: Liste de valeurs numériques
    
    Returns:
        Dict avec les statistiques
    """
    if not values:
        return {
            'count': 0,
            'mean': 0.0,
            'median': 0.0,
            'min': 0.0,
            'max': 0.0,
            'std': 0.0
        }
    
    df = pd.Series(values)
    
    return {
        'count': len(values),
        'mean': float(df.mean()),
        'median': float(df.median()),
        'min': float(df.min()),
        'max': float(df.max()),
        'std': float(df.std()) if len(values) > 1 else 0.0
    }

def format_currency(amount: float, currency: str = 'MAD') -> str:
    """
    Formate une valeur monétaire
    
    Args:
        amount: Montant
        currency: Devise
    
    Returns:
        Chaîne formatée
    """
    try:
        return f"{amount:,.2f} {currency}"
    except:
        return f"0.00 {currency}"

def format_percentage(value: float, decimals: int = 2) -> str:
    """
    Formate un pourcentage
    
    Args:
        value: Valeur en pourcentage
        decimals: Nombre de décimales
    
    Returns:
        Chaîne formatée
    """
    try:
        return f"{value:.{decimals}f}%"
    except:
        return "0.00%"

def format_large_number(number: int) -> str:
    """
    Formate un grand nombre avec des séparateurs
    
    Args:
        number: Nombre à formater
    
    Returns:
        Chaîne formatée
    """
    try:
        if number >= 1_000_000:
            return f"{number / 1_000_000:.1f}M"
        elif number >= 1_000:
            return f"{number / 1_000:.1f}K"
        else:
            return str(number)
    except:
        return "0"

def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    Division sécurisée qui évite la division par zéro
    
    Args:
        numerator: Numérateur
        denominator: Dénominateur
        default: Valeur par défaut si division impossible
    
    Returns:
        Résultat de la division ou valeur par défaut
    """
    try:
        if denominator == 0:
            return default
        return numerator / denominator
    except:
        return default

def clean_company_name(name: str) -> str:
    """
    Nettoie le nom d'une entreprise pour l'utilisation en nom de fichier
    
    Args:
        name: Nom de l'entreprise
    
    Returns:
        Nom nettoyé
    """
    if not name:
        return "Unknown"
    
    # Remplacer les caractères problématiques
    cleaned = name.replace('/', '_').replace('\\', '_').replace(':', '_')
    cleaned = cleaned.replace('<', '_').replace('>', '_').replace('|', '_')
    cleaned = cleaned.replace('?', '_').replace('*', '_').replace('"', '_')
    
    # Supprimer les espaces multiples et les espaces en début/fin
    cleaned = ' '.join(cleaned.split())
    
    # Limiter la longueur
    if len(cleaned) > 50:
        cleaned = cleaned[:50].strip()
    
    return cleaned

def load_json_file(file_path: str) -> Optional[Dict]:
    """
    Charge un fichier JSON en toute sécurité
    
    Args:
        file_path: Chemin vers le fichier JSON
    
    Returns:
        Dictionnaire ou None si erreur
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception:
        return None

def save_json_file(data: Dict, file_path: str) -> bool:
    """
    Sauvegarde un dictionnaire en JSON
    
    Args:
        data: Données à sauvegarder
        file_path: Chemin de destination
    
    Returns:
        bool: Succès de la sauvegarde
    """
    try:
        # Créer le dossier parent si nécessaire
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception:
        return False

def get_file_size_mb(file_path: str) -> float:
    """
    Retourne la taille d'un fichier en MB
    
    Args:
        file_path: Chemin vers le fichier
    
    Returns:
        Taille en MB
    """
    try:
        size_bytes = Path(file_path).stat().st_size
        return size_bytes / (1024 * 1024)
    except:
        return 0.0

def create_backup_filename(original_path: str) -> str:
    """
    Crée un nom de fichier de sauvegarde avec timestamp
    
    Args:
        original_path: Chemin original
    
    Returns:
        Chemin de sauvegarde
    """
    path = Path(original_path)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_name = f"{path.stem}_backup_{timestamp}{path.suffix}"
    return str(path.parent / backup_name)

def retry_operation(func, max_retries: int = 3, delay: float = 1.0, exceptions: tuple = (Exception,)):
    """
    Décorateur pour retry automatique d'une opération
    
    Args:
        func: Fonction à exécuter
        max_retries: Nombre maximum de tentatives
        delay: Délai entre les tentatives
        exceptions: Types d'exceptions à retry
    
    Returns:
        Résultat de la fonction ou lève la dernière exception
    """
    import time
    
    def wrapper(*args, **kwargs):
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except exceptions as e:
                last_exception = e
                if attempt < max_retries - 1:
                    time.sleep(delay)
                    continue
                else:
                    raise last_exception
        
        return None
    
    return wrapper

def validate_price_data(open_price: float, high_price: float, 
                       low_price: float, close_price: float) -> bool:
    """
    Valide la cohérence des données de prix OHLC
    
    Args:
        open_price: Prix d'ouverture
        high_price: Prix le plus haut
        low_price: Prix le plus bas
        close_price: Prix de fermeture
    
    Returns:
        bool: True si les données sont cohérentes
    """
    try:
        # Vérifier que tous les prix sont positifs
        if any(price <= 0 for price in [open_price, high_price, low_price, close_price]):
            return False
        
        # Vérifier que high >= low
        if high_price < low_price:
            return False
        
        # Vérifier que open et close sont dans la plage high-low
        if not (low_price <= open_price <= high_price):
            return False
        
        if not (low_price <= close_price <= high_price):
            return False
        
        return True
        
    except:
        return False

def calculate_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    Calcule des indicateurs techniques simples
    
    Args:
        df: DataFrame avec les colonnes OHLCV
    
    Returns:
        DataFrame enrichi avec les indicateurs
    """
    try:
        if df.empty or 'close_price' not in df.columns:
            return df
        
        # Simple Moving Average (SMA) sur 5 périodes
        if len(df) >= 5:
            df['sma_5'] = df['close_price'].rolling(window=5).mean()
        
        # Relative Strength Index (RSI) simplifié
        if len(df) >= 14:
            delta = df['close_price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands simplifiées
        if len(df) >= 20:
            sma_20 = df['close_price'].rolling(window=20).mean()
            std_20 = df['close_price'].rolling(window=20).std()
            df['bb_upper'] = sma_20 + (std_20 * 2)
            df['bb_lower'] = sma_20 - (std_20 * 2)
        
        return df
        
    except Exception:
        return df
