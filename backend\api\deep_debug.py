#!/usr/bin/env python3
"""
Advanced pickle file analysis to find the corruption point
"""

import pickle
import pickletools
from pathlib import Path

def analyze_pickle_structure(file_path):
    """Analyze the internal structure of a pickle file"""
    print(f"\n🔬 Deep analysis of: {file_path}")
    
    try:
        with open(file_path, 'rb') as f:
            # Read the entire file
            data = f.read()
            print(f"   📊 Total file size: {len(data)} bytes")
            
            # Check if the file starts correctly
            if data[:2] == b'\x80\x04':
                print("   ✅ Valid pickle protocol 4 header")
            else:
                print(f"   ❌ Invalid pickle header: {data[:10]}")
                return
            
            # Try to analyze with pickletools
            print("   🔍 Analyzing pickle opcodes...")
            try:
                # Create a temporary file to analyze
                temp_path = "temp_analysis.pkl"
                with open(temp_path, 'wb') as temp_f:
                    temp_f.write(data[:1000])  # First 1000 bytes
                
                with open(temp_path, 'rb') as temp_f:
                    pickletools.dis(temp_f)
                    
                import os
                os.remove(temp_path)
                
            except Exception as e:
                print(f"   ⚠️ Pickletools analysis failed: {e}")
            
            # Try to find where the corruption occurs
            print("   🔍 Searching for corruption point...")
            
            # Try loading progressively smaller chunks
            chunk_sizes = [len(data), len(data)//2, len(data)//4, len(data)//8, len(data)//16]
            
            for chunk_size in chunk_sizes:
                try:
                    chunk = data[:chunk_size]
                    # Try to create a temporary valid pickle
                    temp_path = "temp_chunk.pkl"
                    with open(temp_path, 'wb') as temp_f:
                        temp_f.write(chunk)
                    
                    with open(temp_path, 'rb') as temp_f:
                        pickle.load(temp_f)
                    
                    print(f"   ✅ Successfully loaded chunk of size: {chunk_size}")
                    import os
                    os.remove(temp_path)
                    break
                    
                except Exception as e:
                    print(f"   ❌ Chunk size {chunk_size} failed: {str(e)[:50]}...")
                    try:
                        import os
                        os.remove(temp_path)
                    except:
                        pass
                    continue
            
            # Look for specific patterns that might indicate the model type
            print("   🔍 Checking for model signatures...")
            data_str = str(data[:2000])  # First 2000 bytes as string
            
            if 'statsmodels' in data_str:
                print("   📋 Found statsmodels signature")
            if 'ARIMA' in data_str:
                print("   📋 Found ARIMA signature")
            if 'sklearn' in data_str:
                print("   📋 Found sklearn signature")
            if 'pandas' in data_str:
                print("   📋 Found pandas signature")
                
    except Exception as e:
        print(f"   💥 Analysis failed: {e}")

def try_alternative_loading_methods(file_path):
    """Try various alternative methods to load the model"""
    print(f"\n🔧 Trying alternative loading methods for: {file_path}")
    
    methods = [
        ("Standard pickle", lambda f: pickle.load(f)),
        ("Pickle with latin1", lambda f: pickle.load(f, encoding='latin1')),
        ("Pickle with bytes", lambda f: pickle.load(f, encoding='bytes')),
    ]
    
    for method_name, method_func in methods:
        try:
            with open(file_path, 'rb') as f:
                model = method_func(f)
            print(f"   ✅ {method_name}: SUCCESS")
            print(f"      Model type: {type(model)}")
            return model
        except Exception as e:
            print(f"   ❌ {method_name}: {str(e)[:60]}...")
    
    return None

def main():
    print("🔬 Advanced ARIMA Model Analysis")
    print("=" * 50)
    
    models_dir = Path("../models/Arima")
    model_files = list(models_dir.glob("*_arima_model.pkl"))[:1]  # Test just one file
    
    for model_file in model_files:
        analyze_pickle_structure(model_file)
        model = try_alternative_loading_methods(model_file)
        
        if model:
            print(f"\n🎉 Successfully loaded model from {model_file.name}")
            break
        else:
            print(f"\n❌ Failed to load model from {model_file.name}")

if __name__ == "__main__":
    main()
