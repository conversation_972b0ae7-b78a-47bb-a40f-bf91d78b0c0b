from rest_framework import generics, status, filters
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Sum, Avg, Max, Min
from datetime import datetime, timedelta, date
from django.shortcuts import get_object_or_404
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from .csv_models import (
    FinancialInstrument, InstrumentPrice, MASIIndex, 
    MASIIndexValue, InstrumentPrediction
)
from .csv_serializers import (
    FinancialInstrumentSerializer, InstrumentPriceSerializer,
    MASIIndexSerializer, MASIIndexValueSerializer,
    InstrumentPredictionSerializer, SectorAnalysisSerializer,
    MarketOverviewSerializer, InstrumentDetailSerializer,
    InstrumentPriceRangeSerializer
)


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 200


# =========================
# INSTRUMENTS FINANCIERS
# =========================

class FinancialInstrumentListView(generics.ListAPIView):
    """Liste tous les instruments financiers disponibles"""
    queryset = FinancialInstrument.objects.filter(is_active=True)
    serializer_class = FinancialInstrumentSerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['sector', 'has_predictions']
    search_fields = ['symbol', 'label', 'sector']
    ordering_fields = ['symbol', 'label', 'sector', 'market_cap']
    ordering = ['symbol']


class FinancialInstrumentDetailView(generics.RetrieveAPIView):
    """Détails complets d'un instrument financier"""
    queryset = FinancialInstrument.objects.filter(is_active=True)
    serializer_class = InstrumentDetailSerializer
    lookup_field = 'symbol'


class InstrumentPriceListView(generics.ListAPIView):
    """Prix historiques d'un instrument"""
    serializer_class = InstrumentPriceSerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['date']
    ordering_fields = ['date']
    ordering = ['-date']

    def get_queryset(self):
        symbol = self.kwargs.get('symbol')
        queryset = InstrumentPrice.objects.filter(instrument__symbol=symbol)
        
        # Filtres de date
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        
        if date_from:
            queryset = queryset.filter(date__gte=date_from)
        if date_to:
            queryset = queryset.filter(date__lte=date_to)
            
        return queryset


@api_view(['GET'])
def get_instrument_chart_data(request, symbol):
    """Données pour graphiques d'un instrument"""
    try:
        instrument = get_object_or_404(FinancialInstrument, symbol=symbol)
        
        # Paramètres de période
        period = request.GET.get('period', '1M')  # 1M, 3M, 6M, 1Y, ALL
        
        # Calculer la date de début selon la période
        end_date = date.today()
        if period == '1M':
            start_date = end_date - timedelta(days=30)
        elif period == '3M':
            start_date = end_date - timedelta(days=90)
        elif period == '6M':
            start_date = end_date - timedelta(days=180)
        elif period == '1Y':
            start_date = end_date - timedelta(days=365)
        else:  # ALL
            start_date = None
        
        # Récupérer les prix
        queryset = instrument.prices.all()
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        
        prices = queryset.order_by('date')
        
        # Préparer les données pour le graphique
        chart_data = {
            'symbol': symbol,
            'label': instrument.label,
            'period': period,
            'data': [
                {
                    'date': price.date.isoformat(),
                    'open': float(price.open_price),
                    'high': float(price.high_price),
                    'low': float(price.low_price),
                    'close': float(price.close_price),
                    'volume': price.volume
                }
                for price in prices
            ]
        }
        
        return Response(chart_data)
        
    except FinancialInstrument.DoesNotExist:
        return Response({'error': 'Instrument not found'}, status=404)


@api_view(['GET'])
def get_instruments_by_sector(request):
    """Instruments groupés par secteur"""
    sectors = FinancialInstrument.objects.filter(is_active=True).values_list('sector', flat=True).distinct()
    
    sector_data = {}
    for sector in sectors:
        instruments = FinancialInstrument.objects.filter(sector=sector, is_active=True)
        sector_data[sector] = FinancialInstrumentSerializer(instruments, many=True).data
    
    return Response({
        'sectors_count': len(sectors),
        'sectors': sector_data
    })


# =========================
# INDICES MASI
# =========================

class MASIIndexListView(generics.ListAPIView):
    """Liste tous les indices MASI"""
    queryset = MASIIndex.objects.filter(is_active=True)
    serializer_class = MASIIndexSerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['type']
    search_fields = ['symbol', 'label', 'description']
    ordering_fields = ['symbol', 'label', 'type']
    ordering = ['symbol']


class MASIIndexDetailView(generics.RetrieveAPIView):
    """Détails d'un indice MASI"""
    queryset = MASIIndex.objects.filter(is_active=True)
    serializer_class = MASIIndexSerializer
    lookup_field = 'symbol'


class MASIIndexValueListView(generics.ListAPIView):
    """Valeurs historiques d'un indice MASI"""
    serializer_class = MASIIndexValueSerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['date']
    ordering_fields = ['date']
    ordering = ['-date']

    def get_queryset(self):
        symbol = self.kwargs.get('symbol')
        queryset = MASIIndexValue.objects.filter(index__symbol=symbol)
        
        # Filtres de date
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        
        if date_from:
            queryset = queryset.filter(date__gte=date_from)
        if date_to:
            queryset = queryset.filter(date__lte=date_to)
            
        return queryset


@api_view(['GET'])
def get_masi_chart_data(request, symbol):
    """Données pour graphiques d'un indice MASI"""
    try:
        index = get_object_or_404(MASIIndex, symbol=symbol)
        
        # Paramètres de période
        period = request.GET.get('period', '1M')
        
        # Calculer la date de début
        end_date = date.today()
        if period == '1M':
            start_date = end_date - timedelta(days=30)
        elif period == '3M':
            start_date = end_date - timedelta(days=90)
        elif period == '6M':
            start_date = end_date - timedelta(days=180)
        elif period == '1Y':
            start_date = end_date - timedelta(days=365)
        else:  # ALL
            start_date = None
        
        # Récupérer les valeurs
        queryset = index.values.all()
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        
        values = queryset.order_by('date')
        
        chart_data = {
            'symbol': symbol,
            'label': index.label,
            'type': index.type,
            'period': period,
            'data': [
                {
                    'date': value.date.isoformat(),
                    'value': float(value.value),
                    'daily_change': float(value.daily_change) if value.daily_change else None,
                    'daily_change_pct': float(value.daily_change_pct) if value.daily_change_pct else None
                }
                for value in values
            ]
        }
        
        return Response(chart_data)
        
    except MASIIndex.DoesNotExist:
        return Response({'error': 'Index not found'}, status=404)


# =========================
# PRÉDICTIONS
# =========================

class InstrumentPredictionListView(generics.ListAPIView):
    """Prédictions ARIMA pour un instrument"""
    serializer_class = InstrumentPredictionSerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    ordering_fields = ['date']
    ordering = ['date']

    def get_queryset(self):
        symbol = self.kwargs.get('symbol')
        return InstrumentPrediction.objects.filter(instrument__symbol=symbol)


@api_view(['GET'])
def get_prediction_chart_data(request, symbol):
    """Données combinées prix historiques + prédictions"""
    try:
        instrument = get_object_or_404(FinancialInstrument, symbol=symbol)
        
        if not instrument.has_predictions:
            return Response({'error': 'No predictions available for this instrument'}, status=404)
        
        # Récupérer les 30 derniers prix
        historical_prices = instrument.prices.order_by('-date')[:30]
        historical_data = [
            {
                'date': price.date.isoformat(),
                'actual_price': float(price.close_price),
                'type': 'historical'
            }
            for price in reversed(historical_prices)
        ]
        
        # Récupérer les prédictions
        predictions = instrument.predictions.order_by('date')[:15]
        prediction_data = [
            {
                'date': pred.date.isoformat(),
                'predicted_price': float(pred.predicted_price),
                'lower_ci': float(pred.lower_ci) if pred.lower_ci else None,
                'upper_ci': float(pred.upper_ci) if pred.upper_ci else None,
                'type': 'prediction'
            }
            for pred in predictions
        ]
        
        return Response({
            'symbol': symbol,
            'label': instrument.label,
            'historical_data': historical_data,
            'predictions': prediction_data,
            'total_points': len(historical_data) + len(prediction_data)
        })
        
    except FinancialInstrument.DoesNotExist:
        return Response({'error': 'Instrument not found'}, status=404)


# =========================
# ANALYSES ET VUES D'ENSEMBLE
# =========================

@api_view(['GET'])
def get_market_overview(request):
    """Vue d'ensemble du marché"""
    
    # Statistiques générales
    total_instruments = FinancialInstrument.objects.filter(is_active=True).count()
    total_indices = MASIIndex.objects.filter(is_active=True).count()
    
    # Instruments avec prédictions
    instruments_with_predictions = FinancialInstrument.objects.filter(
        is_active=True, predictions_file__isnull=False
    ).count()
    
    # Market cap total (approximatif)
    total_market_cap = FinancialInstrument.objects.filter(
        is_active=True, market_cap__isnull=False
    ).aggregate(total=Sum('market_cap'))['total'] or 0
    
    # Top gainers et losers (dernière semaine)
    week_ago = date.today() - timedelta(days=7)
    
    # Calcul des performances sur 7 jours
    instruments_performance = []
    for instrument in FinancialInstrument.objects.filter(is_active=True)[:20]:  # Limiter pour performance
        recent_prices = instrument.prices.filter(date__gte=week_ago).order_by('-date')[:2]
        if len(recent_prices) >= 2:
            current = float(recent_prices[0].close_price)
            previous = float(recent_prices[1].close_price)
            change_pct = ((current - previous) / previous) * 100
            instruments_performance.append({
                'symbol': instrument.symbol,
                'label': instrument.label,
                'current_price': current,
                'change_pct': round(change_pct, 2)
            })
    
    # Trier pour top gainers et losers
    instruments_performance.sort(key=lambda x: x['change_pct'], reverse=True)
    top_gainers = instruments_performance[:5]
    top_losers = instruments_performance[-5:]
    
    # Analyse sectorielle
    sectors_stats = FinancialInstrument.objects.filter(is_active=True).values('sector').annotate(
        count=Count('symbol'),
        avg_market_cap=Avg('market_cap')
    ).order_by('-count')
    
    return Response({
        'market_statistics': {
            'total_instruments': total_instruments,
            'instruments_with_predictions': instruments_with_predictions,
            'total_indices': total_indices,
            'total_market_cap': float(total_market_cap),
            'data_coverage': '2022-present'
        },
        'performance': {
            'top_gainers': top_gainers,
            'top_losers': top_losers
        },
        'sectors': list(sectors_stats),
        'last_updated': date.today().isoformat()
    })


@api_view(['GET'])
def get_sector_analysis(request):
    """Analyse détaillée par secteur"""
    sector = request.GET.get('sector')
    
    if sector:
        # Analyse d'un secteur spécifique
        instruments = FinancialInstrument.objects.filter(sector=sector, is_active=True)
        if not instruments.exists():
            return Response({'error': 'Sector not found'}, status=404)
        
        sector_stats = {
            'sector': sector,
            'instruments_count': instruments.count(),
            'instruments': FinancialInstrumentSerializer(instruments, many=True).data
        }
        
        return Response(sector_stats)
    
    else:
        # Analyse de tous les secteurs
        sectors = FinancialInstrument.objects.filter(is_active=True).values('sector').annotate(
            instruments_count=Count('symbol'),
            total_market_cap=Sum('market_cap')
        ).order_by('-instruments_count')
        
        return Response({
            'total_sectors': len(sectors),
            'sectors': list(sectors)
        })


@api_view(['GET'])
def get_available_symbols(request):
    """Liste de tous les symboles disponibles"""
    
    # Instruments financiers
    instruments = FinancialInstrument.objects.filter(is_active=True).values(
        'symbol', 'label', 'sector'
    ).order_by('symbol')
    
    # Indices MASI
    indices = MASIIndex.objects.filter(is_active=True).values(
        'symbol', 'label', 'type'
    ).order_by('symbol')
    
    return Response({
        'instruments': {
            'total_count': len(instruments),
            'data': list(instruments)
        },
        'indices': {
            'total_count': len(indices), 
            'data': list(indices)
        },
        'combined_total': len(instruments) + len(indices)
    })


@api_view(['GET'])
def health_check_csv_data(request):
    """Vérification de l'état des données CSV"""
    
    return Response({
        'status': 'healthy',
        'data_sources': {
            'instruments_data': {
                'total_instruments': FinancialInstrument.objects.count(),
                'active_instruments': FinancialInstrument.objects.filter(is_active=True).count(),
                'latest_price_date': InstrumentPrice.objects.order_by('-date').first().date if InstrumentPrice.objects.exists() else None
            },
            'masi_indices': {
                'total_indices': MASIIndex.objects.count(),
                'active_indices': MASIIndex.objects.filter(is_active=True).count(),
                'latest_value_date': MASIIndexValue.objects.order_by('-date').first().date if MASIIndexValue.objects.exists() else None
            },
            'predictions': {
                'instruments_with_predictions': FinancialInstrument.objects.filter(predictions_file__isnull=False).count(),
                'total_predictions': InstrumentPrediction.objects.count()
            }
        },
        'timestamp': datetime.now().isoformat()
    })


@api_view(['GET'])
def get_instrument_price_variations(request, symbol):
    """API spécialisée pour récupérer les variations de prix d'un instrument depuis 2022"""
    try:
        instrument = get_object_or_404(FinancialInstrument, symbol=symbol)
        
        # Paramètres de période
        period = request.GET.get('period', 'ALL')  # 1M, 3M, 6M, 1Y, ALL, SINCE_2022
        
        # Calculer la date de début selon la période
        end_date = date.today()
        if period == '1M':
            start_date = end_date - timedelta(days=30)
        elif period == '3M':
            start_date = end_date - timedelta(days=90)
        elif period == '6M':
            start_date = end_date - timedelta(days=180)
        elif period == '1Y':
            start_date = end_date - timedelta(days=365)
        elif period == 'SINCE_2022':
            start_date = date(2022, 1, 1)
        else:  # ALL
            start_date = None
        
        # Récupérer les prix
        queryset = instrument.prices.all()
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        
        prices = queryset.order_by('date')
        
        if not prices.exists():
            return Response({
                'error': 'No price data available for this instrument',
                'symbol': symbol,
                'period': period
            }, status=404)
        
        # Calculer les variations
        price_data = []
        previous_close = None
        first_price = prices.first().close_price
        
        for price in prices:
            # Calcul de variation journalière
            daily_change = 0
            daily_change_pct = 0
            
            if previous_close:
                daily_change = float(price.close_price - previous_close)
                daily_change_pct = (daily_change / float(previous_close)) * 100
            
            # Calcul de variation depuis le début de la période
            total_change = float(price.close_price - first_price)
            total_change_pct = (total_change / float(first_price)) * 100
            
            price_data.append({
                'date': price.date.isoformat(),
                'open': float(price.open_price),
                'high': float(price.high_price),
                'low': float(price.low_price),
                'close': float(price.close_price),
                'volume': price.volume,
                'daily_change': round(daily_change, 4),
                'daily_change_pct': round(daily_change_pct, 2),
                'total_change': round(total_change, 4),
                'total_change_pct': round(total_change_pct, 2)
            })
            
            previous_close = price.close_price
        
        # Calculs de résumé
        last_price = prices.last()
        summary = {
            'symbol': symbol,
            'label': instrument.label,
            'sector': instrument.sector,
            'period': period,
            'start_date': prices.first().date.isoformat(),
            'end_date': last_price.date.isoformat(),
            'first_price': float(first_price),
            'last_price': float(last_price.close_price),
            'total_change': float(last_price.close_price - first_price),
            'total_change_pct': round(((float(last_price.close_price - first_price) / float(first_price)) * 100), 2),
            'highest_price': float(prices.aggregate(Max('high_price'))['high_price__max']),
            'lowest_price': float(prices.aggregate(Min('low_price'))['low_price__min']),
            'average_price': round(float(prices.aggregate(Avg('close_price'))['close_price__avg']), 2),
            'total_volume': prices.aggregate(Sum('volume'))['volume__sum'] or 0,
            'data_points': len(price_data)
        }
        
        response_data = {
            'summary': summary,
            'variations': price_data
        }
        
        return Response(response_data)
        
    except FinancialInstrument.DoesNotExist:
        return Response({'error': 'Instrument not found'}, status=404)
    except Exception as e:
        return Response({'error': str(e)}, status=500)


@csrf_exempt
@api_view(['GET', 'POST'])
def get_instrument_data_form(request):
    """
    API POST pour récupérer les données d'un instrument via formulaire
    
    Paramètres du formulaire :
    - symbol: Symbole de l'instrument (obligatoire)
    - period: 1M, 3M, 6M, 1Y, 3Y, ALL ou CUSTOM (optionnel, défaut: ALL)
    - start_date: Date de début au format YYYY-MM-DD (obligatoire si period=CUSTOM)
    - end_date: Date de fin au format YYYY-MM-DD (optionnel, défaut: aujourd'hui)
    
    Contraintes :
    - Date de début minimum: 15-08-2022
    - Date de fin maximum: aujourd'hui
    """
    from .csv_serializers import PriceDataRequestSerializer
    import pandas as pd
    import os
    
    # Si c'est une requête GET, retourner la documentation
    if request.method == 'GET':
        return Response({
            'message': 'API POST pour récupérer les données d\'instruments',
            'endpoint': '/api/v1/csv-data/instruments/data/',
            'method': 'POST',
            'parameters': {
                'symbol': 'Symbole de l\'instrument (obligatoire)',
                'period': 'Période: 1M, 3M, 6M, 1Y, 3Y, ALL ou CUSTOM',
                'start_date': 'Date de début (format YYYY-MM-DD, si period=CUSTOM)',
                'end_date': 'Date de fin (format YYYY-MM-DD, si period=CUSTOM)'
            },
            'constraints': {
                'min_start_date': '2022-08-15',
                'max_end_date': 'Aujourd\'hui'
            },
            'available_symbols': [
                'ATTIJARIWAFA_BANK', 'BCP', 'COSUMAR', 'BANK_OF_AFRICA',
                'CIH', 'BMCI', 'CFG_BANK', 'AFRIQUIA_GAZ', 'CIMENTS_DU_MAROC'
            ]
        })
    
    # Validation des données du formulaire
    serializer = PriceDataRequestSerializer(data=request.data)
    if not serializer.is_valid():
        return Response({
            'error': 'Données du formulaire invalides',
            'details': serializer.errors
        }, status=400)
    
    validated_data = serializer.validated_data
    symbol = validated_data['symbol']
    period = validated_data['period']
    start_date = validated_data.get('start_date')
    end_date = validated_data.get('end_date', date.today())
    
    try:
        # Récupérer l'instrument
        instrument = get_object_or_404(FinancialInstrument, symbol=symbol, is_active=True)
        
        # Calculer les dates selon la période
        if period != 'CUSTOM':
            end_date = date.today()
            if period == '1M':
                start_date = end_date - timedelta(days=30)
            elif period == '3M':
                start_date = end_date - timedelta(days=90)
            elif period == '6M':
                start_date = end_date - timedelta(days=180)
            elif period == '1Y':
                start_date = end_date - timedelta(days=365)
            elif period == '3Y':
                start_date = end_date - timedelta(days=365*3)
            else:  # ALL
                start_date = date(2022, 8, 15)  # Date minimale
        
        # Vérifier si on a des données dans la base
        queryset = instrument.prices.all()
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)
        
        db_prices = list(queryset.order_by('date'))
        
        # Si pas assez de données en base, lire le fichier CSV
        if len(db_prices) < 10:  # Seuil minimal
            csv_file_path = f"./instruments data/csv/{symbol}_historical_data.csv"
            
            if os.path.exists(csv_file_path):
                try:
                    # Lire le fichier CSV
                    df = pd.read_csv(csv_file_path)
                    
                    # Convertir la colonne Date
                    df['Date'] = pd.to_datetime(df['Date']).dt.date
                    
                    # Filtrer par dates
                    if start_date:
                        df = df[df['Date'] >= start_date]
                    if end_date:
                        df = df[df['Date'] <= end_date]
                    
                    # Trier par date
                    df = df.sort_values('Date')
                    
                    # Préparer les données pour la réponse
                    csv_data = []
                    previous_close = None
                    first_price = None
                    
                    for _, row in df.iterrows():
                        close_price = float(row.get('Close', 0))
                        
                        if first_price is None:
                            first_price = close_price
                        
                        # Calcul de variation journalière
                        daily_change = 0
                        daily_change_pct = 0
                        if previous_close:
                            daily_change = close_price - previous_close
                            daily_change_pct = (daily_change / previous_close) * 100 if previous_close != 0 else 0
                        
                        # Calcul de variation totale
                        total_change = close_price - first_price if first_price else 0
                        total_change_pct = (total_change / first_price) * 100 if first_price and first_price != 0 else 0
                        
                        csv_data.append({
                            'date': row['Date'].isoformat(),
                            'open': float(row.get('Open', 0)),
                            'high': float(row.get('High', 0)),
                            'low': float(row.get('Low', 0)),
                            'close': close_price,
                            'volume': int(row.get('Volume', 0)),
                            'daily_change': round(daily_change, 4),
                            'daily_change_pct': round(daily_change_pct, 2),
                            'total_change': round(total_change, 4),
                            'total_change_pct': round(total_change_pct, 2)
                        })
                        
                        previous_close = close_price
                    
                    # Statistiques de résumé
                    if csv_data:
                        prices = [item['close'] for item in csv_data]
                        volumes = [item['volume'] for item in csv_data]
                        
                        summary = {
                            'symbol': symbol,
                            'label': instrument.label,
                            'sector': instrument.sector,
                            'period': period,
                            'start_date': csv_data[0]['date'] if csv_data else None,
                            'end_date': csv_data[-1]['date'] if csv_data else None,
                            'first_price': csv_data[0]['close'] if csv_data else None,
                            'last_price': csv_data[-1]['close'] if csv_data else None,
                            'total_change': csv_data[-1]['total_change'] if csv_data else 0,
                            'total_change_pct': csv_data[-1]['total_change_pct'] if csv_data else 0,
                            'highest_price': max(prices) if prices else None,
                            'lowest_price': min(prices) if prices else None,
                            'average_price': round(sum(prices) / len(prices), 2) if prices else None,
                            'total_volume': sum(volumes) if volumes else 0,
                            'data_points': len(csv_data),
                            'data_source': 'CSV file'
                        }
                        
                        return Response({
                            'success': True,
                            'summary': summary,
                            'data': csv_data
                        })
                    
                except Exception as csv_error:
                    return Response({
                        'error': f'Erreur lors de la lecture du fichier CSV: {str(csv_error)}'
                    }, status=500)
            
            else:
                return Response({
                    'error': f'Fichier CSV non trouvé pour {symbol}',
                    'file_path': csv_file_path
                }, status=404)
        
        else:
            # Utiliser les données de la base
            price_data = []
            previous_close = None
            first_price = db_prices[0].close_price if db_prices else None
            
            for price in db_prices:
                # Calcul de variation journalière
                daily_change = 0
                daily_change_pct = 0
                if previous_close:
                    daily_change = float(price.close_price - previous_close)
                    daily_change_pct = (daily_change / float(previous_close)) * 100
                
                # Calcul de variation totale
                total_change = float(price.close_price - first_price) if first_price else 0
                total_change_pct = (total_change / float(first_price)) * 100 if first_price else 0
                
                price_data.append({
                    'date': price.date.isoformat(),
                    'open': float(price.open_price),
                    'high': float(price.high_price),
                    'low': float(price.low_price),
                    'close': float(price.close_price),
                    'volume': price.volume,
                    'daily_change': round(daily_change, 4),
                    'daily_change_pct': round(daily_change_pct, 2),
                    'total_change': round(total_change, 4),
                    'total_change_pct': round(total_change_pct, 2)
                })
                
                previous_close = price.close_price
            
            # Calculs de résumé pour données DB
            last_price = db_prices[-1] if db_prices else None
            prices = [float(p.close_price) for p in db_prices]
            
            summary = {
                'symbol': symbol,
                'label': instrument.label,
                'sector': instrument.sector,
                'period': period,
                'start_date': db_prices[0].date.isoformat() if db_prices else None,
                'end_date': last_price.date.isoformat() if last_price else None,
                'first_price': float(first_price) if first_price else None,
                'last_price': float(last_price.close_price) if last_price else None,
                'total_change': float(last_price.close_price - first_price) if (last_price and first_price) else 0,
                'total_change_pct': round(((float(last_price.close_price - first_price) / float(first_price)) * 100), 2) if (last_price and first_price) else 0,
                'highest_price': max(prices) if prices else None,
                'lowest_price': min(prices) if prices else None,
                'average_price': round(sum(prices) / len(prices), 2) if prices else None,
                'total_volume': sum(p.volume for p in db_prices) if db_prices else 0,
                'data_points': len(price_data),
                'data_source': 'Database'
            }
            
            return Response({
                'success': True,
                'summary': summary,
                'data': price_data
            })
    
    except FinancialInstrument.DoesNotExist:
        return Response({'error': 'Instrument not found'}, status=404)
    except Exception as e:
        return Response({'error': str(e)}, status=500)
