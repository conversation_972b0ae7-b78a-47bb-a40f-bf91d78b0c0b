"""
Script principal de récupération quotidienne des données
de la Bourse de Casablanca avec intégration Django
"""

import logging
import time
import json
from datetime import datetime
from typing import Dict, List, Tuple
from pathlib import Path
import pandas as pd

from config import Config
from casablanca_api_client import CasablancaBourseAPIClient
from data_validator import DataValidator
from data_processor import DataProcessor
from django_data_saver import DjangoDataSaver

class CasablancaBourseUpdater:
    """
    Classe principale pour la mise à jour quotidienne des données
    avec intégration complète Django
    """
    
    def __init__(self):
        self.setup_logging()
        
        # Composants
        self.api_client = CasablancaBourseAPIClient()
        self.validator = DataValidator()
        self.processor = DataProcessor()
        self.django_saver = DjangoDataSaver()
        
        # Statistiques
        self.companies_processed = 0
        self.companies_successful = 0
        self.companies_failed = 0
        self.total_records = 0
        
        # Résultats
        self.daily_summaries = []
        self.processing_errors = []
        
        self.logger.info("🚀 CasablancaBourseUpdater initialisé avec intégration Django")
    
    def setup_logging(self):
        """Configure le logging"""
        log_file = Config.LOGS_DIR / Config.get_log_filename("daily_fetcher")
        
        logging.basicConfig(
            level=logging.INFO,
            format=Config.LOG_FORMAT,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def update_single_company(self, company_id: int, target_date: str = None) -> Tuple[bool, Dict]:
        """
        Met à jour les données d'une seule entreprise
        
        Args:
            company_id: ID de l'entreprise
            target_date: Date cible (optionnel)
        
        Returns:
            Tuple (success, summary)
        """
        if target_date is None:
            target_date = Config.get_target_date()
        
        self.companies_processed += 1
        
        try:
            self.logger.info(f"📊 [{self.companies_processed}] Traitement entreprise {company_id}")
            
            # 1. Récupération des données via API
            success, company_name, raw_data = self.api_client.fetch_company_data_with_retries(
                company_id, target_date
            )
            
            if not success:
                self.companies_failed += 1
                error_msg = f"Échec récupération API pour entreprise {company_id}"
                self.processing_errors.append(error_msg)
                self.logger.error(f"❌ {error_msg}")
                return False, self.processor.get_empty_summary(company_id, f"Entreprise_{company_id}")
            
            # 2. Validation des données
            validated_records, validation_summary = self.validator.validate_company_data(
                company_id, company_name, raw_data
            )
            
            if not validated_records:
                self.companies_failed += 1
                error_msg = f"Aucune donnée valide pour {company_name}"
                self.processing_errors.append(error_msg)
                self.logger.warning(f"⚠️ {error_msg}")
                return False, self.processor.get_empty_summary(company_id, company_name)
            
            # 3. Traitement et enrichissement
            processed_df, daily_summary = self.processor.process_company_data(
                company_id, company_name, validated_records
            )
            
            # 4. Sauvegarde des fichiers (CSV/JSON)
            if not processed_df.empty:
                # Sauvegarder les données traitées
                csv_path = self.processor.save_processed_data(
                    processed_df, company_id, company_name, target_date
                )
                
                # Sauvegarder le résumé quotidien
                summary_path = self.processor.save_daily_summary(daily_summary, target_date)
                
                # Ajouter les chemins de fichiers au résumé
                daily_summary['csv_file'] = csv_path
                daily_summary['summary_file'] = summary_path
            
            # 5. Mise à jour des statistiques
            self.companies_successful += 1
            self.total_records += len(validated_records)
            self.daily_summaries.append(daily_summary)
            
            self.logger.info(f"✅ {company_name} traité avec succès ({len(validated_records)} enregistrements)")
            
            return True, daily_summary
            
        except Exception as e:
            self.companies_failed += 1
            error_msg = f"Erreur traitement entreprise {company_id}: {e}"
            self.processing_errors.append(error_msg)
            self.logger.error(f"❌ {error_msg}")
            
            return False, self.processor.get_empty_summary(company_id, f"Entreprise_{company_id}")
    
    def update_all_companies(self, target_date: str = None, company_list: List[int] = None) -> bool:
        """
        Met à jour toutes les entreprises marocaines
        
        Args:
            target_date: Date cible (optionnel)
            company_list: Liste des entreprises à traiter (optionnel)
        
        Returns:
            bool: Success global
        """
        if target_date is None:
            target_date = Config.get_target_date()
        
        if company_list is None:
            company_list = Config.MOROCCAN_COMPANIES
        
        self.logger.info(f"🚀 DÉBUT MISE À JOUR QUOTIDIENNE - {target_date}")
        self.logger.info(f"📊 {len(company_list)} entreprises à traiter")
        self.logger.info("=" * 80)
        
        start_time = datetime.now()
        
        # Test de connexion API
        if not self.api_client.test_connection():
            self.logger.error("❌ Échec du test de connexion API - Arrêt du processus")
            return False
        
        # Test de connexion Django/DB
        if not self.django_saver.verify_database_connection():
            self.logger.error("❌ Échec du test de connexion Django/DB - Arrêt du processus")
            return False
        
        # Traitement de chaque entreprise
        for i, company_id in enumerate(company_list, 1):
            self.logger.info(f"\n{'='*20} [{i}/{len(company_list)}] {'='*20}")
            
            success, summary = self.update_single_company(company_id, target_date)
            
            # Délai entre les entreprises
            if i < len(company_list):
                time.sleep(Config.REQUEST_DELAY)
        
        # Sauvegarde en base de données Django
        self.logger.info("\n🗄️ SAUVEGARDE EN BASE DE DONNÉES")
        self.logger.info("=" * 50)
        
        django_stats = self.django_saver.save_all_data(self.daily_summaries)
        
        # Statistiques finales
        end_time = datetime.now()
        duration = end_time - start_time
        
        self.logger.info("\n" + "=" * 80)
        self.logger.info("📈 STATISTIQUES FINALES")
        self.logger.info("=" * 80)
        self.logger.info(f"⏱️ Durée totale: {duration}")
        self.logger.info(f"🏢 Entreprises traitées: {self.companies_processed}")
        self.logger.info(f"✅ Succès: {self.companies_successful}")
        self.logger.info(f"❌ Échecs: {self.companies_failed}")
        self.logger.info(f"📊 Total enregistrements: {self.total_records}")
        
        # Statistiques API
        api_stats = self.api_client.get_statistics()
        self.logger.info(f"🌐 Requêtes API: {api_stats['total_requests']} (Succès: {api_stats['success_rate']}%)")
        
        # Statistiques validation
        validation_stats = self.validator.get_validation_statistics()
        self.logger.info(f"🔍 Validation: {validation_stats['valid_records']}/{validation_stats['total_processed']} (Taux: {validation_stats['validation_rate']}%)")
        
        # Statistiques Django
        if django_stats['success']:
            self.logger.info(f"💾 Django: {django_stats['companies_processed']} entreprises, {django_stats['bonds_saved']} bonds sauvegardés")
        else:
            self.logger.error(f"❌ Échec sauvegarde Django: {django_stats.get('error', 'Erreur inconnue')}")
        
        success_rate = (self.companies_successful / max(1, self.companies_processed)) * 100
        
        if success_rate >= 80:
            self.logger.info(f"🎉 MISE À JOUR RÉUSSIE (Taux: {success_rate:.1f}%)")
            return True
        else:
            self.logger.warning(f"⚠️ MISE À JOUR PARTIELLE (Taux: {success_rate:.1f}%)")
            return success_rate >= 50  # Succès partiel si > 50%
    
    def create_summary_report(self, target_date: str = None) -> str:
        """Crée un rapport de résumé complet"""
        if target_date is None:
            target_date = Config.get_target_date()
        
        try:
            # Informations sur les dernières données Django
            django_info = self.django_saver.get_latest_data_info()
            
            # Rapport consolidé
            consolidated_report = {
                'date': target_date,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'statistics': {
                    'companies_processed': self.companies_processed,
                    'companies_successful': self.companies_successful,
                    'companies_failed': self.companies_failed,
                    'total_records': self.total_records,
                    'success_rate': (self.companies_successful / max(1, self.companies_processed)) * 100
                },
                'api_statistics': self.api_client.get_statistics(),
                'validation_statistics': self.validator.get_validation_statistics(),
                'django_info': django_info,
                'companies_summaries': self.daily_summaries,
                'errors': self.processing_errors
            }
            
            # Sauvegarde JSON
            report_file = Config.DAILY_DATA_DIR / f"consolidated_report_{target_date}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(consolidated_report, f, ensure_ascii=False, indent=2)
            
            # Sauvegarde CSV des résumés
            if self.daily_summaries:
                summaries_df = pd.DataFrame(self.daily_summaries)
                csv_file = Config.DAILY_DATA_DIR / f"daily_summaries_{target_date}.csv"
                summaries_df.to_csv(csv_file, index=False, encoding='utf-8')
                
                self.logger.info(f"✅ Rapport CSV créé: {csv_file}")
            
            self.logger.info(f"✅ Rapport consolidé créé: {report_file}")
            return str(report_file)
            
        except Exception as e:
            self.logger.error(f"❌ Erreur création rapport: {e}")
            return ""
    
    def test_with_sample_companies(self, count: int = 3) -> bool:
        """Test avec un échantillon d'entreprises"""
        sample_companies = Config.MOROCCAN_COMPANIES[:count]
        self.logger.info(f"🧪 Test avec {count} entreprises: {sample_companies}")
        
        success = self.update_all_companies(company_list=sample_companies)
        
        if success:
            self.create_summary_report()
        
        return success
    
    def cleanup(self):
        """Nettoyage final"""
        try:
            self.api_client.close()
            self.logger.info("🧹 Nettoyage terminé")
        except Exception as e:
            self.logger.error(f"❌ Erreur nettoyage: {e}")

def main():
    """Fonction principale pour test"""
    print("🚀 RÉCUPÉRATEUR DE DONNÉES QUOTIDIENNES")
    print("Bourse de Casablanca - 77 Entreprises")
    print("Intégration Django XCapitalTerminal")
    print("=" * 60)
    
    updater = CasablancaBourseUpdater()
    
    try:
        # Test avec une seule entreprise d'abord
        print("\n📋 Test avec Attijariwafa Bank (ID: 511)...")
        success, summary = updater.update_single_company(511)
        
        if success:
            print("✅ Test réussi!")
            print(f"Prix de clôture: {summary['closing_price']} MAD")
            print(f"Volume: {summary['volume']}")
            
            # Test sauvegarde Django
            django_stats = updater.django_saver.save_all_data([summary])
            if django_stats['success']:
                print(f"✅ Sauvegarde Django réussie: {django_stats['bonds_saved']} bond(s)")
            else:
                print(f"❌ Erreur sauvegarde Django: {django_stats.get('error')}")
            
            # Choix de continuer
            response = input("\nContinuer avec toutes les entreprises? (o/n): ")
            if response.lower().startswith('o'):
                print("\n🚀 Lancement de la mise à jour complète...")
                success = updater.update_all_companies()
                
                if success:
                    print("🎉 Mise à jour complète réussie!")
                    updater.create_summary_report()
                else:
                    print("⚠️ Mise à jour avec des problèmes")
            else:
                # Test avec échantillon
                response = input("Test avec 5 entreprises? (o/n): ")
                if response.lower().startswith('o'):
                    updater.test_with_sample_companies(5)
        else:
            print("❌ Test échoué - Vérifiez la connexion")
    
    finally:
        updater.cleanup()

if __name__ == "__main__":
    main()
