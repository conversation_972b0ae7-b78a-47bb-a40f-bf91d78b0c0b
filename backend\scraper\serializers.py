"""
Serializers pour l'API du scraper
"""

from rest_framework import serializers
from .models import ScrapedItem, ScraperExecution, ScraperTarget


class ScraperTargetSerializer(serializers.ModelSerializer):
    """
    Serializer pour les cibles de scraping
    """
    items_count = serializers.SerializerMethodField()
    last_scrape = serializers.SerializerMethodField()
    
    class Meta:
        model = ScraperTarget
        fields = [
            'id', 'name', 'url', 'is_active', 'scraper_type',
            'css_selectors', 'headers', 'created_at', 'updated_at',
            'items_count', 'last_scrape'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_items_count(self, obj):
        """Nombre d'items scrapés pour cette cible"""
        return obj.scraped_items.count()
    
    def get_last_scrape(self, obj):
        """Date du dernier scraping"""
        last_item = obj.scraped_items.first()
        return last_item.extracted_at if last_item else None


class ScrapedItemSerializer(serializers.ModelSerializer):
    """
    Serializer pour les éléments scrapés
    """
    target_name = serializers.CharField(source='target.name', read_only=True)
    word_count = serializers.SerializerMethodField()
    age_in_hours = serializers.SerializerMethodField()
    
    class Meta:
        model = ScrapedItem
        fields = [
            'id', 'source_url', 'title', 'summary', 'status',
            'extracted_at', 'meta', 'target_name', 'error_message',
            'retry_count', 'word_count', 'age_in_hours'
        ]
        read_only_fields = ['extracted_at']
    
    def get_word_count(self, obj):
        """Nombre de mots dans le résumé"""
        return len(obj.summary.split()) if obj.summary else 0
    
    def get_age_in_hours(self, obj):
        """Âge de l'élément en heures"""
        from django.utils import timezone
        delta = timezone.now() - obj.extracted_at
        return round(delta.total_seconds() / 3600, 1)


class ScrapedItemDetailSerializer(ScrapedItemSerializer):
    """
    Serializer détaillé pour un élément scrapé (inclut le HTML brut)
    """
    target_details = ScraperTargetSerializer(source='target', read_only=True)
    
    class Meta(ScrapedItemSerializer.Meta):
        fields = ScrapedItemSerializer.Meta.fields + ['raw_html', 'target_details']


class ScraperExecutionSerializer(serializers.ModelSerializer):
    """
    Serializer pour les exécutions du scraper
    """
    duration_seconds = serializers.SerializerMethodField()
    duration_formatted = serializers.SerializerMethodField()
    success_rate_formatted = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = ScraperExecution
        fields = [
            'id', 'task_id', 'status', 'status_display', 'started_at', 'completed_at',
            'total_urls', 'successful_scrapes', 'failed_scrapes', 'success_rate',
            'duration_seconds', 'duration_formatted', 'success_rate_formatted',
            'logs', 'error_details'
        ]
        read_only_fields = ['started_at', 'completed_at', 'success_rate']
    
    def get_duration_seconds(self, obj):
        """Durée en secondes"""
        return obj.duration
    
    def get_duration_formatted(self, obj):
        """Durée formatée"""
        duration = obj.duration
        if duration:
            minutes = int(duration // 60)
            seconds = int(duration % 60)
            return f"{minutes}m {seconds}s"
        return None
    
    def get_success_rate_formatted(self, obj):
        """Taux de succès formaté"""
        return f"{obj.success_rate:.1f}%"


class ScraperExecutionSummarySerializer(serializers.ModelSerializer):
    """
    Serializer résumé pour les exécutions (sans les logs)
    """
    duration_formatted = serializers.SerializerMethodField()
    success_rate_formatted = serializers.SerializerMethodField()
    
    class Meta:
        model = ScraperExecution
        fields = [
            'id', 'task_id', 'status', 'started_at', 'completed_at',
            'total_urls', 'successful_scrapes', 'failed_scrapes',
            'success_rate', 'duration_formatted', 'success_rate_formatted'
        ]
        read_only_fields = fields
    
    def get_duration_formatted(self, obj):
        """Durée formatée"""
        duration = obj.duration
        if duration:
            minutes = int(duration // 60)
            seconds = int(duration % 60)
            return f"{minutes}m {seconds}s"
        return None
    
    def get_success_rate_formatted(self, obj):
        """Taux de succès formaté"""
        return f"{obj.success_rate:.1f}%"


class ScraperStatsSerializer(serializers.Serializer):
    """
    Serializer pour les statistiques du scraper
    """
    total_items = serializers.IntegerField()
    items_last_24h = serializers.IntegerField()
    items_last_week = serializers.IntegerField()
    items_by_status = serializers.DictField()
    active_targets = serializers.IntegerField()
    recent_executions = ScraperExecutionSummarySerializer(many=True, read_only=True)
    last_execution = ScraperExecutionSummarySerializer(read_only=True)


class TriggerScraperSerializer(serializers.Serializer):
    """
    Serializer pour déclencher le scraper manuellement
    """
    force = serializers.BooleanField(
        default=False,
        help_text="Forcer l'exécution même si une autre tâche est en cours"
    )


class TestScraperSerializer(serializers.Serializer):
    """
    Serializer pour tester le scraper avec une URL
    """
    url = serializers.URLField(
        help_text="URL à tester avec le scraper"
    )
    save_result = serializers.BooleanField(
        default=False,
        help_text="Sauvegarder le résultat en base de données"
    )
