# Generated by Django 5.2.5 on 2025-08-20 19:38

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('market_data', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PredictionModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('model_type', models.CharField(choices=[('ARIMA', 'ARIMA'), ('LSTM', 'LSTM'), ('LINEAR_REGRESSION', 'Linear Regression'), ('RANDOM_FOREST', 'Random Forest'), ('PROPHET', 'Prophet')], max_length=20)),
                ('file_path', models.CharField(max_length=500)),
                ('parameters', models.J<PERSON><PERSON>ield(default=dict)),
                ('accuracy_metrics', models.JSO<PERSON>ield(default=dict)),
                ('training_start_date', models.DateField()),
                ('training_end_date', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('stock', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prediction_models', to='market_data.stock')),
            ],
            options={
                'db_table': 'prediction_models',
                'ordering': ['-created_at'],
                'unique_together': {('stock', 'model_type', 'name')},
            },
        ),
        migrations.CreateModel(
            name='PredictionRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('model_type', models.CharField(choices=[('ARIMA', 'ARIMA'), ('LSTM', 'LSTM'), ('LINEAR_REGRESSION', 'Linear Regression'), ('RANDOM_FOREST', 'Random Forest'), ('PROPHET', 'Prophet')], max_length=20)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('forecast_days', models.IntegerField(default=30, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(365)])),
                ('confidence_interval', models.DecimalField(decimal_places=2, default=95.0, max_digits=5, validators=[django.core.validators.MinValueValidator(0.01), django.core.validators.MaxValueValidator(99.99)])),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed')], default='PENDING', max_length=20)),
                ('result_data', models.JSONField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('requested_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('stock', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='market_data.stock')),
            ],
            options={
                'db_table': 'prediction_requests',
                'ordering': ['-requested_at'],
            },
        ),
        migrations.CreateModel(
            name='Prediction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('prediction_date', models.DateField()),
                ('target_date', models.DateField()),
                ('predicted_value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('confidence_lower', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('confidence_upper', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('confidence_level', models.DecimalField(decimal_places=2, default=95.0, max_digits=5, validators=[django.core.validators.MinValueValidator(0.01), django.core.validators.MaxValueValidator(99.99)])),
                ('actual_value', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='predictions', to='predictions.predictionmodel')),
            ],
            options={
                'db_table': 'predictions',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['target_date'], name='predictions_target__c50c1c_idx'), models.Index(fields=['prediction_date'], name='predictions_predict_b05e71_idx'), models.Index(fields=['model', 'target_date'], name='predictions_model_i_47690b_idx')],
                'unique_together': {('model', 'prediction_date', 'target_date')},
            },
        ),
    ]
