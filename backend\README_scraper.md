# Casablanca Stock Exchange Scraper

## Overview

This Django application implements a comprehensive scraper for the Casablanca Stock Exchange (Bourse de Casablanca) that automatically fetches stock market data daily at 19:00 and stores it in the database.

## Features

- **Daily Automated Scraping**: Scheduled execution at 19:00 using Celery Beat
- **Dual API Integration**: Fetches data from both pages of the Casablanca Stock Exchange API
- **Database Integration**: Stores data in existing `XCapitalTerminal_Companies` and `XCapitalTerminal_CompanyBonds` tables
- **Comprehensive Audit Trail**: Tracks all scraping operations with detailed logs
- **Error Handling**: Robust retry logic and error recovery
- **Concurrent Execution Prevention**: Redis locks prevent multiple simultaneous executions
- **Docker Support**: Fully containerized with Docker Compose
- **Monitoring**: Built-in health checks and connectivity tests

## Architecture

### Components

1. **Celery Tasks** (`scraper/tasks.py`)
   - `run_casablanca_scraper`: Main daily scraper task
   - `test_casablanca_api_connectivity`: API connectivity testing
   - `cleanup_old_data`: Data maintenance and cleanup
   - `monthly_maintenance`: Monthly system maintenance

2. **Database Models** (`scraper/models.py`)
   - `ScrapedItem`: Enhanced model for Casablanca-specific data
   - `CasablancaApiExecution`: Detailed execution tracking
   - Integration with existing `XCapitalTerminal_Companies` and `XCapitalTerminal_CompanyBonds`

3. **Celery Configuration** (`xcapital_backend/celery.py`)
   - Daily scheduling at 19:00
   - Redis broker configuration
   - Task routing and options

## Environment Variables

### Required Configuration

```bash
# Database
DATABASE_URL=*****************************************************/xcapital

# Redis/Celery
REDIS_URL=redis://redis:6379/0
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Casablanca Scraper Configuration
CASABLANCA_USER_AGENT=XCapital-Bot/1.0 (+https://xcapitalterminal.com/robots.txt)
CASABLANCA_TIMEOUT=30
CASABLANCA_MAX_RETRIES=3
CASABLANCA_RETRY_DELAY=60
CASABLANCA_RATE_LIMIT=2
CASABLANCA_VERIFY_SSL=False
CASABLANCA_BATCH_SIZE=10
CASABLANCA_AUDIT_TRAIL=True
CASABLANCA_SAVE_RAW=True
CASABLANCA_DB_BATCH_SIZE=50
CASABLANCA_LOG_LEVEL=INFO
CASABLANCA_LOG_RESPONSES=False
```

### Optional Configuration

```bash
# Development
DEBUG=True

# Legacy compatibility
TARGET_URLS=https://www.xcapitalterminal.com,https://finance.yahoo.com/news
SCRAPER_USER_AGENT=XCapital-Bot/1.0
SCRAPER_TIMEOUT=30
SCRAPER_MAX_RETRIES=3
SCRAPER_RETRY_DELAY=60

```

## API Endpoints

The scraper fetches data from two Casablanca Stock Exchange API endpoints:

### Page 1
```
https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action?page%5Blimit%5D=50&page%5Boffset%5D=0
```

### Page 2
```
https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action?page%5Blimit%5D=50&page%5Boffset%5D=50
```

### Response Format

```json
{
  "data": {
    "data": [
      {
        "id": "468",
        "type": "instrument",
        "attributes": {
          "symbol": "BOA",
          "instrument_name": "Bank of Africa",
          "open_price": "180.00",
          "close_price": "182.50",
          "high_price": "185.00",
          "low_price": "178.00",
          "current_price": "182.50",
          "volume": "10000",
          "shares_traded": "8500",
          "total_trades": "45",
          "market_cap": "1825000.00",
          "variation": "2.50",
          "variation_percent": "1.39",
          "currency": "MAD"
        }
      }
    ]
  },
  "meta": {
    "total": 100,
    "page": 1
  }
}
```

## Local Development

### Prerequisites

- Docker and Docker Compose
- Python 3.11+
- PostgreSQL 13+
- Redis 6+

### Setup

1. **Clone and Navigate**
   ```bash
   git checkout feature/scraper-celery
   cd backend
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Build and Start Services**
   ```bash
   docker-compose up --build
   ```

4. **Run Database Migrations**
   ```bash
   docker-compose exec django python manage.py migrate
   ```

5. **Create Superuser (Optional)**
   ```bash
   docker-compose exec django python manage.py createsuperuser
   ```

### Development Commands

#### Start Services
```bash
# Start all services
docker-compose up

# Start in background
docker-compose up -d

# Start specific service
docker-compose up django
docker-compose up celery_worker
docker-compose up celery_beat
```

#### Testing

```bash
# Run all tests
docker-compose exec django python manage.py test scraper

# Run specific test class
docker-compose exec django python manage.py test scraper.tests.CasablancaScraperTasksTest

# Run with coverage
docker-compose exec django coverage run --source='.' manage.py test scraper
docker-compose exec django coverage report
```

#### Manual Task Execution

```bash
# Test API connectivity
docker-compose exec django python manage.py shell -c "
from scraper.tasks import test_casablanca_api_connectivity
result = test_casablanca_api_connectivity.delay()
print(result.get())
"

# Run scraper manually
docker-compose exec django python manage.py shell -c "
from scraper.tasks import run_casablanca_scraper
result = run_casablanca_scraper.delay()
print(result.get())
"

# Cleanup old data
docker-compose exec django python manage.py shell -c "
from scraper.tasks import cleanup_old_data
result = cleanup_old_data.delay(days_to_keep=30)
print(result.get())
"
```

## Verification Steps

### 1. Service Health Check

```bash
# Check all containers are running
docker-compose ps

# Check logs
docker-compose logs django
docker-compose logs celery_worker
docker-compose logs celery_beat
docker-compose logs redis
docker-compose logs postgres
```

### 2. Database Verification

```bash
# Connect to database
docker-compose exec postgres psql -U postgres -d xcapital

# Check tables exist
\dt

# Check data
SELECT COUNT(*) FROM "XCapitalTerminal_Companies";
SELECT COUNT(*) FROM "XCapitalTerminal_CompanyBonds";
SELECT COUNT(*) FROM scraper_scrapeditem;
SELECT COUNT(*) FROM scraper_casablancaapiexecution;
```

### 3. API Connectivity Test

```bash
# Test API endpoints manually
curl -H "User-Agent: XCapital-Bot/1.0" \
  "https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action?page%5Blimit%5D=5&page%5Boffset%5D=0"
```

### 4. Celery Task Verification

```bash
# Check Celery worker status
docker-compose exec celery_worker celery -A xcapital_backend inspect active

# Check scheduled tasks
docker-compose exec celery_beat celery -A xcapital_backend inspect scheduled

# Monitor task execution
docker-compose exec django python manage.py shell -c "
from django_celery_beat.models import PeriodicTask
for task in PeriodicTask.objects.all():
    print(f'{task.name}: {task.enabled} - {task.crontab}')
"
```

## Monitoring and Logs

### Log Locations

- **Django**: `logs/django.log`
- **Celery Worker**: `logs/celery_worker.log`
- **Celery Beat**: `logs/celery_beat.log`
- **Scraper**: `logs/scraper.log`

### Monitoring Commands

```bash
# Follow logs in real-time
docker-compose logs -f django
docker-compose logs -f celery_worker

# Check last execution
docker-compose exec django python manage.py shell -c "
from scraper.models import CasablancaApiExecution
last_exec = CasablancaApiExecution.objects.order_by('-started_at').first()
if last_exec:
    print(f'Last execution: {last_exec.started_at}')
    print(f'Status: {last_exec.status}')
    print(f'Companies processed: {last_exec.companies_processed}')
    print(f'Success rate: {last_exec.success_rate}%')
"
```

## Troubleshooting

### Common Issues

1. **SSL Certificate Errors**
   - Set `CASABLANCA_VERIFY_SSL=False` in environment variables
   - Check network connectivity to Casablanca APIs

2. **Database Connection Issues**
   - Verify `DATABASE_URL` is correct
   - Ensure PostgreSQL container is running
   - Check database migrations are applied

3. **Redis Connection Issues**
   - Verify `REDIS_URL` is correct
   - Ensure Redis container is running
   - Check Celery broker configuration

4. **Task Not Executing**
   - Verify Celery Beat is running
   - Check scheduled tasks in database
   - Ensure timezone configuration is correct

5. **API Rate Limiting**
   - Increase `CASABLANCA_RETRY_DELAY`
   - Reduce `CASABLANCA_RATE_LIMIT`
   - Check API response headers for rate limit info

### Debug Commands

```bash
# Check Celery configuration
docker-compose exec django python -c "
from xcapital_backend.celery import app
print('Broker URL:', app.conf.broker_url)
print('Result Backend:', app.conf.result_backend)
print('Beat Schedule:', app.conf.beat_schedule)
"

# Test database connection
docker-compose exec django python manage.py dbshell

# Check Redis connection
docker-compose exec redis redis-cli ping
```

## Production Deployment

### Environment Considerations

1. **Security**
   - Set `DEBUG=False`
   - Use strong database passwords
   - Configure proper SSL certificates
   - Set up firewall rules

2. **Performance**
   - Increase worker processes based on CPU cores
   - Configure Redis persistence
   - Set up database connection pooling
   - Monitor memory usage

3. **Monitoring**
   - Set up log aggregation (ELK stack, Fluentd)
   - Configure alerting for failed tasks
   - Monitor API response times
   - Track database performance

### Production Docker Compose

```yaml
# Use production-ready images
# Configure resource limits
# Set up health checks
# Configure log drivers
# Use secrets for sensitive data
```

## Support

For issues and questions:

1. Check logs for error messages
2. Verify environment configuration
3. Test API connectivity manually
4. Review database state
5. Check Celery task status

## License

This project is part of the XCapital Terminal backend system.


