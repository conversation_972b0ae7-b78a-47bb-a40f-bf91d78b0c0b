#!/usr/bin/env python3
"""
Test rapide pour Bank of Africa (Company ID: 391)
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
django.setup()

def test_bank_of_africa():
    """Test de Bank of Africa (ID: 391)"""
    
    print("🏦 TEST BANK OF AFRICA (ID: 391)")
    print("=" * 40)
    
    try:
        import daily_update_orchestrator
        
        print("📊 Lancement du test...")
        result = daily_update_orchestrator.test_single_company(391)
        
        print("\n📈 Résultats:")
        print(f"Succès: {'✅' if result.get('success', False) else '❌'} {result.get('success', False)}")
        
        if result.get('success', False):
            print(f"Entreprise: {result.get('company_name', 'Bank of Africa')}")
            print(f"ID: {result.get('company_id', 391)}")
            print(f"Prix de clôture: {result.get('closing_price', 'N/A')} MAD")
            print(f"Volume: {result.get('volume', 'N/A')}")
            print(f"Nombre d'enregistrements: {result.get('records_count', 0)}")
            print(f"Date des données: {result.get('data_date', 'N/A')}")
            
            if result.get('raw_data'):
                print(f"Données brutes disponibles: {len(result['raw_data'])} enregistrements")
        else:
            print(f"Erreur: {result.get('error', 'Erreur inconnue')}")
            
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    
    print("\n🎉 Test terminé!")
    return True

if __name__ == "__main__":
    success = test_bank_of_africa()
    print(f"\nRésultat: {'✅ SUCCÈS' if success else '❌ ÉCHEC'}")
