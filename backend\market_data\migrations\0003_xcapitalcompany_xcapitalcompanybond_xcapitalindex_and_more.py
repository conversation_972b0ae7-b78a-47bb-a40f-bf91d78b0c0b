# Generated by Django 4.2.23 on 2025-09-10 17:44

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('market_data', '0002_financialinstrument_masiindex_instrumentprediction_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='XCapitalCompany',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('symbol', models.CharField(db_index=True, max_length=50, unique=True)),
                ('company_id', models.CharField(max_length=100, unique=True)),
                ('nom_francais', models.CharField(max_length=255)),
                ('nom_arabe', models.CharField(blank=True, max_length=255, null=True)),
                ('nom_anglais', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'XCapital Company',
                'verbose_name_plural': 'XCapital Companies',
                'db_table': 'XCapitalTerminal_Companies',
                'ordering': ['symbol'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='XCapitalCompanyBond',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_trade', models.DateField(db_index=True)),
                ('open_price', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('high_price', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('low_price', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('close_price', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('current_price', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('volume', models.BigIntegerField(blank=True, default=0, null=True)),
                ('shares_traded', models.BigIntegerField(blank=True, default=0, null=True)),
                ('total_trades', models.IntegerField(blank=True, default=0, null=True)),
                ('market_cap', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('adjusted_close', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('consolidated_ratio', models.DecimalField(blank=True, decimal_places=6, max_digits=10, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'XCapital Company Bond',
                'verbose_name_plural': 'XCapital Company Bonds',
                'db_table': 'XCapitalTerminal_CompanyBonds',
                'ordering': ['-date_trade'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='XCapitalIndex',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('index_id', models.CharField(db_index=True, max_length=100, unique=True)),
                ('index_name', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'XCapital Index',
                'verbose_name_plural': 'XCapital Indices',
                'db_table': 'XCapitalTerminal_Index',
                'ordering': ['index_name'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='XCapitalIndexValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_value', models.DateField(db_index=True)),
                ('value', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('previous_value', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('daily_change', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('daily_change_pct', models.DecimalField(blank=True, decimal_places=4, max_digits=8, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'XCapital Index Value',
                'verbose_name_plural': 'XCapital Index Values',
                'db_table': 'XCapitalTerminal_IndexValue',
                'ordering': ['-date_value'],
                'managed': False,
            },
        ),
        migrations.AlterUniqueTogether(
            name='instrumentprediction',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='instrumentprediction',
            name='instrument',
        ),
        migrations.AlterUniqueTogether(
            name='instrumentprice',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='instrumentprice',
            name='instrument',
        ),
        migrations.AlterUniqueTogether(
            name='masiindexvalue',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='masiindexvalue',
            name='index',
        ),
        migrations.DeleteModel(
            name='FinancialInstrument',
        ),
        migrations.DeleteModel(
            name='InstrumentPrediction',
        ),
        migrations.DeleteModel(
            name='InstrumentPrice',
        ),
        migrations.DeleteModel(
            name='MASIIndex',
        ),
        migrations.DeleteModel(
            name='MASIIndexValue',
        ),
    ]
