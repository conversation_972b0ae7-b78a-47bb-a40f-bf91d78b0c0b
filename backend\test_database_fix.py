#!/usr/bin/env python3
"""
Test et Correction du Système de Base de Données
"""

import os
import sys
from datetime import datetime, date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

try:
    import django
    django.setup()
    print("✅ Django configuré avec succès")
except Exception as e:
    print(f"❌ Erreur configuration Django: {e}")
    sys.exit(1)

def test_database_insertion():
    """Test d'insertion directe dans la base de données"""
    print("🔍 TEST INSERTION BASE DE DONNÉES")
    print("=" * 50)
    
    try:
        from market_data.postgres_models import XCapitalCompany, XCapitalCompanyBond
        from django.db import transaction, connection
        from decimal import Decimal
        
        # 1. Vérifier la structure des tables
        print("\n1️⃣ Vérification structure tables:")
        
        with connection.cursor() as cursor:
            # Vérifier si les tables existent
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('XCapitalTerminal_Companies', 'XCapitalTerminal_CompanyBonds')
            """)
            tables = cursor.fetchall()
            print(f"   Tables trouvées: {[t[0] for t in tables]}")
            
            # Vérifier structure XCapitalTerminal_Companies
            cursor.execute("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'XCapitalTerminal_Companies'
                ORDER BY ordinal_position
            """)
            companies_columns = cursor.fetchall()
            print(f"   Colonnes XCapitalTerminal_Companies: {len(companies_columns)}")
            for col in companies_columns:
                print(f"      {col[0]} ({col[1]}) - Nullable: {col[2]}")
            
            # Vérifier structure XCapitalTerminal_CompanyBonds
            cursor.execute("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'XCapitalTerminal_CompanyBonds'
                ORDER BY ordinal_position
            """)
            bonds_columns = cursor.fetchall()
            print(f"   Colonnes XCapitalTerminal_CompanyBonds: {len(bonds_columns)}")
            for col in bonds_columns:
                print(f"      {col[0]} ({col[1]}) - Nullable: {col[2]}")
        
        # 2. Test insertion avec SQL direct
        print("\n2️⃣ Test insertion SQL directe:")
        
        test_company_id = "468"
        test_symbol = "BOA"
        test_name = "Bank of Africa"
        
        with connection.cursor() as cursor:
            # Insérer une entreprise de test
            cursor.execute("""
                INSERT INTO "XCapitalTerminal_Companies" 
                (company_id, symbol, nom_francais, nom_anglais, nom_arabe)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (company_id) DO UPDATE SET
                symbol = EXCLUDED.symbol,
                nom_francais = EXCLUDED.nom_francais,
                nom_anglais = EXCLUDED.nom_anglais
                RETURNING id
            """, [test_company_id, test_symbol, test_name, test_name, ""])
            
            company_result = cursor.fetchone()
            if company_result:
                company_pk = company_result[0]
                print(f"   ✅ Entreprise insérée/mise à jour: ID={company_pk}")
            
            # Insérer des données de prix
            today = date.today()
            cursor.execute("""
                INSERT INTO "XCapitalTerminal_CompanyBonds"
                (company_id, date_trade, open_price, high_price, low_price, close_price, volume, value_mad)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (company_id, date_trade) DO UPDATE SET
                open_price = EXCLUDED.open_price,
                high_price = EXCLUDED.high_price,
                low_price = EXCLUDED.low_price,
                close_price = EXCLUDED.close_price,
                volume = EXCLUDED.volume,
                value_mad = EXCLUDED.value_mad
                RETURNING id
            """, [
                company_pk, today,
                Decimal('180.50'), Decimal('185.00'), Decimal('178.00'), Decimal('182.75'),
                15000, Decimal('2741250.00')
            ])
            
            bond_result = cursor.fetchone()
            if bond_result:
                bond_pk = bond_result[0]
                print(f"   ✅ Données prix insérées/mises à jour: ID={bond_pk}")
        
        # 3. Vérifier les données insérées
        print("\n3️⃣ Vérification données insérées:")
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT c.company_id, c.symbol, c.nom_francais, b.date_trade, b.close_price, b.volume
                FROM "XCapitalTerminal_Companies" c
                LEFT JOIN "XCapitalTerminal_CompanyBonds" b ON c.id = b.company_id
                WHERE c.company_id = %s
                ORDER BY b.date_trade DESC
                LIMIT 5
            """, [test_company_id])
            
            results = cursor.fetchall()
            print(f"   Données trouvées: {len(results)} enregistrements")
            for row in results:
                print(f"      {row[1]} ({row[0]}): {row[4]} MAD, Vol: {row[5]} ({row[3]})")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test base de données: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_improved_database_saver():
    """Créer un module amélioré pour sauvegarder les données"""
    
    database_saver_code = '''#!/usr/bin/env python3
"""
Module amélioré pour sauvegarder les données dans PostgreSQL
"""

import os
import sys
from datetime import datetime, date
from decimal import Decimal
from typing import Dict, Optional, List

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

try:
    import django
    django.setup()
    from django.db import transaction, connection
    DJANGO_AVAILABLE = True
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    DJANGO_AVAILABLE = False

class ImprovedDatabaseSaver:
    """
    Sauvegarde améliorée avec SQL direct pour éviter les problèmes managed=False
    """
    
    def __init__(self):
        self.logger = None
        
    def save_company_data(self, company_data: Dict) -> bool:
        """
        Sauvegarde une entreprise et ses données avec SQL direct
        """
        
        if not DJANGO_AVAILABLE:
            print("❌ Django non disponible")
            return False
        
        try:
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # 1. Insérer/Mettre à jour l'entreprise
                    cursor.execute("""
                        INSERT INTO "XCapitalTerminal_Companies" 
                        (company_id, symbol, nom_francais, nom_anglais, nom_arabe)
                        VALUES (%s, %s, %s, %s, %s)
                        ON CONFLICT (company_id) DO UPDATE SET
                        symbol = EXCLUDED.symbol,
                        nom_francais = EXCLUDED.nom_francais,
                        nom_anglais = EXCLUDED.nom_anglais,
                        updated_at = CURRENT_TIMESTAMP
                        RETURNING id
                    """, [
                        str(company_data['company_id']),
                        company_data['symbol'],
                        company_data['company_name'],
                        company_data['company_name'],
                        ""
                    ])
                    
                    company_result = cursor.fetchone()
                    if not company_result:
                        # Si INSERT a échoué, essayer de récupérer l'ID existant
                        cursor.execute("""
                            SELECT id FROM "XCapitalTerminal_Companies" 
                            WHERE company_id = %s
                        """, [str(company_data['company_id'])])
                        company_result = cursor.fetchone()
                    
                    if not company_result:
                        raise Exception(f"Impossible de créer/récupérer l'entreprise {company_data['company_id']}")
                    
                    company_pk = company_result[0]
                    
                    # 2. Insérer/Mettre à jour les données de prix
                    cursor.execute("""
                        INSERT INTO "XCapitalTerminal_CompanyBonds"
                        (company_id, date_trade, open_price, high_price, low_price, close_price, volume, value_mad)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (company_id, date_trade) DO UPDATE SET
                        open_price = EXCLUDED.open_price,
                        high_price = EXCLUDED.high_price,
                        low_price = EXCLUDED.low_price,
                        close_price = EXCLUDED.close_price,
                        volume = EXCLUDED.volume,
                        value_mad = EXCLUDED.value_mad,
                        updated_at = CURRENT_TIMESTAMP
                        RETURNING id
                    """, [
                        company_pk,
                        company_data['date_trade'],
                        company_data['open_price'],
                        company_data['high_price'],
                        company_data['low_price'],
                        company_data['close_price'],
                        company_data['volume'],
                        company_data['value_mad']
                    ])
                    
                    bond_result = cursor.fetchone()
                    if bond_result:
                        print(f"✅ Données sauvées: {company_data['symbol']} - {company_data['close_price']} MAD")
                        return True
                    else:
                        print(f"❌ Échec sauvegarde prix pour {company_data['symbol']}")
                        return False
                        
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_companies_from_database(self) -> List[Dict]:
        """
        Récupère toutes les entreprises de la base de données
        """
        
        if not DJANGO_AVAILABLE:
            return []
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT company_id, symbol, nom_francais
                    FROM "XCapitalTerminal_Companies"
                    ORDER BY symbol
                """)
                
                companies = []
                for row in cursor.fetchall():
                    companies.append({
                        'company_id': row[0],
                        'symbol': row[1],
                        'name': row[2]
                    })
                
                return companies
                
        except Exception as e:
            print(f"❌ Erreur récupération entreprises: {e}")
            return []
    
    def get_latest_prices(self, limit: int = 10) -> List[Dict]:
        """
        Récupère les derniers prix enregistrés
        """
        
        if not DJANGO_AVAILABLE:
            return []
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT c.symbol, c.nom_francais, b.date_trade, b.close_price, b.volume
                    FROM "XCapitalTerminal_CompanyBonds" b
                    JOIN "XCapitalTerminal_Companies" c ON c.id = b.company_id
                    ORDER BY b.date_trade DESC, b.updated_at DESC
                    LIMIT %s
                """, [limit])
                
                prices = []
                for row in cursor.fetchall():
                    prices.append({
                        'symbol': row[0],
                        'name': row[1],
                        'date': row[2],
                        'price': float(row[3]),
                        'volume': row[4]
                    })
                
                return prices
                
        except Exception as e:
            print(f"❌ Erreur récupération prix: {e}")
            return []

# Instance globale
improved_db_saver = ImprovedDatabaseSaver()

def save_company_data(company_data: Dict) -> bool:
    """Fonction helper pour sauvegarder"""
    return improved_db_saver.save_company_data(company_data)

def get_companies_from_database() -> List[Dict]:
    """Fonction helper pour récupérer les entreprises"""
    return improved_db_saver.get_companies_from_database()

def get_latest_prices(limit: int = 10) -> List[Dict]:
    """Fonction helper pour récupérer les derniers prix"""
    return improved_db_saver.get_latest_prices(limit)

if __name__ == "__main__":
    # Test du module
    print("🧪 TEST MODULE SAUVEGARDE")
    print("=" * 40)
    
    # Test sauvegarde
    test_data = {
        'company_id': 468,
        'symbol': 'BOA',
        'company_name': 'Bank of Africa',
        'date_trade': date.today(),
        'open_price': Decimal('180.00'),
        'high_price': Decimal('185.00'),
        'low_price': Decimal('178.00'),
        'close_price': Decimal('182.50'),
        'volume': 12500,
        'value_mad': Decimal('2281250.00')
    }
    
    success = save_company_data(test_data)
    print(f"Sauvegarde réussie: {success}")
    
    # Test récupération
    companies = get_companies_from_database()
    print(f"Entreprises en BDD: {len(companies)}")
    
    prices = get_latest_prices(5)
    print(f"Derniers prix: {len(prices)}")
    for price in prices:
        print(f"  {price['symbol']}: {price['price']} MAD ({price['date']})")
'''
    
    print("\n4️⃣ Création module sauvegarde amélioré:")
    with open('improved_database_saver.py', 'w', encoding='utf-8') as f:
        f.write(database_saver_code)
    print("   ✅ Module improved_database_saver.py créé")
    
    return True

def main():
    """Test principal"""
    
    # 1. Test de la base de données
    db_test_ok = test_database_insertion()
    
    # 2. Créer le module amélioré
    module_created = create_improved_database_saver()
    
    # 3. Test du nouveau module
    if module_created:
        print("\n5️⃣ Test du module amélioré:")
        try:
            exec(open('improved_database_saver.py').read())
            print("   ✅ Module amélioré fonctionne")
        except Exception as e:
            print(f"   ❌ Erreur module amélioré: {e}")
    
    print(f"\n🎯 RÉSULTAT: Base de données {'✅ OPÉRATIONNELLE' if db_test_ok else '❌ PROBLÈME'}")
    
    if db_test_ok:
        print("\n📋 PROCHAINES ÉTAPES:")
        print("1. Utiliser improved_database_saver.py pour les sauvegardes")
        print("2. Mettre à jour improved_daily_orchestrator.py")
        print("3. Tester le système complet")

if __name__ == "__main__":
    main()
