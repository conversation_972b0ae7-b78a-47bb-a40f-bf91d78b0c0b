"""
Vues API pour le système de scraping Casablanca Stock Exchange
"""

from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from rest_framework.renderers import TemplateHTMLRenderer
from rest_framework.views import APIView
from celery.result import AsyncResult
from django.utils import timezone
from django.shortcuts import render
from django.db.models import Count, Q
from datetime import timed<PERSON>ta

from .tasks import (
    run_casablanca_scraper,
    test_casablanca_api_connectivity,
    cleanup_old_data,
    run_scraper,
    test_connection,
    check_tables
)
from .models import ScrapedItem, CasablancaApiExecution


@api_view(['POST'])
def trigger_test_scraper(request):
    """
    API endpoint pour déclencher manuellement le scraper de test (Phase 2)
    """
    try:
        # Lancer la tâche de test scraping
        task = run_scraper.delay()
        
        return Response({
            'status': 'success',
            'message': '🚀 Scraper de test Phase 2 démarré avec succès',
            'phase': 'Phase 2 - Test Simple',
            'task_id': task.id,
            'check_status_url': f'/api/scraper/task-status/{task.id}/',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_202_ACCEPTED)
        
    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'❌ Erreur lors du démarrage du scraper de test: {str(e)}',
            'phase': 'Phase 2 - Test Simple'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def trigger_casablanca_scraper(request):
    """
    API endpoint pour déclencher manuellement le scraper Casablanca
    """
    try:
        # Lancer la tâche de scraping Casablanca
        task = run_casablanca_scraper.delay()

        return Response({
            'status': 'success',
            'message': '🚀 Scraper Casablanca Stock Exchange démarré avec succès',
            'task_id': task.id,
            'check_status_url': f'/api/scraper/task-status/{task.id}/',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_202_ACCEPTED)

    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'❌ Erreur lors du démarrage du scraper Casablanca: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def test_casablanca_connectivity(request):
    """
    API endpoint pour tester la connectivité avec l'API Casablanca
    """
    try:
        # Lancer le test de connectivité
        task = test_casablanca_api_connectivity.delay()

        return Response({
            'status': 'success',
            'message': '🔍 Test de connectivité Casablanca démarré',
            'task_id': task.id,
            'check_status_url': f'/api/scraper/task-status/{task.id}/',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_202_ACCEPTED)

    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'❌ Erreur lors du test de connectivité: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def casablanca_dashboard(request):
    """
    API endpoint pour le dashboard de monitoring Casablanca
    """
    try:
        # Statistiques des dernières 24h
        last_24h = timezone.now() - timedelta(hours=24)

        # Dernière exécution
        last_execution = CasablancaApiExecution.objects.order_by('-started_at').first()

        # Statistiques des items scrapés
        total_items = ScrapedItem.objects.filter(scraper_type='casablanca_api').count()
        recent_items = ScrapedItem.objects.filter(
            scraper_type='casablanca_api',
            extracted_at__gte=last_24h
        ).count()

        # Statistiques par statut
        status_stats = {}
        for status_choice in ['success', 'failed', 'pending']:
            status_stats[status_choice] = ScrapedItem.objects.filter(
                scraper_type='casablanca_api',
                status=status_choice
            ).count()

        # Dernières exécutions
        recent_executions = CasablancaApiExecution.objects.order_by('-started_at')[:10]

        dashboard_data = {
            'status': 'success',
            'timestamp': timezone.now().isoformat(),
            'last_execution': {
                'task_id': last_execution.task_id if last_execution else None,
                'status': last_execution.status if last_execution else None,
                'started_at': last_execution.started_at.isoformat() if last_execution else None,
                'companies_processed': last_execution.companies_processed if last_execution else 0,
                'success_rate': last_execution.success_rate if last_execution else 0,
            } if last_execution else None,
            'statistics': {
                'total_items': total_items,
                'recent_items_24h': recent_items,
                'status_breakdown': status_stats,
                'total_executions': CasablancaApiExecution.objects.count(),
                'successful_executions': CasablancaApiExecution.objects.filter(status='completed').count(),
            },
            'recent_executions': [
                {
                    'task_id': exec.task_id[:12] + '...' if len(exec.task_id) > 12 else exec.task_id,
                    'status': exec.status,
                    'started_at': exec.started_at.isoformat(),
                    'companies_processed': exec.companies_processed,
                    'success_rate': exec.success_rate,
                }
                for exec in recent_executions
            ]
        }

        return Response(dashboard_data, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'❌ Erreur lors de la récupération du dashboard: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def dashboard_view(request):
    """
    Vue HTML pour le dashboard de monitoring Casablanca
    """
    return render(request, 'scraper/dashboard.html')


@api_view(['POST'])
def test_celery_connection(request):
    """
    API endpoint pour tester la connexion Celery simple
    """
    try:
        # Lancer une tâche simple de test de connexion
        task = test_connection.delay()
        
        return Response({
            'status': 'success',
            'message': '🔗 Test de connexion Celery démarré',
            'task_id': task.id,
            'check_status_url': f'/api/scraper/task-status/{task.id}/',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_202_ACCEPTED)
        
    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'❌ Erreur lors du test de connexion: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def test_database_tables(request):
    """
    API endpoint pour tester les tables de la base de données
    """
    try:
        # Lancer la tâche de vérification des tables
        task = check_tables.delay()
        
        return Response({
            'status': 'success',
            'message': '🗃️ Test des tables XCapitalTerminal démarré',
            'task_id': task.id,
            'check_status_url': f'/api/scraper/task-status/{task.id}/',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_202_ACCEPTED)
        
    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'❌ Erreur lors du test des tables: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def task_status(request, task_id):
    """
    API endpoint pour vérifier le statut d'une tâche Celery
    """
    try:
        # Vérifier le statut avec Celery
        result = AsyncResult(task_id)
        
        response_data = {
            'task_id': task_id,
            'status': result.status,
            'ready': result.ready(),
            'timestamp': timezone.now().isoformat()
        }
        
        if result.ready():
            if result.successful():
                response_data['result'] = result.result
                response_data['message'] = '✅ Tâche terminée avec succès'
            else:
                response_data['error'] = str(result.result)
                response_data['message'] = '❌ Tâche terminée avec erreur'
        else:
            response_data['info'] = result.info
            response_data['message'] = '⏳ Tâche en cours d\'exécution'
        
        return Response(response_data)
        
    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'❌ Erreur lors de la vérification du statut: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def phase2_status(request):
    """
    API endpoint pour obtenir le statut de la Phase 2
    """
    try:
        from django.core.cache import cache
        from django.db import connection
        
        status_info = {
            'phase': 'Phase 2 - Test Simple',
            'timestamp': timezone.now().isoformat(),
            'components': {},
            'overall_status': 'healthy',
            'next_steps': []
        }
        
        # Test de la base de données
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                status_info['components']['database'] = {
                    'status': 'ok',
                    'message': '✅ Base de données PostgreSQL accessible'
                }
        except Exception as e:
            status_info['components']['database'] = {
                'status': 'error',
                'message': f'❌ Erreur base de données: {str(e)}'
            }
            status_info['overall_status'] = 'unhealthy'
        
        # Test du cache Redis
        try:
            cache.set('phase2_test', 'ok', 10)
            if cache.get('phase2_test') == 'ok':
                status_info['components']['redis'] = {
                    'status': 'ok',
                    'message': '✅ Cache Redis accessible'
                }
            else:
                status_info['components']['redis'] = {
                    'status': 'error',
                    'message': '❌ Cache Redis non fonctionnel'
                }
                status_info['overall_status'] = 'unhealthy'
        except Exception as e:
            status_info['components']['redis'] = {
                'status': 'error',
                'message': f'❌ Erreur Redis: {str(e)}'
            }
            status_info['overall_status'] = 'unhealthy'
        
        # Test Celery
        try:
            from celery import current_app
            i = current_app.control.inspect()
            active_workers = i.active()
            if active_workers:
                status_info['components']['celery'] = {
                    'status': 'ok',
                    'message': f'✅ Celery workers actifs: {len(active_workers)}'
                }
            else:
                status_info['components']['celery'] = {
                    'status': 'warning',
                    'message': '⚠️ Aucun worker Celery actif'
                }
        except Exception as e:
            status_info['components']['celery'] = {
                'status': 'error',
                'message': f'❌ Erreur Celery: {str(e)}'
            }
            status_info['overall_status'] = 'unhealthy'
        
        # Prochaines étapes recommandées
        if status_info['overall_status'] == 'healthy':
            status_info['next_steps'] = [
                "✅ Phase 1 & 2 complètes",
                "🚀 Prêt pour la Phase 3",
                "📝 Définir les spécifications de la Phase 3",
                "🔧 Implémenter la logique métier pour Company Bonds"
            ]
        else:
            status_info['next_steps'] = [
                "🔧 Corriger les composants en erreur",
                "🔄 Relancer les tests",
                "📋 Vérifier la configuration"
            ]
        
        return Response(status_info)
        
    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'❌ Erreur lors de la vérification: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
