#!/usr/bin/env python3
"""
Test simple de l'API pour Bank of Africa (391)
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
django.setup()

import requests
import json
from daily_updater.config import BASE_URL, DEFAULT_HEADERS

def test_api_direct():
    """Test direct de l'API pour la Bank of Africa"""
    
    print("🌐 TEST API DIRECT - BANK OF AFRICA (391)")
    print("=" * 50)
    
    # Configuration de la requête
    company_id = 391
    url = f"{BASE_URL}/negotiation/rech/fiche-detaille/{company_id}"
    
    print(f"URL: {url}")
    print(f"Headers: {DEFAULT_HEADERS}")
    
    try:
        print("\n📡 Envoi de la requête...")
        response = requests.get(url, headers=DEFAULT_HEADERS, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'N/A')}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ Données JSON reçues")
                print(f"Taille des données: {len(str(data))} caractères")
                
                # Afficher les premiers éléments
                if isinstance(data, dict):
                    print(f"Clés principales: {list(data.keys())[:5]}")
                elif isinstance(data, list):
                    print(f"Nombre d'éléments: {len(data)}")
                    if data:
                        print(f"Premier élément: {list(data[0].keys())[:5] if isinstance(data[0], dict) else str(data[0])[:100]}")
                
                # Chercher des informations sur l'entreprise
                if isinstance(data, dict):
                    company_name = data.get('name', data.get('nom', data.get('entreprise', 'Bank of Africa')))
                    print(f"Nom de l'entreprise: {company_name}")
                    
                    if 'price' in str(data).lower() or 'prix' in str(data).lower():
                        print("💰 Données de prix trouvées dans la réponse")
                
                return True
                
            except json.JSONDecodeError:
                print(f"❌ Erreur: Réponse non JSON")
                print(f"Contenu (100 premiers caractères): {response.text[:100]}")
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            print(f"Réponse: {response.text[:200]}")
            
    except requests.exceptions.Timeout:
        print("❌ Timeout de la requête")
    except requests.exceptions.ConnectionError:
        print("❌ Erreur de connexion")
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    return False

if __name__ == "__main__":
    print("🚀 Démarrage du test API Bank of Africa...")
    success = test_api_direct()
    print(f"\n{'✅ SUCCÈS' if success else '❌ ÉCHEC'}")
