#!/usr/bin/env python3
"""
Test complet du système de mise à jour quotidienne automatique
"""

import os
import sys
from datetime import datetime, date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

try:
    import django
    django.setup()
    print("✅ Django configuré avec succès")
except Exception as e:
    print(f"❌ Erreur configuration Django: {e}")
    sys.exit(1)

def test_system():
    print("🧪 TEST COMPLET DU SYSTÈME")
    print("=" * 60)
    
    # 1. Test de l'orchestrateur amélioré
    print("\n1️⃣ Test Orchestrateur Amélioré:")
    try:
        from improved_daily_orchestrator import improved_orchestrator
        
        # Statut du système
        status = improved_orchestrator.get_system_status()
        print(f"   📊 Django disponible: {status['django_available']}")
        print(f"   📊 Database disponible: {status['database_available']}")
        print(f"   📊 Redis disponible: {status['redis_available']}")
        print(f"   📊 Mock generator disponible: {status['mock_generator_available']}")
        print(f"   📊 Entreprises en BDD: {status['companies_in_database']}")
        print(f"   📊 Données prix en BDD: {status['bonds_in_database']}")
        
        orchestrator_ok = True
        
    except Exception as e:
        print(f"   ❌ Erreur orchestrateur: {e}")
        orchestrator_ok = False
    
    # 2. Test entreprises individuelles
    print("\n2️⃣ Test Entreprises Individuelles:")
    test_companies = [
        (468, "Bank of Africa"),
        (511, "Attijariwafa Bank"),
        (385, "Lafargeholcim Maroc")
    ]
    
    individual_tests_ok = True
    
    for company_id, company_name in test_companies:
        try:
            result = improved_orchestrator.test_single_company(company_id)
            
            if result['success']:
                print(f"   ✅ {company_name} ({company_id}): {result['closing_price']} MAD, Vol: {result['volume']}, BDD: {result['database_saved']}")
            else:
                print(f"   ❌ {company_name} ({company_id}): {result['error']}")
                individual_tests_ok = False
                
        except Exception as e:
            print(f"   ❌ {company_name} ({company_id}): Erreur {e}")
            individual_tests_ok = False
    
    # 3. Test mise à jour complète (mode test)
    print("\n3️⃣ Test Mise à Jour Complète (Mode Test):")
    try:
        result = improved_orchestrator.run_daily_update(test_mode=True)
        
        if result['success']:
            print(f"   ✅ Mise à jour réussie!")
            print(f"   📊 Entreprises traitées: {result['companies_processed']}")
            print(f"   📊 Entreprises réussies: {result['companies_successful']}")
            print(f"   📊 Entreprises échouées: {result['companies_failed']}")
            print(f"   📊 Sauvegardes BDD: {result['database_saves']}")
            print(f"   📊 Durée: {result['duration_seconds']:.2f}s")
            print(f"   📊 Date cible: {result['target_date']}")
        else:
            print(f"   ❌ Mise à jour échouée")
            print(f"   📊 Erreurs: {len(result.get('errors', []))}")
        
        batch_test_ok = result['success']
        
    except Exception as e:
        print(f"   ❌ Erreur mise à jour: {e}")
        batch_test_ok = False
    
    # 4. Test de la planification
    print("\n4️⃣ Test Planification Celery:")
    try:
        from django_celery_beat.models import PeriodicTask
        
        daily_tasks = PeriodicTask.objects.filter(name__icontains='daily')
        print(f"   📅 Tâches quotidiennes configurées: {daily_tasks.count()}")
        
        for task in daily_tasks:
            print(f"   📅 Tâche: {task.name} - Activée: {task.enabled} - Prochaine: {task.last_run_at}")
        
        schedule_ok = daily_tasks.count() > 0
        
    except Exception as e:
        print(f"   ❌ Erreur planification: {e}")
        schedule_ok = False
    
    # 5. Test base de données avec nouveau système
    print("\n5️⃣ Test Base de Données Améliorée:")
    try:
        from improved_database_saver import save_company_data, get_companies_from_database, get_latest_prices
        from decimal import Decimal
        
        # Test sauvegarde directe
        test_data = {
            'company_id': 468,
            'symbol': 'BOA',
            'company_name': 'Bank of Africa',
            'date_trade': date.today(),
            'open_price': Decimal('180.00'),
            'high_price': Decimal('185.00'),
            'low_price': Decimal('178.00'),
            'close_price': Decimal('182.50'),
            'volume': 12500,
            'value_mad': Decimal('2281250.00')
        }
        
        save_success = save_company_data(test_data)
        print(f"   💾 Test sauvegarde directe: {'✅ Succès' if save_success else '❌ Échec'}")
        
        companies = get_companies_from_database()
        prices = get_latest_prices(5)
        
        companies_count = len(companies)
        bonds_count = len(prices)
        
        print(f"   💾 Entreprises en BDD: {companies_count}")
        print(f"   💾 Données prix en BDD: {bonds_count}")
        
        # Afficher quelques exemples récents
        print(f"   💾 Dernières données:")
        for price in prices[:3]:
            print(f"      {price['symbol']} ({price['date']}): {price['price']} MAD, Vol: {price['volume']}")
        
        database_ok = save_success and companies_count > 0
        
    except Exception as e:
        print(f"   ❌ Erreur base de données améliorée: {e}")
        # Fallback vers l'ancienne méthode
        try:
            from market_data.postgres_models import XCapitalCompany, XCapitalCompanyBond
            
            companies_count = XCapitalCompany.objects.count()
            bonds_count = XCapitalCompanyBond.objects.count()
            
            print(f"   💾 Entreprises en BDD (fallback): {companies_count}")
            print(f"   💾 Données prix en BDD (fallback): {bonds_count}")
            
            database_ok = companies_count > 0
            
        except Exception as e2:
            print(f"   ❌ Erreur base de données (fallback): {e2}")
            database_ok = False
    
    # 6. Résumé final
    print("\n6️⃣ RÉSUMÉ FINAL:")
    print("=" * 40)
    
    tests = [
        ("Orchestrateur", orchestrator_ok),
        ("Tests individuels", individual_tests_ok),
        ("Mise à jour complète", batch_test_ok),
        ("Planification", schedule_ok),
        ("Base de données", database_ok)
    ]
    
    passed = sum(1 for _, ok in tests if ok)
    total = len(tests)
    
    for test_name, ok in tests:
        status = "✅ PASSÉ" if ok else "❌ ÉCHEC"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 RÉSULTAT: {passed}/{total} tests passés")
    
    if passed == total:
        print("🎉 SYSTÈME OPÉRATIONNEL! Prêt pour mise à jour automatique à 19:00")
        return True
    else:
        print("⚠️ Système partiellement opérationnel. Vérifier les erreurs.")
        return False

if __name__ == "__main__":
    success = test_system()
    
    if success:
        print("\n📋 INSTRUCTIONS FINALES:")
        print("1. Démarrer Redis: redis-server")
        print("2. Démarrer Celery worker: celery -A xcapital_backend worker --loglevel=info")
        print("3. Démarrer Celery beat: celery -A xcapital_backend beat --loglevel=info")
        print("4. La mise à jour automatique aura lieu chaque jour à 19:00")
        print("5. Les données seront sauvées dans XCapitalTerminal_Companies et XCapitalTerminal_CompanyBonds")
    
    sys.exit(0 if success else 1)
