# Generated by Django 4.2.23 on 2025-09-10 17:44

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ScraperExecution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_id', models.CharField(help_text='ID de la tâche Celery', max_length=255, unique=True)),
                ('status', models.CharField(choices=[('running', 'En cours'), ('completed', 'Terminé'), ('failed', 'Échec'), ('cancelled', 'Annulé')], default='running', max_length=20)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('total_urls', models.IntegerField(default=0)),
                ('successful_scrapes', models.IntegerField(default=0)),
                ('failed_scrapes', models.IntegerField(default=0)),
                ('logs', models.TextField(blank=True)),
                ('error_details', models.TextField(blank=True)),
            ],
            options={
                'verbose_name': 'Scraper Execution',
                'verbose_name_plural': 'Scraper Executions',
                'db_table': 'scraper_executions',
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='ScraperTarget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Nom descriptif de la source', max_length=200)),
                ('url', models.URLField(help_text='URL à scraper', max_length=500)),
                ('is_active', models.BooleanField(default=True, help_text='Si False, cette URL sera ignorée lors du scraping')),
                ('scraper_type', models.CharField(choices=[('html', 'HTML Simple'), ('javascript', 'Nécessite JavaScript'), ('api', 'API Endpoint')], default='html', help_text='Type de scraper à utiliser', max_length=50)),
                ('css_selectors', models.JSONField(blank=True, default=dict, help_text='Sélecteurs CSS pour extraire les données (JSON)')),
                ('headers', models.JSONField(blank=True, default=dict, help_text='Headers HTTP personnalisés (JSON)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Scraper Target',
                'verbose_name_plural': 'Scraper Targets',
                'db_table': 'scraper_targets',
            },
        ),
        migrations.CreateModel(
            name='ScrapedItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_url', models.URLField(help_text="URL source de l'élément scrapé", max_length=500)),
                ('title', models.CharField(help_text='Titre extrait de la page', max_length=500)),
                ('summary', models.TextField(blank=True, help_text='Résumé ou description extraite')),
                ('raw_html', models.TextField(blank=True, help_text='HTML brut de la page (optionnel pour debug)')),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('processing', 'En cours'), ('success', 'Succès'), ('failed', 'Échec'), ('skipped', 'Ignoré')], default='pending', help_text='Statut du scraping', max_length=20)),
                ('extracted_at', models.DateTimeField(auto_now_add=True, help_text="Date et heure d'extraction")),
                ('meta', models.JSONField(blank=True, default=dict, help_text='Métadonnées supplémentaires (JSON)')),
                ('error_message', models.TextField(blank=True, help_text="Message d'erreur en cas d'échec")),
                ('retry_count', models.IntegerField(default=0, help_text='Nombre de tentatives de retry')),
                ('target', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='scraped_items', to='scraper.scrapertarget')),
            ],
            options={
                'verbose_name': 'Scraped Item',
                'verbose_name_plural': 'Scraped Items',
                'db_table': 'scraped_items',
                'ordering': ['-extracted_at'],
                'indexes': [models.Index(fields=['status'], name='scraped_ite_status_cd3b15_idx'), models.Index(fields=['extracted_at'], name='scraped_ite_extract_ec22b9_idx'), models.Index(fields=['source_url'], name='scraped_ite_source__ed1527_idx')],
            },
        ),
    ]
