#!/usr/bin/env python3
"""
Test Spécifique: Fetch et Sauvegarde Données dans Base de Données
"""

import os
import sys
from datetime import datetime, date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

try:
    import django
    django.setup()
    print("✅ Django configuré avec succès")
except Exception as e:
    print(f"❌ Erreur configuration Django: {e}")
    sys.exit(1)

def test_fetch_and_save():
    """Test complet: génération → sauvegarde → vérification"""
    
    print("🧪 TEST FETCH ET SAUVEGARDE DONNÉES")
    print("=" * 50)
    
    try:
        from improved_daily_orchestrator import improved_orchestrator
        from improved_database_saver import get_companies_from_database, get_latest_prices
        
        # 1. État initial de la base de données
        print("\n1️⃣ État initial base de données:")
        initial_companies = get_companies_from_database()
        initial_prices = get_latest_prices(5)
        
        print(f"   Entreprises existantes: {len(initial_companies)}")
        print(f"   Derniers prix: {len(initial_prices)}")
        
        if initial_prices:
            print("   Dernières données:")
            for price in initial_prices[:3]:
                print(f"      {price['symbol']}: {price['price']} MAD ({price['date']})")
        
        # 2. Test avec Bank of Africa (468)
        print(f"\n2️⃣ Test génération et sauvegarde Bank of Africa (468):")
        
        result = improved_orchestrator.test_single_company(468)
        
        if result['success']:
            print(f"   ✅ Génération réussie:")
            print(f"      Entreprise: {result['company_name']}")
            print(f"      Prix: {result['closing_price']} MAD")
            print(f"      Volume: {result['volume']}")
            print(f"      Sauvé en BDD: {result['database_saved']}")
            print(f"      Source: {result['data_source']}")
        else:
            print(f"   ❌ Génération échouée: {result['error']}")
            return False
        
        # 3. Vérification immédiate après sauvegarde
        print(f"\n3️⃣ Vérification après sauvegarde:")
        
        new_companies = get_companies_from_database()
        new_prices = get_latest_prices(5)
        
        print(f"   Entreprises maintenant: {len(new_companies)}")
        print(f"   Prix maintenant: {len(new_prices)}")
        
        # Chercher Bank of Africa dans les données
        boa_found = False
        for company in new_companies:
            if company['company_id'] == '468' or company['symbol'] == 'BOA':
                print(f"   ✅ Bank of Africa trouvée: {company['symbol']} ({company['company_id']})")
                boa_found = True
                break
        
        if not boa_found:
            print(f"   ❌ Bank of Africa non trouvée dans la base")
        
        # Chercher les prix récents de Bank of Africa
        boa_price_found = False
        today = date.today()
        
        for price in new_prices:
            if price['symbol'] == 'BOA' and str(price['date']) == str(today):
                print(f"   ✅ Prix BOA aujourd'hui: {price['price']} MAD, Volume: {price['volume']}")
                boa_price_found = True
                break
        
        if not boa_price_found:
            print(f"   ⚠️ Prix BOA d'aujourd'hui non trouvé")
            print(f"   Derniers prix disponibles:")
            for price in new_prices:
                print(f"      {price['symbol']}: {price['price']} MAD ({price['date']})")
        
        # 4. Test avec plusieurs entreprises
        print(f"\n4️⃣ Test avec plusieurs entreprises:")
        
        test_companies = [511, 385, 498]  # Attijariwafa, Lafargeholcim, BMCE
        
        for company_id in test_companies:
            print(f"   Test entreprise {company_id}...")
            
            result = improved_orchestrator.test_single_company(company_id)
            
            if result['success']:
                print(f"      ✅ {result['company_name']}: {result['closing_price']} MAD (BDD: {result['database_saved']})")
            else:
                print(f"      ❌ Échec: {result['error']}")
        
        # 5. Vérification finale
        print(f"\n5️⃣ Vérification finale:")
        
        final_companies = get_companies_from_database()
        final_prices = get_latest_prices(10)
        
        print(f"   Total entreprises: {len(final_companies)}")
        print(f"   Total prix récents: {len(final_prices)}")
        
        # Montrer les dernières données par date
        print(f"   Dernières données par date:")
        
        from collections import defaultdict
        prices_by_date = defaultdict(list)
        
        for price in final_prices:
            prices_by_date[str(price['date'])].append(price)
        
        for date_str in sorted(prices_by_date.keys(), reverse=True):
            prices_for_date = prices_by_date[date_str]
            print(f"      {date_str}: {len(prices_for_date)} entreprises")
            for price in prices_for_date[:3]:  # Montrer 3 exemples
                print(f"         {price['symbol']}: {price['price']} MAD")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_update():
    """Test mise à jour en lot"""
    
    print(f"\n6️⃣ Test mise à jour en lot (mode test):")
    
    try:
        from improved_daily_orchestrator import improved_orchestrator
        
        result = improved_orchestrator.run_daily_update(test_mode=True)
        
        if result['success']:
            print(f"   ✅ Mise à jour en lot réussie!")
            print(f"      Entreprises traitées: {result['companies_processed']}")
            print(f"      Entreprises réussies: {result['companies_successful']}")
            print(f"      Sauvegardes BDD: {result['database_saves']}")
            print(f"      Durée: {result['duration_seconds']:.2f}s")
        else:
            print(f"   ❌ Mise à jour en lot échouée")
            if result.get('errors'):
                print(f"      Erreurs: {len(result['errors'])}")
                for error in result['errors'][:3]:
                    print(f"         {error}")
        
        return result['success']
        
    except Exception as e:
        print(f"❌ Erreur mise à jour en lot: {e}")
        return False

def main():
    """Test principal"""
    
    print("🎯 TEST COMPLET FETCH ET SAUVEGARDE")
    print("=" * 60)
    
    # Test 1: Fetch et sauvegarde individuels
    individual_test = test_fetch_and_save()
    
    # Test 2: Mise à jour en lot
    batch_test = test_batch_update()
    
    # Résumé
    print(f"\n📊 RÉSUMÉ DES TESTS:")
    print(f"   Tests individuels: {'✅ PASSÉ' if individual_test else '❌ ÉCHEC'}")
    print(f"   Test en lot: {'✅ PASSÉ' if batch_test else '❌ ÉCHEC'}")
    
    if individual_test and batch_test:
        print(f"\n🎉 SYSTÈME OPÉRATIONNEL!")
        print(f"   ✅ Les données sont correctement générées")
        print(f"   ✅ Les données sont sauvées dans XCapitalTerminal_Companies")
        print(f"   ✅ Les données sont sauvées dans XCapitalTerminal_CompanyBonds")
        print(f"   ✅ La relation company_id fonctionne correctement")
        
        print(f"\n📋 PROCHAINES ÉTAPES:")
        print(f"   1. Le système est prêt pour la mise à jour automatique à 19:00")
        print(f"   2. Démarrer les services: Redis, Celery Worker, Celery Beat")
        print(f"   3. Surveiller les logs pour les mises à jour quotidiennes")
        
        return True
    else:
        print(f"\n⚠️ PROBLÈMES DÉTECTÉS - Vérifier les erreurs ci-dessus")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
