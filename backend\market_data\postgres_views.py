"""
Vues Django pour les APIs XCapital utilisant PostgreSQL
"""
from rest_framework import generics, status, filters
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Sum, Avg, Max, Min
from django.db import models
from datetime import datetime, timedelta, date
from django.shortcuts import get_object_or_404
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.utils import timezone
from decimal import Decimal

from .postgres_models import XCapitalCompany, XCapitalCompanyBond, XCapitalIndex, XCapitalIndexValue
from .postgres_serializers import (
    XCapitalCompanySerializer, XCapitalCompanyBondSerializer,
    XCapitalPriceDataRequestSerializer, XCapitalPriceVariationSerializer,
    XCapitalMarketSummarySerializer, XCapitalIndexSerializer, XCapitalIndexValueSerializer,
    XCapitalIndexDataRequestSerializer, XCapitalIndexVariationSerializer,
    XCapitalIndexSummarySerializer
)


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 200


# =========================
# APIS POUR LES ENTREPRISES
# =========================

@api_view(['GET'])
def simple_companies_list(request):
    """
    API simplifiée pour récupérer la liste des entreprises
    Retourne uniquement les informations de base sans calculs de prix
    """
    try:
        companies = XCapitalCompany.objects.all().values(
            'symbol', 'company_id', 'nom_francais', 'nom_arabe',
            'nom_anglais', 'created_at', 'updated_at'
        ).order_by('symbol')

        return Response({
            'success': True,
            'total_companies': len(companies),
            'companies': list(companies),
            'data_source': 'XCapitalTerminal_Companies table',
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error in simple_companies_list: {e}")
        return Response({
            'error': 'Erreur lors de la récupération des entreprises',
            'message': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class XCapitalCompanyListView(generics.ListAPIView):
    """Liste toutes les entreprises de la base PostgreSQL (version complète)"""
    queryset = XCapitalCompany.objects.all()
    serializer_class = XCapitalCompanySerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['symbol', 'nom_francais', 'nom_anglais', 'company_id']
    ordering_fields = ['symbol', 'nom_francais', 'created_at']
    ordering = ['symbol']


class XCapitalCompanyDetailView(generics.RetrieveAPIView):
    """Détails d'une entreprise spécifique"""
    queryset = XCapitalCompany.objects.all()
    serializer_class = XCapitalCompanySerializer
    lookup_field = 'symbol'


# =========================
# APIS POUR LES PRIX (BONDS)
# =========================

class XCapitalCompanyBondListView(generics.ListAPIView):
    """Liste les données de prix pour une entreprise"""
    serializer_class = XCapitalCompanyBondSerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['date_trade']
    ordering_fields = ['date_trade']
    ordering = ['-date_trade']
    
    def get_queryset(self):
        symbol = self.kwargs['symbol']
        company = get_object_or_404(XCapitalCompany, symbol=symbol)
        
        queryset = XCapitalCompanyBond.objects.filter(company=company)
        
        # Filtrage par période
        period = self.request.query_params.get('period', None)
        if period:
            end_date = date.today()
            if period == '1M':
                start_date = end_date - timedelta(days=30)
            elif period == '3M':
                start_date = end_date - timedelta(days=90)
            elif period == '6M':
                start_date = end_date - timedelta(days=180)
            elif period == '1Y':
                start_date = end_date - timedelta(days=365)
            elif period == '3Y':
                start_date = end_date - timedelta(days=365*3)
            else:
                start_date = None
            
            if start_date:
                queryset = queryset.filter(date_trade__gte=start_date)
        
        return queryset


@api_view(['GET'])
def get_xcapital_chart_data(request, symbol):
    """Données pour graphiques d'une entreprise XCapital"""
    try:
        company = get_object_or_404(XCapitalCompany, symbol=symbol)
        
        # Paramètres de période
        period = request.GET.get('period', '1M')
        
        # Calculer la date de début selon la période
        end_date = date.today()
        if period == '1M':
            start_date = end_date - timedelta(days=30)
        elif period == '3M':
            start_date = end_date - timedelta(days=90)
        elif period == '6M':
            start_date = end_date - timedelta(days=180)
        elif period == '1Y':
            start_date = end_date - timedelta(days=365)
        elif period == '3Y':
            start_date = end_date - timedelta(days=365*3)
        else:  # ALL
            start_date = None
        
        # Récupérer les prix
        queryset = company.bonds.all()
        if start_date:
            queryset = queryset.filter(date_trade__gte=start_date)
        
        bonds = queryset.order_by('date_trade')
        
        # Préparer les données pour le graphique
        chart_data = {
            'symbol': symbol,
            'label': company.display_name,
            'period': period,
            'data': [
                {
                    'date': bond.date_trade.isoformat(),
                    'open': float(bond.open_price) if bond.open_price else 0,
                    'high': float(bond.high_price) if bond.high_price else 0,
                    'low': float(bond.low_price) if bond.low_price else 0,
                    'close': float(bond.close_price) if bond.close_price else 0,
                    'volume': bond.volume or 0
                }
                for bond in bonds
            ]
        }
        
        return Response(chart_data)
        
    except XCapitalCompany.DoesNotExist:
        return Response({'error': 'Company not found'}, status=404)


@api_view(['GET'])
def get_xcapital_price_variations(request, symbol):
    """API pour récupérer les variations de prix d'une entreprise XCapital"""
    try:
        company = get_object_or_404(XCapitalCompany, symbol=symbol)
        
        # Paramètres de période
        period = request.GET.get('period', 'ALL')
        
        # Calculer la date de début selon la période
        end_date = date.today()
        if period == '1M':
            start_date = end_date - timedelta(days=30)
        elif period == '3M':
            start_date = end_date - timedelta(days=90)
        elif period == '6M':
            start_date = end_date - timedelta(days=180)
        elif period == '1Y':
            start_date = end_date - timedelta(days=365)
        elif period == '3Y':
            start_date = end_date - timedelta(days=365*3)
        elif period == 'SINCE_2022':
            start_date = date(2022, 8, 15)
        else:  # ALL
            start_date = None
        
        # Récupérer les prix
        queryset = company.bonds.all()
        if start_date:
            queryset = queryset.filter(date_trade__gte=start_date)
        
        bonds = queryset.order_by('date_trade')
        
        if not bonds.exists():
            return Response({
                'error': 'No price data available for this company',
                'symbol': symbol,
                'period': period
            }, status=404)
        
        # Calculer les variations
        price_data = []
        previous_close = None
        first_price = bonds.first().close_price if bonds.first().close_price else Decimal('0')
        
        for bond in bonds:
            # Calcul de variation journalière
            daily_change = Decimal('0')
            daily_change_pct = Decimal('0')
            
            if previous_close and bond.close_price:
                daily_change = bond.close_price - previous_close
                daily_change_pct = (daily_change / previous_close) * 100
            
            # Calcul de variation depuis le début de la période
            total_change = bond.close_price - first_price if bond.close_price else Decimal('0')
            total_change_pct = (total_change / first_price) * 100 if first_price else Decimal('0')
            
            price_data.append({
                'date': bond.date_trade.isoformat(),
                'open': float(bond.open_price) if bond.open_price else 0,
                'high': float(bond.high_price) if bond.high_price else 0,
                'low': float(bond.low_price) if bond.low_price else 0,
                'close': float(bond.close_price) if bond.close_price else 0,
                'volume': bond.volume or 0,
                'daily_change': round(float(daily_change), 4),
                'daily_change_pct': round(float(daily_change_pct), 2),
                'total_change': round(float(total_change), 4),
                'total_change_pct': round(float(total_change_pct), 2)
            })
            
            if bond.close_price:
                previous_close = bond.close_price
        
        # Calculs de résumé
        last_bond = bonds.last()
        last_price = last_bond.close_price if last_bond.close_price else Decimal('0')
        
        # Statistiques
        close_prices = [float(b.close_price) for b in bonds if b.close_price]
        high_prices = [float(b.high_price) for b in bonds if b.high_price]
        low_prices = [float(b.low_price) for b in bonds if b.low_price]
        volumes = [b.volume for b in bonds if b.volume]
        
        summary = {
            'symbol': symbol,
            'label': company.display_name,
            'period': period,
            'start_date': bonds.first().date_trade.isoformat() if bonds.first() else None,
            'end_date': last_bond.date_trade.isoformat() if last_bond else None,
            'first_price': float(first_price),
            'last_price': float(last_price),
            'total_change': float(last_price - first_price),
            'total_change_pct': round(float((last_price - first_price) / first_price * 100), 2) if first_price else 0,
            'highest_price': max(high_prices) if high_prices else 0,
            'lowest_price': min(low_prices) if low_prices else 0,
            'average_price': round(sum(close_prices) / len(close_prices), 2) if close_prices else 0,
            'total_volume': sum(volumes) if volumes else 0,
            'data_points': len(price_data),
            'data_source': 'PostgreSQL Database'
        }
        
        response_data = {
            'summary': summary,
            'variations': price_data
        }
        
        return Response(response_data)
        
    except XCapitalCompany.DoesNotExist:
        return Response({'error': 'Company not found'}, status=404)
    except Exception as e:
        return Response({'error': str(e)}, status=500)


@api_view(['GET', 'POST'])
@csrf_exempt
def get_xcapital_data_form(request):
    """
    API POST pour récupérer les données d'une entreprise via formulaire
    Utilise la base PostgreSQL au lieu des fichiers CSV
    """
    
    # Si c'est une requête GET, retourner la documentation
    if request.method == 'GET':
        # Récupérer quelques symboles disponibles
        available_symbols = list(XCapitalCompany.objects.values_list('symbol', flat=True)[:20])
        
        return Response({
            'message': 'API POST pour récupérer les données d\'entreprises XCapital',
            'endpoint': '/api/v1/xcapital/companies/form-data/',
            'method': 'POST',
            'database': 'PostgreSQL AWS RDS',
            'parameters': {
                'symbol': 'Symbole de l\'entreprise (obligatoire)',
                'period': 'Période: 1M, 3M, 6M, 1Y, 3Y, ALL ou CUSTOM',
                'start_date': 'Date de début (format YYYY-MM-DD, si period=CUSTOM)',
                'end_date': 'Date de fin (format YYYY-MM-DD, si period=CUSTOM)'
            },
            'constraints': {
                'min_start_date': '2022-08-15',
                'max_end_date': 'Aujourd\'hui'
            },
            'available_symbols_sample': available_symbols,
            'total_companies': XCapitalCompany.objects.count()
        })
    
    # Validation des données du formulaire
    # Normaliser payload (strip espaces)
    mutable_data = request.data.copy() if hasattr(request.data, 'copy') else dict(request.data)
    if 'symbol' in mutable_data and isinstance(mutable_data['symbol'], str):
        mutable_data['symbol'] = mutable_data['symbol'].strip()
    serializer = XCapitalPriceDataRequestSerializer(data=mutable_data)
    if not serializer.is_valid():
        return Response({
            'error': 'Données du formulaire invalides',
            'details': serializer.errors
        }, status=400)
    
    validated_data = serializer.validated_data
    symbol = validated_data['symbol'].strip() if isinstance(validated_data['symbol'], str) else validated_data['symbol']
    period = validated_data['period']
    start_date = validated_data.get('start_date')
    end_date = validated_data.get('end_date', date.today())
    
    try:
        # Récupérer l'entreprise
        company = get_object_or_404(XCapitalCompany, symbol=symbol)
        
        # Calculer les dates selon la période
        if period != 'CUSTOM':
            end_date = date.today()
            if period == '1M':
                start_date = end_date - timedelta(days=30)
            elif period == '3M':
                start_date = end_date - timedelta(days=90)
            elif period == '6M':
                start_date = end_date - timedelta(days=180)
            elif period == '1Y':
                start_date = end_date - timedelta(days=365)
            elif period == '3Y':
                start_date = end_date - timedelta(days=365*3)
            else:  # ALL
                start_date = date(2022, 8, 15)  # Date minimale
        
        # Récupérer les données de la base PostgreSQL
        queryset = company.bonds.all()
        if start_date:
            queryset = queryset.filter(date_trade__gte=start_date)
        if end_date:
            queryset = queryset.filter(date_trade__lte=end_date)
        
        bonds = queryset.order_by('date_trade')
        
        if not bonds.exists():
            return Response({
                'error': f'Aucune donnée trouvée pour {symbol} dans la période demandée',
                'symbol': symbol,
                'period': period,
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            }, status=404)
        
        # Préparer les données pour la réponse
        price_data = []
        previous_close = None
        first_price = bonds.first().close_price if bonds.first().close_price else Decimal('0')
        
        for bond in bonds:
            close_price = bond.close_price or Decimal('0')
            
            # Calcul de variation journalière
            daily_change = Decimal('0')
            daily_change_pct = Decimal('0')
            if previous_close:
                daily_change = close_price - previous_close
                daily_change_pct = (daily_change / previous_close) * 100 if previous_close else Decimal('0')
            
            # Calcul de variation totale
            total_change = close_price - first_price
            total_change_pct = (total_change / first_price) * 100 if first_price else Decimal('0')
            
            price_data.append({
                'date': bond.date_trade.isoformat(),
                'open': float(bond.open_price) if bond.open_price else 0,
                'high': float(bond.high_price) if bond.high_price else 0,
                'low': float(bond.low_price) if bond.low_price else 0,
                'close': float(close_price),
                'volume': bond.volume or 0,
                'daily_change': round(float(daily_change), 4),
                'daily_change_pct': round(float(daily_change_pct), 2),
                'total_change': round(float(total_change), 4),
                'total_change_pct': round(float(total_change_pct), 2)
            })
            
            previous_close = close_price
        
        # Statistiques de résumé
        last_bond = bonds.last()
        close_prices = [float(b.close_price) for b in bonds if b.close_price]
        high_prices = [float(b.high_price) for b in bonds if b.high_price]
        low_prices = [float(b.low_price) for b in bonds if b.low_price]
        volumes = [b.volume for b in bonds if b.volume]
        
        summary = {
            'symbol': symbol,
            'label': company.display_name,
            'period': period,
            'start_date': bonds.first().date_trade.isoformat(),
            'end_date': last_bond.date_trade.isoformat(),
            'first_price': float(first_price),
            'last_price': float(last_bond.close_price) if last_bond.close_price else 0,
            'total_change': float(last_bond.close_price - first_price) if last_bond.close_price else 0,
            'total_change_pct': round(float((last_bond.close_price - first_price) / first_price * 100), 2) if (last_bond.close_price and first_price) else 0,
            'highest_price': max(high_prices) if high_prices else 0,
            'lowest_price': min(low_prices) if low_prices else 0,
            'average_price': round(sum(close_prices) / len(close_prices), 2) if close_prices else 0,
            'total_volume': sum(volumes) if volumes else 0,
            'data_points': len(price_data),
            'data_source': 'PostgreSQL Database'
        }
        
        return Response({
            'success': True,
            'summary': summary,
            'data': price_data
        })
    
    except XCapitalCompany.DoesNotExist:
        return Response({'error': 'Company not found'}, status=404)
    except Exception as e:
        return Response({'error': str(e)}, status=500)


@api_view(['GET'])
def get_xcapital_market_overview(request):
    """Vue d'ensemble du marché XCapital"""
    try:
        total_companies = XCapitalCompany.objects.count()
        total_bonds = XCapitalCompanyBond.objects.count()
        
        # Entreprises avec des données récentes (derniers 30 jours)
        recent_date = date.today() - timedelta(days=30)
        companies_with_recent_data = XCapitalCompany.objects.filter(
            bonds__date_trade__gte=recent_date
        ).distinct().count()
        
        # Dernière date de trading
        latest_trade_date = XCapitalCompanyBond.objects.aggregate(
            Max('date_trade')
        )['date_trade__max']
        
        # Volume total récent
        recent_volume = XCapitalCompanyBond.objects.filter(
            date_trade__gte=recent_date
        ).aggregate(Sum('volume'))['volume__sum'] or 0
        
        # Top 5 des entreprises par volume récent
        top_companies = XCapitalCompany.objects.filter(
            bonds__date_trade__gte=recent_date
        ).annotate(
            recent_volume=Sum('bonds__volume')
        ).order_by('-recent_volume')[:5]
        
        return Response({
            'market_overview': {
                'total_companies': total_companies,
                'total_price_records': total_bonds,
                'companies_with_recent_data': companies_with_recent_data,
                'latest_trade_date': latest_trade_date,
                'recent_total_volume': recent_volume,
                'data_source': 'PostgreSQL Database'
            },
            'top_companies_by_volume': [
                {
                    'symbol': company.symbol,
                    'name': company.display_name,
                    'recent_volume': company.recent_volume or 0
                }
                for company in top_companies
            ],
            'timestamp': datetime.now().isoformat()
        })
    
    except Exception as e:
        return Response({'error': str(e)}, status=500)


@api_view(['GET'])
def get_xcapital_available_symbols(request):
    """Liste tous les symboles disponibles"""
    try:
        companies = XCapitalCompany.objects.all().order_by('symbol')
        
        symbols_data = []
        for company in companies:
            # Compter les données de prix pour cette entreprise
            price_count = company.bonds.count()
            latest_bond = company.bonds.order_by('-date_trade').first()
            
            symbols_data.append({
                'symbol': company.symbol,
                'company_id': company.company_id,
                'nom_francais': company.nom_francais,
                'nom_anglais': company.nom_anglais,
                'price_records': price_count,
                'latest_date': latest_bond.date_trade if latest_bond else None,
                'latest_price': float(latest_bond.close_price) if (latest_bond and latest_bond.close_price) else None
            })
        
        return Response({
            'symbols': symbols_data,
            'total_count': len(symbols_data),
            'data_source': 'PostgreSQL Database'
        })
    
    except Exception as e:
        return Response({'error': str(e)}, status=500)


# =========================
# API SÉCURISÉE POUR PÉRIODE PERSONNALISÉE
# =========================

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import logging

# Configuration du logging pour la sécurité
logger = logging.getLogger(__name__)

@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
@csrf_exempt
def secure_company_price_data(request):
    """
    API GET/POST sécurisée pour récupérer les données de prix d'une entreprise
    Période par défaut : 2022-08-22 à aujourd'hui

    GET: Retourne les instructions d'utilisation
    POST: Récupère les données selon le format JSON:
    {
        "company_symbol": "CIH",
        "start_date": "2024-01-01" (optionnel),
        "end_date": "2024-12-31" (optionnel)
    }
    """
    # Logging de la requête pour la sécurité
    client_ip = request.META.get('HTTP_X_FORWARDED_FOR', request.META.get('REMOTE_ADDR', 'Unknown'))
    logger.info(f"Secure API Request from IP: {client_ip} - Method: {request.method}")

    if request.method == 'GET':
        # Retourner les instructions et exemples d'utilisation
        try:
            sample_companies = list(XCapitalCompany.objects.values_list('symbol', flat=True)[:10])
            total_companies = XCapitalCompany.objects.count()
            total_records = XCapitalCompanyBond.objects.count()
            
            return Response({
                'success': True,
                'api_info': {
                    'name': 'API XCapital Sécurisée',
                    'version': '1.0',
                    'description': 'Récupération sécurisée des données de prix des entreprises',
                    'methods': ['GET', 'POST'],
                    'default_period': {
                        'start': '2022-08-22',
                        'end': 'today'
                    }
                },
                'usage': {
                    'method': 'POST',
                    'content_type': 'application/json',
                    'url': '/api/v1/xcapital/secure/company-data/',
                    'required_fields': ['company_symbol'],
                    'optional_fields': ['start_date', 'end_date']
                },
                'example_request': {
                    'company_symbol': 'ATW',
                    'start_date': '2022-08-22',
                    'end_date': '2025-08-27'
                },
                'database_stats': {
                    'total_companies': total_companies,
                    'total_price_records': total_records,
                    'sample_companies': sample_companies
                },
                'security_features': [
                    'Validation stricte des entrées',
                    'Limite de 1000 enregistrements',
                    'Logging des requêtes',
                    'Protection CSRF'
                ],
                'timestamp': timezone.now().isoformat()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': 'Erreur lors de la récupération des informations',
                'message': str(e),
                'timestamp': timezone.now().isoformat()
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    elif request.method == 'POST':
        try:
            # Validation du Content-Type
            if request.content_type != 'application/json':
                return Response({
                    'error': 'Content-Type non supporté',
                    'message': 'Veuillez utiliser application/json',
                    'required_content_type': 'application/json'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Récupération sécurisée des données
            company_symbol = request.data.get('company_symbol', '').strip().upper()
            start_date = request.data.get('start_date', '2022-08-22')  # Valeur par défaut
            end_date = request.data.get('end_date', timezone.now().date().strftime('%Y-%m-%d'))
            
            # Validation stricte du symbole d'entreprise
            if not company_symbol:
                return Response({
                    'error': 'Symbole d\'entreprise requis',
                    'message': 'Le champ company_symbol est obligatoire',
                    'example': {'company_symbol': 'ATW'}
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validation de la longueur du symbole (sécurité)
            if len(company_symbol) > 10:
                return Response({
                    'error': 'Symbole d\'entreprise invalide',
                    'message': 'Le symbole ne peut pas dépasser 10 caractères',
                    'provided_symbol': company_symbol
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validation des caractères du symbole (sécurité)
            if not company_symbol.isalnum():
                return Response({
                    'error': 'Symbole d\'entreprise invalide',
                    'message': 'Le symbole ne peut contenir que des lettres et chiffres',
                    'provided_symbol': company_symbol
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validation des dates (sécurité)
            try:
                from datetime import datetime
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                
                # Vérification de la logique des dates
                if start_date_obj > end_date_obj:
                    return Response({
                        'error': 'Dates invalides',
                        'message': 'La date de début doit être antérieure à la date de fin',
                        'start_date': start_date,
                        'end_date': end_date
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # Validation de la période autorisée (2022-08-22 à aujourd'hui)
                min_allowed_date = datetime.strptime('2022-08-22', '%Y-%m-%d').date()
                max_allowed_date = timezone.now().date()
                
                if start_date_obj < min_allowed_date:
                    return Response({
                        'error': 'Date de début non autorisée',
                        'message': f'La date de début ne peut pas être antérieure au {min_allowed_date}',
                        'min_allowed_date': str(min_allowed_date),
                        'provided_start_date': start_date
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if end_date_obj > max_allowed_date:
                    return Response({
                        'error': 'Date de fin non autorisée',
                        'message': f'La date de fin ne peut pas être postérieure à aujourd\'hui ({max_allowed_date})',
                        'max_allowed_date': str(max_allowed_date),
                        'provided_end_date': end_date
                    }, status=status.HTTP_400_BAD_REQUEST)
                
            except ValueError as e:
                return Response({
                    'error': 'Format de date invalide',
                    'message': 'Utilisez le format YYYY-MM-DD',
                    'example': {'start_date': '2022-08-22', 'end_date': '2025-08-27'},
                    'error_detail': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Vérification sécurisée de l'existence de l'entreprise
            try:
                company = XCapitalCompany.objects.get(symbol=company_symbol)
                logger.info(f"Company found: {company_symbol} - {company.nom_francais}")
            except XCapitalCompany.DoesNotExist:
                # Ne pas révéler la liste complète pour la sécurité
                available_sample = list(XCapitalCompany.objects.values_list('symbol', flat=True)[:10])
                return Response({
                    'error': f'Entreprise "{company_symbol}" non trouvée',
                    'message': 'Vérifiez le symbole de l\'entreprise',
                    'sample_companies': available_sample,
                    'total_available': XCapitalCompany.objects.count()
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Recherche intelligente des dates disponibles
            # Si les dates exactes n'existent pas, on trouve les plus proches
            all_company_dates = XCapitalCompanyBond.objects.filter(
                company=company
            ).values_list('date_trade', flat=True).order_by('date_trade')
            
            if not all_company_dates.exists():
                return Response({
                    'success': True,
                    'message': f'Aucune donnée de prix trouvée pour l\'entreprise {company_symbol}',
                    'company_info': {
                        'symbol': company.symbol,
                        'nom_francais': company.nom_francais,
                        'company_id': company.company_id
                    },
                    'period': {
                        'requested_start_date': start_date,
                        'requested_end_date': end_date,
                        'total_records': 0
                    },
                    'data': []
                }, status=status.HTTP_200_OK)
            
            # Trouver la date de début la plus proche (>= date demandée)
            closest_start_date = all_company_dates.filter(date_trade__gte=start_date_obj).first()
            if not closest_start_date:
                # Si aucune date >= start_date, prendre la première date disponible
                closest_start_date = all_company_dates.first()
            
            # Trouver la date de fin la plus proche (<= date demandée)
            closest_end_date = all_company_dates.filter(date_trade__lte=end_date_obj).last()
            if not closest_end_date:
                # Si aucune date <= end_date, prendre la dernière date disponible
                closest_end_date = all_company_dates.last()
            
            # Vérification de sécurité - s'assurer qu'on a trouvé des dates
            if not closest_start_date or not closest_end_date:
                return Response({
                    'success': True,
                    'message': f'Impossible de trouver des dates appropriées pour {company_symbol}',
                    'company_info': {
                        'symbol': company.symbol,
                        'nom_francais': company.nom_francais,
                        'company_id': company.company_id
                    },
                    'period': {
                        'requested_start_date': start_date,
                        'requested_end_date': end_date,
                        'total_records': 0
                    },
                    'available_dates_info': {
                        'total_available_dates': all_company_dates.count(),
                        'first_available_date': str(all_company_dates.first()) if all_company_dates.exists() else None,
                        'last_available_date': str(all_company_dates.last()) if all_company_dates.exists() else None
                    },
                    'data': []
                }, status=status.HTTP_200_OK)
            
            # Récupération de TOUTES les données disponibles pour la période (sans limite)
            price_data = XCapitalCompanyBond.objects.filter(
                company=company,
                date_trade__gte=closest_start_date,
                date_trade__lte=closest_end_date
            ).order_by('date_trade')
            
            total_records = price_data.count()
            logger.info(f"Retrieved {total_records} records for {company_symbol} from {closest_start_date} to {closest_end_date}")
            
            if total_records == 0:
                return Response({
                    'success': True,
                    'message': f'Aucune donnée trouvée pour {company_symbol} dans la période disponible',
                    'company_info': {
                        'symbol': company.symbol,
                        'nom_francais': company.nom_francais,
                        'company_id': company.company_id
                    },
                    'period': {
                        'requested_start_date': start_date,
                        'requested_end_date': end_date,
                        'actual_start_date': str(closest_start_date) if closest_start_date else None,
                        'actual_end_date': str(closest_end_date) if closest_end_date else None,
                        'total_records': 0
                    },
                    'data': []
                }, status=status.HTTP_200_OK)
            
            # Formatage sécurisé des données
            formatted_data = []
            for bond in price_data:
                try:
                    formatted_data.append({
                        'date': bond.date_trade.strftime('%Y-%m-%d'),
                        'open_price': float(bond.open_price) if bond.open_price else None,
                        'high_price': float(bond.high_price) if bond.high_price else None,
                        'low_price': float(bond.low_price) if bond.low_price else None,
                        'close_price': float(bond.close_price) if bond.close_price else None,
                        'current_price': float(bond.current_price) if bond.current_price else None,
                        'volume': bond.volume if bond.volume else 0,
                        'shares_traded': bond.shares_traded if bond.shares_traded else 0,
                        'total_trades': bond.total_trades if bond.total_trades else 0,
                        'market_cap': float(bond.market_cap) if bond.market_cap else None,
                        'daily_change': float(bond.daily_change) if bond.daily_change else 0.0,
                        'daily_change_pct': float(bond.daily_change_pct) if bond.daily_change_pct else 0.0
                    })
                except (ValueError, TypeError) as e:
                    logger.warning(f"Data formatting error for {bond.date_trade}: {e}")
                    continue
            
            # Calcul sécurisé des statistiques
            try:
                # Vérifier qu'on a des données
                if price_data.exists():
                    first_record = price_data.first()
                    last_record = price_data.last()
                    
                    first_price = first_record.close_price if first_record and first_record.close_price else Decimal('0')
                    last_price = last_record.close_price if last_record and last_record.close_price else Decimal('0')
                    
                    period_change = last_price - first_price if first_price and last_price else Decimal('0')
                    period_change_pct = (period_change / first_price * 100) if first_price else Decimal('0')
                    
                    # Utilisation d'agrégations Django pour la sécurité
                    aggregates = price_data.aggregate(
                        max_price=models.Max('high_price'),
                        min_price=models.Min('low_price'),
                        avg_volume=models.Avg('volume')
                    )
                    
                    max_price = aggregates['max_price'] or Decimal('0')
                    min_price = aggregates['min_price'] or Decimal('0')
                    avg_volume = aggregates['avg_volume'] or Decimal('0')
                else:
                    # Aucune donnée trouvée
                    first_price = last_price = period_change = period_change_pct = Decimal('0')
                    max_price = min_price = avg_volume = Decimal('0')
                
            except Exception as e:
                logger.error(f"Statistics calculation error: {e}")
                # Valeurs par défaut en cas d'erreur
                first_price = last_price = period_change = period_change_pct = Decimal('0')
                max_price = min_price = avg_volume = Decimal('0')
            
            # Réponse sécurisée
            # Réponse sécurisée avec toutes les informations
            response_data = {
                'success': True,
                'timestamp': timezone.now().isoformat(),
                'company_info': {
                    'symbol': company.symbol,
                    'nom_francais': company.nom_francais,
                    'nom_arabe': company.nom_arabe if company.nom_arabe else None,
                    'nom_anglais': company.nom_anglais if company.nom_anglais else None,
                    'company_id': company.company_id
                },
                'period': {
                    'requested_start_date': start_date,
                    'requested_end_date': end_date,
                    'actual_start_date': str(closest_start_date),
                    'actual_end_date': str(closest_end_date),
                    'total_records': len(formatted_data),
                    'date_adjustment_info': {
                        'start_date_adjusted': str(closest_start_date) != start_date,
                        'end_date_adjusted': str(closest_end_date) != end_date,
                        'message': 'Dates ajustées aux données disponibles les plus proches' if (str(closest_start_date) != start_date or str(closest_end_date) != end_date) else 'Dates exactes trouvées'
                    }
                },
                'period_statistics': {
                    'first_price': float(first_price),
                    'last_price': float(last_price),
                    'period_change': float(period_change),
                    'period_change_pct': float(period_change_pct),
                    'max_price': float(max_price),
                    'min_price': float(min_price),
                    'avg_volume': float(avg_volume)
                },
                'data': formatted_data
            }
            
            logger.info(f"Successful API response for {company_symbol}: {len(formatted_data)} records")
            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Unexpected error in secure_company_price_data: {e}")
            return Response({
                'error': 'Erreur interne du serveur',
                'message': str(e),
                'timestamp': timezone.now().isoformat(),
                'support_contact': 'Contactez le support technique'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    elif request.method == 'POST':
        try:
            # Validation du Content-Type
            if request.content_type != 'application/json':
                return Response({
                    'error': 'Content-Type non supporté',
                    'message': 'Veuillez utiliser application/json',
                    'required_content_type': 'application/json'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Récupération des données du formulaire JSON
            company_symbol = request.data.get('company_symbol', '').strip().upper()
            start_date = request.data.get('start_date', '2022-08-22')  # Valeur par défaut
            end_date = request.data.get('end_date', timezone.now().date().strftime('%Y-%m-%d'))

            # Validation des données requises
            if not company_symbol:
                return Response({
                    'error': 'Le symbole de l\'entreprise est requis',
                    'message': 'Veuillez fournir company_symbol',
                    'example': {'company_symbol': 'ATW'}
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validation des dates
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

                if start_date_obj > end_date_obj:
                    return Response({
                        'error': 'Dates invalides',
                        'message': 'La date de début doit être antérieure à la date de fin',
                        'start_date': start_date,
                        'end_date': end_date
                    }, status=status.HTTP_400_BAD_REQUEST)

            except ValueError as e:
                return Response({
                    'error': 'Format de date invalide',
                    'message': 'Utilisez le format YYYY-MM-DD',
                    'example': {'start_date': '2022-08-22', 'end_date': '2024-12-31'},
                    'error_detail': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)

            # Vérifier que l'entreprise existe
            try:
                company = XCapitalCompany.objects.get(symbol=company_symbol)
                logger.info(f"Company found: {company_symbol} - {company.nom_francais}")
            except XCapitalCompany.DoesNotExist:
                available_sample = list(XCapitalCompany.objects.values_list('symbol', flat=True)[:10])
                return Response({
                    'error': f'Entreprise "{company_symbol}" non trouvée',
                    'message': 'Vérifiez le symbole de l\'entreprise',
                    'sample_companies': available_sample,
                    'total_available': XCapitalCompany.objects.count()
                }, status=status.HTTP_404_NOT_FOUND)

            # Recherche intelligente des dates disponibles
            company_bonds_queryset = XCapitalCompanyBond.objects.filter(company=company)

            if not company_bonds_queryset.exists():
                return Response({
                    'success': True,
                    'message': f'Aucune donnée de prix trouvée pour l\'entreprise {company_symbol}',
                    'company_info': {
                        'symbol': company.symbol,
                        'nom_francais': company.nom_francais,
                        'company_id': company.company_id
                    },
                    'period': {
                        'requested_start_date': start_date,
                        'requested_end_date': end_date,
                        'total_records': 0
                    },
                    'data': []
                }, status=status.HTTP_200_OK)

            # Trouver les dates les plus proches
            closest_start_bond = company_bonds_queryset.filter(date_trade__gte=start_date_obj).order_by('date_trade').first()
            if closest_start_bond:
                closest_start_date = closest_start_bond.date_trade
            else:
                closest_start_date = company_bonds_queryset.order_by('date_trade').first().date_trade

            closest_end_bond = company_bonds_queryset.filter(date_trade__lte=end_date_obj).order_by('-date_trade').first()
            if closest_end_bond:
                closest_end_date = closest_end_bond.date_trade
            else:
                closest_end_date = company_bonds_queryset.order_by('-date_trade').first().date_trade

            # Récupération des données de prix pour la période
            price_data = XCapitalCompanyBond.objects.filter(
                company=company,
                date_trade__gte=closest_start_date,
                date_trade__lte=closest_end_date
            ).order_by('date_trade')

            total_records = price_data.count()
            logger.info(f"Retrieved {total_records} records for {company_symbol}")

            if total_records == 0:
                return Response({
                    'success': True,
                    'message': f'Aucune donnée trouvée pour {company_symbol} dans la période disponible',
                    'company_info': {
                        'symbol': company.symbol,
                        'nom_francais': company.nom_francais,
                        'company_id': company.company_id
                    },
                    'period': {
                        'requested_start_date': start_date,
                        'requested_end_date': end_date,
                        'actual_start_date': str(closest_start_date) if closest_start_date else None,
                        'actual_end_date': str(closest_end_date) if closest_end_date else None,
                        'total_records': 0
                    },
                    'data': []
                }, status=status.HTTP_200_OK)

            # Formatage des données
            formatted_data = []
            for bond in price_data:
                try:
                    formatted_data.append({
                        'date': bond.date_trade.strftime('%Y-%m-%d'),
                        'open_price': float(bond.open_price) if bond.open_price else None,
                        'high_price': float(bond.high_price) if bond.high_price else None,
                        'low_price': float(bond.low_price) if bond.low_price else None,
                        'close_price': float(bond.close_price) if bond.close_price else None,
                        'current_price': float(bond.current_price) if bond.current_price else None,
                        'volume': bond.volume if bond.volume else 0,
                        'shares_traded': bond.shares_traded if bond.shares_traded else 0,
                        'total_trades': bond.total_trades if bond.total_trades else 0,
                        'market_cap': float(bond.market_cap) if bond.market_cap else None
                    })
                except (ValueError, TypeError) as e:
                    logger.warning(f"Data formatting error for {bond.date_trade}: {e}")
                    continue

            # Calcul des statistiques
            try:
                if price_data.exists():
                    first_record = price_data.first()
                    last_record = price_data.last()

                    first_price = first_record.close_price if first_record and first_record.close_price else Decimal('0')
                    last_price = last_record.close_price if last_record and last_record.close_price else Decimal('0')

                    period_change = last_price - first_price if first_price and last_price else Decimal('0')
                    period_change_pct = (period_change / first_price * 100) if first_price else Decimal('0')

                    # Utilisation d'agrégations Django
                    aggregates = price_data.aggregate(
                        max_price=models.Max('high_price'),
                        min_price=models.Min('low_price'),
                        avg_volume=models.Avg('volume')
                    )

                    max_price = aggregates['max_price'] or Decimal('0')
                    min_price = aggregates['min_price'] or Decimal('0')
                    avg_volume = aggregates['avg_volume'] or Decimal('0')
                else:
                    first_price = last_price = period_change = period_change_pct = Decimal('0')
                    max_price = min_price = avg_volume = Decimal('0')

            except Exception as e:
                logger.error(f"Statistics calculation error: {e}")
                first_price = last_price = period_change = period_change_pct = Decimal('0')
                max_price = min_price = avg_volume = Decimal('0')

            # Réponse finale
            response_data = {
                'success': True,
                'timestamp': timezone.now().isoformat(),
                'company_info': {
                    'symbol': company.symbol,
                    'nom_francais': company.nom_francais,
                    'nom_arabe': company.nom_arabe if company.nom_arabe else None,
                    'nom_anglais': company.nom_anglais if company.nom_anglais else None,
                    'company_id': company.company_id
                },
                'period': {
                    'requested_start_date': start_date,
                    'requested_end_date': end_date,
                    'actual_start_date': str(closest_start_date),
                    'actual_end_date': str(closest_end_date),
                    'total_records': len(formatted_data),
                    'date_adjustment_info': {
                        'start_date_adjusted': str(closest_start_date) != start_date,
                        'end_date_adjusted': str(closest_end_date) != end_date,
                        'message': 'Dates ajustées aux données disponibles les plus proches' if (str(closest_start_date) != start_date or str(closest_end_date) != end_date) else 'Dates exactes trouvées'
                    }
                },
                'period_statistics': {
                    'first_price': float(first_price),
                    'last_price': float(last_price),
                    'period_change': float(period_change),
                    'period_change_pct': float(period_change_pct),
                    'max_price': float(max_price),
                    'min_price': float(min_price),
                    'avg_volume': float(avg_volume)
                },
                'data': formatted_data
            }

            logger.info(f"Successful API response for {company_symbol}: {len(formatted_data)} records")
            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Unexpected error in secure_company_price_data POST: {e}")
            return Response({
                'error': 'Erreur interne du serveur',
                'message': str(e),
                'timestamp': timezone.now().isoformat(),
                'support_contact': 'Contactez le support technique'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def available_companies_list(request):
    """
    API pour récupérer la liste de toutes les entreprises disponibles
    """
    try:
        companies = XCapitalCompany.objects.all().values(
            'symbol', 'nom_francais', 'nom_anglais', 'company_id'
        )
        
        return Response({
            'success': True,
            'total_companies': len(companies),
            'companies': list(companies)
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response({
            'error': 'Erreur lors de la récupération des entreprises',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
@csrf_exempt
def clean_company_price_data(request):
    """
    API nettoyée pour récupérer les données de prix d'une entreprise
    Version sans problèmes de champs calculés
    """
    client_ip = request.META.get('HTTP_X_FORWARDED_FOR', request.META.get('REMOTE_ADDR', 'Unknown'))
    logger.info(f"Clean API Request from IP: {client_ip} - Method: {request.method}")

    if request.method == 'GET':
        return Response({
            'success': True,
            'api_info': {
                'name': 'API XCapital Clean',
                'version': '1.0',
                'description': 'API nettoyée pour les données de prix d\'entreprise',
                'methods': ['GET', 'POST'],
                'post_format': {
                    'company_symbol': 'string (required)',
                    'start_date': 'YYYY-MM-DD (optional, default: 2022-08-22)',
                    'end_date': 'YYYY-MM-DD (optional, default: today)'
                }
            },
            'database_stats': {
                'total_companies': XCapitalCompany.objects.count(),
                'total_price_records': XCapitalCompanyBond.objects.count(),
                'data_source': 'PostgreSQL Database'
            },
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_200_OK)

    elif request.method == 'POST':
        try:
            # Récupération et validation des données
            company_symbol = request.data.get('company_symbol', '').strip().upper()
            start_date = request.data.get('start_date', '2022-08-22')
            end_date = request.data.get('end_date', timezone.now().date().strftime('%Y-%m-%d'))

            if not company_symbol:
                return Response({
                    'error': 'Symbole d\'entreprise requis',
                    'message': 'Veuillez fournir company_symbol',
                    'example': {'company_symbol': 'CIH', 'start_date': '2024-01-01', 'end_date': '2024-12-31'}
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validation des dates
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                return Response({
                    'error': 'Format de date invalide',
                    'message': 'Utilisez le format YYYY-MM-DD'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Vérifier l'entreprise
            try:
                company = XCapitalCompany.objects.get(symbol=company_symbol)
            except XCapitalCompany.DoesNotExist:
                available_companies = list(XCapitalCompany.objects.values_list('symbol', flat=True)[:10])
                return Response({
                    'error': f'Entreprise "{company_symbol}" non trouvée',
                    'available_companies': available_companies
                }, status=status.HTTP_404_NOT_FOUND)

            # Récupération des données de prix (requête simple)
            price_data = XCapitalCompanyBond.objects.filter(
                company=company,
                date_trade__gte=start_date_obj,
                date_trade__lte=end_date_obj
            ).order_by('date_trade').values(
                'date_trade', 'open_price', 'high_price', 'low_price',
                'close_price', 'current_price', 'volume', 'shares_traded',
                'total_trades', 'market_cap'
            )

            # Conversion en liste pour éviter les problèmes de QuerySet
            formatted_data = []
            for record in price_data:
                formatted_data.append({
                    'date': record['date_trade'].strftime('%Y-%m-%d'),
                    'open_price': float(record['open_price']) if record['open_price'] else None,
                    'high_price': float(record['high_price']) if record['high_price'] else None,
                    'low_price': float(record['low_price']) if record['low_price'] else None,
                    'close_price': float(record['close_price']) if record['close_price'] else None,
                    'current_price': float(record['current_price']) if record['current_price'] else None,
                    'volume': record['volume'] if record['volume'] else 0,
                    'shares_traded': record['shares_traded'] if record['shares_traded'] else 0,
                    'total_trades': record['total_trades'] if record['total_trades'] else 0,
                    'market_cap': float(record['market_cap']) if record['market_cap'] else None
                })

            # Calcul des statistiques simples
            if formatted_data:
                prices = [r['close_price'] for r in formatted_data if r['close_price'] is not None]
                if prices:
                    first_price = prices[0]
                    last_price = prices[-1]
                    max_price = max(r['high_price'] for r in formatted_data if r['high_price'] is not None)
                    min_price = min(r['low_price'] for r in formatted_data if r['low_price'] is not None)
                    avg_volume = sum(r['volume'] for r in formatted_data) / len(formatted_data)

                    period_change = last_price - first_price
                    period_change_pct = (period_change / first_price * 100) if first_price else 0
                else:
                    first_price = last_price = max_price = min_price = avg_volume = 0
                    period_change = period_change_pct = 0
            else:
                first_price = last_price = max_price = min_price = avg_volume = 0
                period_change = period_change_pct = 0

            # Réponse finale
            response_data = {
                'success': True,
                'timestamp': timezone.now().isoformat(),
                'company_info': {
                    'symbol': company.symbol,
                    'nom_francais': company.nom_francais,
                    'nom_arabe': company.nom_arabe if hasattr(company, 'nom_arabe') else None,
                    'nom_anglais': company.nom_anglais if hasattr(company, 'nom_anglais') else None,
                    'company_id': company.company_id
                },
                'period': {
                    'requested_start_date': start_date,
                    'requested_end_date': end_date,
                    'actual_start_date': formatted_data[0]['date'] if formatted_data else None,
                    'actual_end_date': formatted_data[-1]['date'] if formatted_data else None,
                    'total_records': len(formatted_data)
                },
                'period_statistics': {
                    'first_price': first_price,
                    'last_price': last_price,
                    'period_change': period_change,
                    'period_change_pct': round(period_change_pct, 2),
                    'max_price': max_price,
                    'min_price': min_price,
                    'avg_volume': round(avg_volume, 2)
                },
                'data': formatted_data
            }

            logger.info(f"Clean API success for {company_symbol}: {len(formatted_data)} records")
            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Clean API error: {e}")
            return Response({
                'error': 'Erreur interne du serveur',
                'message': str(e),
                'timestamp': timezone.now().isoformat()
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# =========================
# APIS POUR LES INDICES
# =========================

@api_view(['GET'])
def simple_indices_list(request):
    """
    API simplifiée pour récupérer la liste des indices
    Retourne uniquement les informations de base sans calculs de valeurs
    """
    try:
        indices = XCapitalIndex.objects.all().values(
            'index_id', 'index_name', 'created_at', 'updated_at'
        ).order_by('index_name')

        return Response({
            'success': True,
            'total_indices': len(indices),
            'indices': list(indices)
        })
    except Exception as e:
        return Response({'error': str(e)}, status=500)


class XCapitalIndexListView(generics.ListAPIView):
    """Liste complète des indices avec détails et calculs"""
    queryset = XCapitalIndex.objects.all()
    serializer_class = XCapitalIndexSerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['index_id', 'index_name']
    ordering_fields = ['index_id', 'index_name', 'created_at']
    ordering = ['index_name']


class XCapitalIndexDetailView(generics.RetrieveAPIView):
    """Détails d'un indice spécifique"""
    queryset = XCapitalIndex.objects.all()
    serializer_class = XCapitalIndexSerializer
    lookup_field = 'index_id'


class XCapitalIndexValueListView(generics.ListAPIView):
    """Liste les données de valeurs pour un indice"""
    serializer_class = XCapitalIndexValueSerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['date_value']
    ordering_fields = ['date_value']
    ordering = ['-date_value']

    def get_queryset(self):
        index_id = self.kwargs['index_id']
        index = get_object_or_404(XCapitalIndex, index_id=index_id)
        
        queryset = XCapitalIndexValue.objects.filter(index_table=index)
        
        # Filtrage par période
        period = self.request.query_params.get('period', None)
        if period:
            end_date = date.today()
            if period == '1M':
                start_date = end_date - timedelta(days=30)
            elif period == '3M':
                start_date = end_date - timedelta(days=90)
            elif period == '6M':
                start_date = end_date - timedelta(days=180)
            elif period == '1Y':
                start_date = end_date - timedelta(days=365)
            elif period == '3Y':
                start_date = end_date - timedelta(days=365*3)
            else:
                start_date = None
            
            if start_date:
                queryset = queryset.filter(date_value__gte=start_date)
        
        return queryset


@api_view(['GET'])
def get_xcapital_index_chart_data(request, index_id):
    """Données pour graphiques d'un indice XCapital"""
    try:
        index = get_object_or_404(XCapitalIndex, index_id=index_id)
        
        # Paramètres de période
        period = request.GET.get('period', '1M')
        
        # Calculer la date de début selon la période
        end_date = date.today()
        if period == '1M':
            start_date = end_date - timedelta(days=30)
        elif period == '3M':
            start_date = end_date - timedelta(days=90)
        elif period == '6M':
            start_date = end_date - timedelta(days=180)
        elif period == '1Y':
            start_date = end_date - timedelta(days=365)
        elif period == '3Y':
            start_date = end_date - timedelta(days=365*3)
        else:  # ALL
            start_date = None
        
        # Récupérer les valeurs
        queryset = index.values.all()
        if start_date:
            queryset = queryset.filter(date_value__gte=start_date)
        
        values = queryset.order_by('date_value')
        
        # Préparer les données pour le graphique
        chart_data = {
            'index_id': index_id,
            'index_name': index.index_name,
            'period': period,
            'data': [
                {
                    'date': value.date_value.isoformat(),
                    'value': float(value.value) if value.value else 0,
                    'previous_value': float(value.previous_value) if value.previous_value else 0,
                    'daily_change': float(value.daily_change) if value.daily_change else 0,
                    'daily_change_pct': float(value.daily_change_pct) if value.daily_change_pct else 0
                }
                for value in values
            ]
        }
        
        return Response(chart_data)
        
    except XCapitalIndex.DoesNotExist:
        return Response({'error': 'Index not found'}, status=404)
    except Exception as e:
        return Response({'error': str(e)}, status=500)


@api_view(['GET'])
def get_xcapital_index_variations(request, index_id):
    """API pour récupérer les variations d'un indice XCapital"""
    try:
        index = get_object_or_404(XCapitalIndex, index_id=index_id)
        
        # Paramètres de période
        period = request.GET.get('period', 'ALL')
        
        # Calculer la date de début selon la période
        end_date = date.today()
        if period == '1M':
            start_date = end_date - timedelta(days=30)
        elif period == '3M':
            start_date = end_date - timedelta(days=90)
        elif period == '6M':
            start_date = end_date - timedelta(days=180)
        elif period == '1Y':
            start_date = end_date - timedelta(days=365)
        elif period == '3Y':
            start_date = end_date - timedelta(days=365*3)
        elif period == 'SINCE_2022':
            start_date = date(2022, 8, 15)
        else:  # ALL
            start_date = None
        
        # Récupérer les valeurs
        queryset = index.values.all()
        if start_date:
            queryset = queryset.filter(date_value__gte=start_date)
        
        values = queryset.order_by('date_value')
        
        if not values.exists():
            return Response({
                'error': 'No data available for this index',
                'index_id': index_id,
                'period': period
            }, status=404)
        
        # Calculer les variations
        value_data = []
        previous_close = None
        first_value = values.first().value if values.first().value else Decimal('0')
        
        for value in values:
            # Utiliser daily_change et daily_change_pct de la table si disponibles
            daily_change = value.daily_change if value.daily_change else Decimal('0')
            daily_change_pct = value.daily_change_pct if value.daily_change_pct else Decimal('0')
            
            # Si pas de données dans la table, calculer
            if not value.daily_change and previous_close and value.value:
                daily_change = value.value - previous_close
                daily_change_pct = (daily_change / previous_close) * 100
            
            # Calcul de variation depuis le début de la période
            total_change = value.value - first_value if value.value else Decimal('0')
            total_change_pct = (total_change / first_value) * 100 if first_value else Decimal('0')
            
            value_data.append({
                'date': value.date_value.isoformat(),
                'value': float(value.value) if value.value else 0,
                'previous_value': float(value.previous_value) if value.previous_value else 0,
                'daily_change': round(float(daily_change), 4),
                'daily_change_pct': round(float(daily_change_pct), 2),
                'total_change': round(float(total_change), 4),
                'total_change_pct': round(float(total_change_pct), 2)
            })
            
            if value.value:
                previous_close = value.value
        
        # Calculs de résumé
        last_value = values.last()
        last_value_close = last_value.value if last_value.value else Decimal('0')
        
        # Statistiques
        close_values = [float(v.value) for v in values if v.value]
        
        summary = {
            'index_id': index_id,
            'index_name': index.index_name,
            'period': period,
            'start_date': values.first().date_value.isoformat() if values.first() else None,
            'end_date': last_value.date_value.isoformat() if last_value else None,
            'first_value': float(first_value),
            'last_value': float(last_value_close),
            'total_change': float(last_value_close - first_value),
            'total_change_pct': round(float((last_value_close - first_value) / first_value * 100), 2) if first_value else 0,
            'highest_value': max(close_values) if close_values else 0,
            'lowest_value': min(close_values) if close_values else 0,
            'average_value': round(sum(close_values) / len(close_values), 2) if close_values else 0,
            'data_points': len(value_data),
            'data_source': 'PostgreSQL Database'
        }
        
        response_data = {
            'summary': summary,
            'variations': value_data
        }
        
        return Response(response_data)
        
    except XCapitalIndex.DoesNotExist:
        return Response({'error': 'Index not found'}, status=404)
    except Exception as e:
        return Response({'error': str(e)}, status=500)


@api_view(['GET', 'POST'])
@csrf_exempt
def get_xcapital_index_data_form(request):
    """
    API GET/POST pour récupérer des données d'indices avec un formulaire
    
    GET: Retourne les instructions et exemples
    POST: Retourne les données selon les paramètres fournis
    """
    if request.method == 'GET':
        # Instructions d'utilisation
        available_indices = list(XCapitalIndex.objects.values_list('index_id', flat=True)[:10])
        
        return Response({
            'api_name': 'XCapital Index Data API',
            'description': 'Récupération de données historiques des indices XCapital via POST',
            'method': 'POST',
            'content_type': 'application/json',
            'parameters': {
                'index_id': 'ID de l\'indice (requis)',
                'period': 'Période des données: 1M, 3M, 6M, 1Y, 3Y, ALL, CUSTOM',
                'start_date': 'Date de début (format YYYY-MM-DD, si period=CUSTOM)',
                'end_date': 'Date de fin (format YYYY-MM-DD, si period=CUSTOM)'
            },
            'constraints': {
                'min_start_date': '2022-08-15',
                'max_end_date': 'Aujourd\'hui'
            },
            'available_indices_sample': available_indices,
            'total_indices': XCapitalIndex.objects.count()
        })
    
    # Validation des données du formulaire
    mutable_data = request.data.copy() if hasattr(request.data, 'copy') else dict(request.data)
    if 'index_id' in mutable_data and isinstance(mutable_data['index_id'], str):
        mutable_data['index_id'] = mutable_data['index_id'].strip()
    
    serializer = XCapitalIndexDataRequestSerializer(data=mutable_data)
    if not serializer.is_valid():
        return Response({
            'error': 'Données du formulaire invalides',
            'details': serializer.errors
        }, status=400)
    
    validated_data = serializer.validated_data
    index_id = validated_data['index_id'].strip() if isinstance(validated_data['index_id'], str) else validated_data['index_id']
    period = validated_data['period']
    start_date = validated_data.get('start_date')
    end_date = validated_data.get('end_date', date.today())
    
    try:
        # Récupérer l'indice
        index = get_object_or_404(XCapitalIndex, index_id=index_id)
        
        # Calculer les dates selon la période
        if period != 'CUSTOM':
            end_date = date.today()
            if period == '1M':
                start_date = end_date - timedelta(days=30)
            elif period == '3M':
                start_date = end_date - timedelta(days=90)
            elif period == '6M':
                start_date = end_date - timedelta(days=180)
            elif period == '1Y':
                start_date = end_date - timedelta(days=365)
            elif period == '3Y':
                start_date = end_date - timedelta(days=365*3)
            else:  # ALL
                start_date = date(2022, 8, 15)  # Date minimale
        
        # Récupérer les données de la base PostgreSQL
        queryset = index.values.all()
        if start_date:
            queryset = queryset.filter(date_value__gte=start_date)
        if end_date:
            queryset = queryset.filter(date_value__lte=end_date)
        
        values = queryset.order_by('date_value')
        
        if not values.exists():
            return Response({
                'error': f'Aucune donnée trouvée pour {index_id} dans la période demandée',
                'index_id': index_id,
                'period': period,
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            }, status=404)
        
        # Préparer les données pour la réponse
        value_data = []
        previous_close = None
        first_value = values.first().value if values.first().value else Decimal('0')
        
        for value in values:
            close_value = value.value or Decimal('0')
            
            # Utiliser les données de la table si disponibles, sinon calculer
            daily_change = value.daily_change if value.daily_change else Decimal('0')
            daily_change_pct = value.daily_change_pct if value.daily_change_pct else Decimal('0')
            
            if not value.daily_change and previous_close:
                daily_change = close_value - previous_close
                daily_change_pct = (daily_change / previous_close) * 100 if previous_close else Decimal('0')
            
            # Calcul de variation totale
            total_change = close_value - first_value
            total_change_pct = (total_change / first_value) * 100 if first_value else Decimal('0')
            
            value_data.append({
                'date': value.date_value.isoformat(),
                'value': float(close_value),
                'previous_value': float(value.previous_value) if value.previous_value else 0,
                'daily_change': round(float(daily_change), 4),
                'daily_change_pct': round(float(daily_change_pct), 2),
                'total_change': round(float(total_change), 4),
                'total_change_pct': round(float(total_change_pct), 2)
            })
            
            previous_close = close_value
        
        # Statistiques de résumé
        last_value = values.last()
        close_values = [float(v.value) for v in values if v.value]
        
        summary = {
            'index_id': index_id,
            'index_name': index.index_name,
            'period': period,
            'start_date': values.first().date_value.isoformat(),
            'end_date': last_value.date_value.isoformat(),
            'first_value': float(first_value),
            'last_value': float(last_value.value) if last_value.value else 0,
            'total_change': float(last_value.value - first_value) if last_value.value else 0,
            'total_change_pct': round(float((last_value.value - first_value) / first_value * 100), 2) if (last_value.value and first_value) else 0,
            'highest_value': max(close_values) if close_values else 0,
            'lowest_value': min(close_values) if close_values else 0,
            'average_value': round(sum(close_values) / len(close_values), 2) if close_values else 0,
            'data_points': len(value_data),
            'data_source': 'PostgreSQL Database'
        }
        
        return Response({
            'success': True,
            'summary': summary,
            'data': value_data
        })
    
    except XCapitalIndex.DoesNotExist:
        return Response({'error': 'Index not found'}, status=404)
    except Exception as e:
        return Response({'error': str(e)}, status=500)


@api_view(['GET'])
def get_xcapital_available_indices(request):
    """Liste tous les indices disponibles"""
    try:
        indices = XCapitalIndex.objects.all().values(
            'index_id', 'index_name'
        ).order_by('index_name')
        
        indices_list = [
            {
                'index_id': index['index_id'],
                'index_name': index['index_name']
            }
            for index in indices
        ]
        
        return Response({
            'success': True,
            'total_indices': len(indices_list),
            'indices': indices_list
        })
    except Exception as e:
        return Response({'error': str(e)}, status=500)


@api_view(['GET'])
def available_indices_list(request):
    """Liste simple des indices disponibles"""
    try:
        indices = XCapitalIndex.objects.all().order_by('index_name')
        serializer = XCapitalIndexSerializer(indices, many=True)
        return Response({
            'success': True,
            'total_indices': indices.count(),
            'indices': serializer.data
        })
    except Exception as e:
        return Response({'error': str(e)}, status=500)
