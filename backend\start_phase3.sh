#!/bin/bash
# PHASE 3 QUICK START GUIDE
# Run this to start the complete XCapital Terminal Backend with Phase 3

echo "🚀 STARTING XCAPITAL TERMINAL BACKEND - PHASE 3"
echo "=============================================="

# Check if <PERSON><PERSON> is running
echo "1. Checking Redis..."
if ./redis-windows/redis-cli.exe ping > /dev/null 2>&1; then
    echo "✅ Redis is running"
else
    echo "❌ Redis not running. Starting Redis..."
    echo "Please run: cd redis-windows && ./redis-server.exe"
    exit 1
fi

# Start Celery worker
echo "2. Starting Celery worker..."
echo "Run in a separate terminal:"
echo "celery -A xcapital_backend worker --loglevel=info --pool=solo"

echo ""
echo "📊 AVAILABLE PHASE 3 TASKS:"
echo "============================"
echo "1. Full Daily Update:"
echo "   python manage.py shell -c \"from scraper.tasks import run_daily_update; result = run_daily_update.delay(); print(f'Task ID: {result.id}')\""
echo ""
echo "2. Test Update (5 companies):"
echo "   python manage.py shell -c \"from scraper.tasks import run_test_update; result = run_test_update.delay(); print(f'Task ID: {result.id}')\""
echo ""
echo "3. Test Single Company (Attijariwafa Bank):"
echo "   python manage.py shell -c \"from scraper.tasks import test_single_company; result = test_single_company.delay(511); print(f'Task ID: {result.id}')\""
echo ""
echo "4. System Status:"
echo "   python manage.py shell -c \"from scraper.tasks import get_system_status; result = get_system_status.delay(); print(result.get())\""
echo ""
echo "🎉 PHASE 3 READY FOR PRODUCTION!"
echo "77 Moroccan companies configured for daily updates"
