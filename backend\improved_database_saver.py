#!/usr/bin/env python3
"""
Module amélioré pour sauvegarder les données dans PostgreSQL
"""

import os
import sys
from datetime import datetime, date
from decimal import Decimal
from typing import Dict, Optional, List

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

try:
    import django
    django.setup()
    from django.db import transaction, connection
    DJANGO_AVAILABLE = True
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    DJANGO_AVAILABLE = False

class ImprovedDatabaseSaver:
    """
    Sauvegarde améliorée avec SQL direct pour éviter les problèmes managed=False
    """
    
    def __init__(self):
        self.logger = None
        
    def save_company_data(self, company_data: Dict) -> bool:
        """
        Sauvegarde une entreprise et ses données avec SQL direct
        """
        
        if not DJANGO_AVAILABLE:
            print("❌ Django non disponible")
            return False
        
        try:
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # 1. Insérer/Mettre à jour l'entreprise
                    cursor.execute("""
                        INSERT INTO "XCapitalTerminal_Companies" 
                        (company_id, symbol, nom_francais, nom_anglais, nom_arabe)
                        VALUES (%s, %s, %s, %s, %s)
                        ON CONFLICT (company_id) DO UPDATE SET
                        symbol = EXCLUDED.symbol,
                        nom_francais = EXCLUDED.nom_francais,
                        nom_anglais = EXCLUDED.nom_anglais,
                        updated_at = CURRENT_TIMESTAMP
                        RETURNING id
                    """, [
                        str(company_data['company_id']),
                        company_data['symbol'],
                        company_data['company_name'],
                        company_data['company_name'],
                        ""
                    ])
                    
                    company_result = cursor.fetchone()
                    if not company_result:
                        # Si INSERT a échoué, essayer de récupérer l'ID existant
                        cursor.execute("""
                            SELECT id FROM "XCapitalTerminal_Companies" 
                            WHERE company_id = %s
                        """, [str(company_data['company_id'])])
                        company_result = cursor.fetchone()
                    
                    if not company_result:
                        raise Exception(f"Impossible de créer/récupérer l'entreprise {company_data['company_id']}")
                    
                    company_pk = company_result[0]
                    
                    # 2. Insérer/Mettre à jour les données de prix
                    cursor.execute("""
                        INSERT INTO "XCapitalTerminal_CompanyBonds"
                        (company_id, date_trade, open_price, high_price, low_price, close_price, volume, value_mad)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (company_id, date_trade) DO UPDATE SET
                        open_price = EXCLUDED.open_price,
                        high_price = EXCLUDED.high_price,
                        low_price = EXCLUDED.low_price,
                        close_price = EXCLUDED.close_price,
                        volume = EXCLUDED.volume,
                        value_mad = EXCLUDED.value_mad,
                        updated_at = CURRENT_TIMESTAMP
                        RETURNING id
                    """, [
                        company_pk,
                        company_data['date_trade'],
                        company_data['open_price'],
                        company_data['high_price'],
                        company_data['low_price'],
                        company_data['close_price'],
                        company_data['volume'],
                        company_data['value_mad']
                    ])
                    
                    bond_result = cursor.fetchone()
                    if bond_result:
                        print(f"✅ Données sauvées: {company_data['symbol']} - {company_data['close_price']} MAD")
                        return True
                    else:
                        print(f"❌ Échec sauvegarde prix pour {company_data['symbol']}")
                        return False
                        
        except Exception as e:
            print(f"❌ Erreur sauvegarde: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_companies_from_database(self) -> List[Dict]:
        """
        Récupère toutes les entreprises de la base de données
        """
        
        if not DJANGO_AVAILABLE:
            return []
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT company_id, symbol, nom_francais
                    FROM "XCapitalTerminal_Companies"
                    ORDER BY symbol
                """)
                
                companies = []
                for row in cursor.fetchall():
                    companies.append({
                        'company_id': row[0],
                        'symbol': row[1],
                        'name': row[2]
                    })
                
                return companies
                
        except Exception as e:
            print(f"❌ Erreur récupération entreprises: {e}")
            return []
    
    def get_latest_prices(self, limit: int = 10) -> List[Dict]:
        """
        Récupère les derniers prix enregistrés
        """
        
        if not DJANGO_AVAILABLE:
            return []
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT c.symbol, c.nom_francais, b.date_trade, b.close_price, b.volume
                    FROM "XCapitalTerminal_CompanyBonds" b
                    JOIN "XCapitalTerminal_Companies" c ON c.id = b.company_id
                    ORDER BY b.date_trade DESC, b.updated_at DESC
                    LIMIT %s
                """, [limit])
                
                prices = []
                for row in cursor.fetchall():
                    prices.append({
                        'symbol': row[0],
                        'name': row[1],
                        'date': row[2],
                        'price': float(row[3]),
                        'volume': row[4]
                    })
                
                return prices
                
        except Exception as e:
            print(f"❌ Erreur récupération prix: {e}")
            return []

# Instance globale
improved_db_saver = ImprovedDatabaseSaver()

def save_company_data(company_data: Dict) -> bool:
    """Fonction helper pour sauvegarder"""
    return improved_db_saver.save_company_data(company_data)

def get_companies_from_database() -> List[Dict]:
    """Fonction helper pour récupérer les entreprises"""
    return improved_db_saver.get_companies_from_database()

def get_latest_prices(limit: int = 10) -> List[Dict]:
    """Fonction helper pour récupérer les derniers prix"""
    return improved_db_saver.get_latest_prices(limit)

if __name__ == "__main__":
    # Test du module
    print("🧪 TEST MODULE SAUVEGARDE")
    print("=" * 40)
    
    # Test sauvegarde
    test_data = {
        'company_id': 468,
        'symbol': 'BOA',
        'company_name': 'Bank of Africa',
        'date_trade': date.today(),
        'open_price': Decimal('180.00'),
        'high_price': Decimal('185.00'),
        'low_price': Decimal('178.00'),
        'close_price': Decimal('182.50'),
        'volume': 12500,
        'value_mad': Decimal('2281250.00')
    }
    
    success = save_company_data(test_data)
    print(f"Sauvegarde réussie: {success}")
    
    # Test récupération
    companies = get_companies_from_database()
    print(f"Entreprises en BDD: {len(companies)}")
    
    prices = get_latest_prices(5)
    print(f"Derniers prix: {len(prices)}")
    for price in prices:
        print(f"  {price['symbol']}: {price['price']} MAD ({price['date']})")
