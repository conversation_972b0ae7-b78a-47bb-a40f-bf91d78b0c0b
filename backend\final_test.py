"""
Complete Phase 2 Test Suite
Run this script to test all components of the scraper system
"""

import os
import sys
import time
import json
from pathlib import Path

# Setup Django
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

import django
django.setup()

def test_redis_connection():
    """Test Redis connection"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        ping_result = r.ping()
        print(f"✅ Redis Connection: {ping_result}")
        return True
    except Exception as e:
        print(f"❌ Redis Connection Failed: {e}")
        return False

def test_celery_configuration():
    """Test Celery configuration"""
    try:
        from xcapital_backend.celery import app
        print(f"✅ Celery Configuration Loaded")
        print(f"   Broker URL: {app.conf.broker_url}")
        print(f"   Result Backend: {app.conf.result_backend}")
        return True
    except Exception as e:
        print(f"❌ Celery Configuration Failed: {e}")
        return False

def test_database_connection():
    """Test database connection"""
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchone()
            print(f"✅ Database Connection: {result[0] == 1}")
            return True
    except Exception as e:
        print(f"❌ Database Connection Failed: {e}")
        return False

def test_task_imports():
    """Test task imports"""
    try:
        from scraper.tasks import test_connection, run_scraper, check_tables
        print("✅ Scraper Tasks Imported Successfully")
        print("   Available tasks: test_connection, run_scraper, check_tables")
        return True
    except Exception as e:
        print(f"❌ Task Import Failed: {e}")
        return False

def test_celery_task_execution():
    """Test actual Celery task execution"""
    try:
        from scraper.tasks import test_connection
        print("🚀 Testing Celery Task Execution...")
        
        # Execute the task
        result = test_connection.delay()
        print(f"   Task ID: {result.id}")
        
        # Wait for result (with timeout)
        try:
            task_result = result.get(timeout=10)
            print(f"✅ Task Execution Result: {task_result}")
            return True
        except Exception as timeout_error:
            print(f"⚠️ Task execution timeout (this is normal if worker isn't running): {timeout_error}")
            return False
            
    except Exception as e:
        print(f"❌ Task Execution Failed: {e}")
        return False

def test_table_existence():
    """Test if required tables exist"""
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            # Check for existing tables
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE '%companies%' OR table_name LIKE '%bonds%'
            """)
            tables = cursor.fetchall()
            
            if tables:
                print("✅ Found relevant tables:")
                for table in tables:
                    print(f"   - {table[0]}")
            else:
                print("⚠️ No company/bond tables found yet")
            return True
    except Exception as e:
        print(f"❌ Table Check Failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🎯 Phase 2 - Complete System Test")
    print("=" * 50)
    
    # Run all tests
    tests = [
        ("Redis Connection", test_redis_connection),
        ("Celery Configuration", test_celery_configuration),
        ("Database Connection", test_database_connection),
        ("Task Imports", test_task_imports),
        ("Table Existence", test_table_existence),
        ("Celery Task Execution", test_celery_task_execution),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Testing: {test_name}")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed >= 4:  # Core tests passed
        print("\n🎉 Phase 2 setup is working!")
        print("\nNext steps:")
        print("1. Start Celery worker: celery -A xcapital_backend worker --loglevel=info --pool=solo")
        print("2. Start Django server: python manage.py runserver")
        print("3. Test API endpoints:")
        print("   - GET http://localhost:8000/api/scraper/health/")
        print("   - POST http://localhost:8000/api/scraper/trigger-test/")
        print("   - GET http://localhost:8000/api/scraper/test-celery/")
    else:
        print("\n❌ Some components need attention. Check the errors above.")

if __name__ == "__main__":
    main()
