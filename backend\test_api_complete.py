#!/usr/bin/env python3
"""
TEST COMPLET - API CASABLANCA + BASE DE DONNÉES
==============================================

Test toutes les fonctionnalités:
1. Connexion API Casablanca
2. Parsing des données
3. Sauvegarde en base de données
4. Vérification des données
5. Mise à jour quotidienne
"""

import os
import sys
from datetime import datetime, date

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

try:
    import django
    django.setup()
    print("✅ Django configuré avec succès")
except Exception as e:
    print(f"❌ Erreur configuration Django: {e}")
    sys.exit(1)

def test_api_connection():
    """Test de connexion à l'API Casablanca"""
    print("\n1️⃣ TEST CONNEXION API CASABLANCA")
    print("-" * 40)
    
    try:
        from casablanca_api_client import casablanca_client
        
        # Test récupération d'une page
        page_data = casablanca_client.fetch_page_data(page_offset=0, page_limit=5)
        
        if page_data:
            print(f"✅ API accessible - {len(page_data)} entreprises récupérées")
            
            # Afficher un exemple
            if len(page_data) > 0:
                example = page_data[0]
                print(f"   Exemple entreprise:")
                print(f"      ID: {example.get('id', 'N/A')}")
                print(f"      Attributes: {list(example.get('attributes', {}).keys())}")
                
            return True
        else:
            print("❌ Aucune donnée récupérée de l'API")
            return False
            
    except Exception as e:
        print(f"❌ Erreur connexion API: {e}")
        return False

def test_data_parsing():
    """Test du parsing des données API"""
    print("\n2️⃣ TEST PARSING DONNÉES")
    print("-" * 40)
    
    try:
        from casablanca_api_client import casablanca_client
        
        # Récupérer quelques données pour tester le parsing
        raw_data = casablanca_client.fetch_page_data(page_offset=0, page_limit=3)
        
        if not raw_data:
            print("❌ Aucune donnée à parser")
            return False
        
        parsed_count = 0
        for i, company_raw in enumerate(raw_data):
            parsed_data = casablanca_client.parse_company_data(company_raw)
            
            if parsed_data:
                parsed_count += 1
                print(f"✅ Entreprise {i+1} parsée:")
                print(f"   ID: {parsed_data['company_id']}")
                print(f"   Symbol: {parsed_data['symbol']}")
                print(f"   Prix fermeture: {parsed_data['close_price']} MAD")
                print(f"   Volume: {parsed_data['volume']}")
            else:
                print(f"❌ Échec parsing entreprise {i+1}")
        
        print(f"\n📊 Résultat parsing: {parsed_count}/{len(raw_data)} entreprises parsées")
        return parsed_count > 0
        
    except Exception as e:
        print(f"❌ Erreur parsing: {e}")
        return False

def test_database_saving():
    """Test de sauvegarde en base de données"""
    print("\n3️⃣ TEST SAUVEGARDE BASE DE DONNÉES")
    print("-" * 40)
    
    try:
        from casablanca_api_client import casablanca_client
        from decimal import Decimal
        
        # Créer des données de test
        test_data = {
            'company_id': 999999,
            'symbol': 'TEST_API',
            'company_name': 'Test Company API',
            'date_trade': date.today(),
            'open_price': Decimal('100.00'),
            'high_price': Decimal('105.00'),
            'low_price': Decimal('98.00'),
            'close_price': Decimal('102.50'),
            'volume': 5000,
            'value_mad': Decimal('512500.00'),
            'shares_traded': 5000.0,
            'total_trades': 25,
            'api_source': 'test'
        }
        
        # Test sauvegarde
        success = casablanca_client.save_company_to_database(test_data)
        
        if success:
            print("✅ Sauvegarde test réussie")
            
            # Vérifier que les données sont bien sauvées
            latest_data = casablanca_client.get_latest_data_from_db(5)
            
            test_found = False
            for data in latest_data:
                if data['symbol'] == 'TEST_API':
                    test_found = True
                    print(f"✅ Données test trouvées en BDD:")
                    print(f"   Prix: {data['price']} MAD")
                    print(f"   Date: {data['date']}")
                    break
            
            if not test_found:
                print("⚠️ Données test non trouvées en BDD")
            
            return success and test_found
        else:
            print("❌ Échec sauvegarde test")
            return False
            
    except Exception as e:
        print(f"❌ Erreur sauvegarde: {e}")
        return False

def test_full_api_update():
    """Test de mise à jour complète depuis l'API"""
    print("\n4️⃣ TEST MISE À JOUR COMPLÈTE API")
    print("-" * 40)
    
    try:
        from casablanca_api_client import update_from_casablanca_api
        
        print("🚀 Lancement mise à jour complète...")
        result = update_from_casablanca_api()
        
        if result['success']:
            print(f"✅ Mise à jour API réussie!")
            print(f"   Entreprises traitées: {result['companies_processed']}")
            print(f"   Entreprises sauvées: {result['companies_saved']}")
            print(f"   Entreprises échouées: {result['companies_failed']}")
            print(f"   Appels API: {result['api_calls']}")
            print(f"   Durée: {result['duration_seconds']:.2f}s")
            
            if result['errors']:
                print(f"   Erreurs: {len(result['errors'])}")
                for error in result['errors'][:3]:
                    print(f"      {error}")
            
            return True
        else:
            print(f"❌ Mise à jour API échouée: {result.get('error', 'Erreur inconnue')}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur mise à jour API: {e}")
        return False

def test_orchestrator_integration():
    """Test de l'intégration avec l'orchestrateur"""
    print("\n5️⃣ TEST INTÉGRATION ORCHESTRATEUR")
    print("-" * 40)
    
    try:
        from improved_daily_orchestrator import improved_orchestrator
        
        print("🔄 Test mise à jour quotidienne avec API...")
        result = improved_orchestrator.run_daily_update(test_mode=True)
        
        if result['success']:
            print(f"✅ Orchestrateur fonctionne!")
            print(f"   Entreprises traitées: {result['companies_processed']}")
            print(f"   Entreprises réussies: {result['companies_successful']}")
            print(f"   Sauvegardes BDD: {result['database_saves']}")
            print(f"   Source données: {result['data_source']}")
            print(f"   Durée: {result['duration_seconds']:.2f}s")
            
            return True
        else:
            print(f"❌ Orchestrateur échoué: {result.get('error', 'Erreur inconnue')}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur orchestrateur: {e}")
        return False

def test_database_verification():
    """Vérification finale des données en base"""
    print("\n6️⃣ VÉRIFICATION FINALE BASE DE DONNÉES")
    print("-" * 40)
    
    try:
        from casablanca_api_client import casablanca_client
        from django.db import connection
        
        # Compter les entreprises et données
        with connection.cursor() as cursor:
            cursor.execute('SELECT COUNT(*) FROM "XCapitalTerminal_Companies"')
            companies_count = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM "XCapitalTerminal_CompanyBonds"')
            bonds_count = cursor.fetchone()[0]
            
            # Données d'aujourd'hui
            cursor.execute("""
                SELECT COUNT(*) FROM "XCapitalTerminal_CompanyBonds" 
                WHERE date_trade = %s
            """, [date.today()])
            today_count = cursor.fetchone()[0]
        
        print(f"📊 Total entreprises: {companies_count}")
        print(f"📊 Total données prix: {bonds_count}")
        print(f"📊 Données aujourd'hui: {today_count}")
        
        # Afficher les dernières données
        latest_data = casablanca_client.get_latest_data_from_db(5)
        print(f"\n📊 Dernières données:")
        for data in latest_data:
            print(f"   {data['symbol']}: {data['price']} MAD, Vol: {data['volume']} ({data['date']})")
        
        return companies_count > 0 and today_count > 0
        
    except Exception as e:
        print(f"❌ Erreur vérification BDD: {e}")
        return False

def main():
    """Test principal"""
    print("🧪 TEST COMPLET API CASABLANCA + BASE DE DONNÉES")
    print("=" * 60)
    
    tests = [
        ("Connexion API", test_api_connection),
        ("Parsing données", test_data_parsing),
        ("Sauvegarde BDD", test_database_saving),
        ("Mise à jour API", test_full_api_update),
        ("Intégration orchestrateur", test_orchestrator_integration),
        ("Vérification finale", test_database_verification)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Erreur inattendue dans {test_name}: {e}")
            results[test_name] = False
    
    # Résumé final
    print("\n📊 RÉSUMÉ DES TESTS")
    print("=" * 40)
    
    passed = 0
    total = len(tests)
    
    for test_name, success in results.items():
        status = "✅ PASSÉ" if success else "❌ ÉCHEC"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 RÉSULTAT GLOBAL: {passed}/{total} tests passés")
    
    if passed == total:
        print("\n🎉 SYSTÈME COMPLÈTEMENT OPÉRATIONNEL!")
        print("   ✅ API Casablanca fonctionnelle")
        print("   ✅ Sauvegarde PostgreSQL opérationnelle")
        print("   ✅ Orchestrateur intégré")
        print("   ✅ Prêt pour mise à jour automatique à 19:00")
        
        print("\n📋 COMMANDES POUR DÉMARRER:")
        print("   1. Redis: cd redis-windows && redis-server.exe")
        print("   2. Celery Worker: celery -A xcapital_backend worker --loglevel=info --pool=solo")
        print("   3. Celery Beat: celery -A xcapital_backend beat --loglevel=info")
        
        return True
    else:
        print(f"\n⚠️ SYSTÈME PARTIELLEMENT OPÉRATIONNEL")
        print(f"   {total - passed} test(s) ont échoué")
        print("   Vérifier les erreurs ci-dessus")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
