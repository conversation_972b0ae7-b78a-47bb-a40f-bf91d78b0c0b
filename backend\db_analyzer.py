#!/usr/bin/env python3
"""
Analyse de la structure de la base de données XCapitalTerminal
"""

import os
import sys

def analyze_database_structure():
    """Analyser les tables et colonnes"""
    print("🗄️ ANALYSE STRUCTURE BASE DE DONNÉES")
    print("=" * 50)
    
    try:
        # Configuration Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
        import django
        django.setup()
        
        from django.db import connection
        
        # Analyser XCapitalTerminal_CompanyBonds
        print("📋 TABLE: XCapitalTerminal_CompanyBonds")
        print("-" * 40)
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default,
                       character_maximum_length, numeric_precision, numeric_scale
                FROM information_schema.columns 
                WHERE table_name = 'XCapitalTerminal_CompanyBonds'
                ORDER BY ordinal_position
            """)
            
            columns = cursor.fetchall()
            
            for col in columns:
                name, data_type, nullable, default, max_length, precision, scale = col
                
                type_info = data_type
                if max_length:
                    type_info += f"({max_length})"
                elif precision:
                    type_info += f"({precision}"
                    if scale:
                        type_info += f",{scale}"
                    type_info += ")"
                
                nullable_str = "NULL" if nullable == 'YES' else "NOT NULL"
                default_str = f" DEFAULT {default}" if default else ""
                
                print(f"   {name:25} {type_info:20} {nullable_str:8} {default_str}")
        
        # Analyser XCapitalTerminal_Companies  
        print(f"\n📋 TABLE: XCapitalTerminal_Companies")
        print("-" * 40)
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default,
                       character_maximum_length, numeric_precision, numeric_scale
                FROM information_schema.columns 
                WHERE table_name = 'XCapitalTerminal_Companies'
                ORDER BY ordinal_position
            """)
            
            columns = cursor.fetchall()
            
            for col in columns:
                name, data_type, nullable, default, max_length, precision, scale = col
                
                type_info = data_type
                if max_length:
                    type_info += f"({max_length})"
                elif precision:
                    type_info += f"({precision}"
                    if scale:
                        type_info += f",{scale}"
                    type_info += ")"
                
                nullable_str = "NULL" if nullable == 'YES' else "NOT NULL"
                default_str = f" DEFAULT {default}" if default else ""
                
                print(f"   {name:25} {type_info:20} {nullable_str:8} {default_str}")
        
        # Analyser les contraintes et relations
        print(f"\n🔗 RELATIONS ET CONTRAINTES")
        print("-" * 40)
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    tc.constraint_name,
                    tc.table_name,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name 
                FROM 
                    information_schema.table_constraints AS tc 
                    JOIN information_schema.key_column_usage AS kcu
                      ON tc.constraint_name = kcu.constraint_name
                    JOIN information_schema.constraint_column_usage AS ccu
                      ON ccu.constraint_name = tc.constraint_name
                WHERE tc.constraint_type = 'FOREIGN KEY' 
                AND (tc.table_name = 'XCapitalTerminal_CompanyBonds' 
                     OR tc.table_name = 'XCapitalTerminal_Companies')
            """)
            
            foreign_keys = cursor.fetchall()
            
            for fk in foreign_keys:
                constraint_name, table_name, column_name, foreign_table, foreign_column = fk
                print(f"   {table_name}.{column_name} -> {foreign_table}.{foreign_column}")
        
        # Compter les données existantes
        print(f"\n📊 DONNÉES EXISTANTES")
        print("-" * 30)
        
        with connection.cursor() as cursor:
            cursor.execute('SELECT COUNT(*) FROM "XCapitalTerminal_Companies"')
            companies_count = cursor.fetchone()[0]
            print(f"   Entreprises: {companies_count}")
            
            cursor.execute('SELECT COUNT(*) FROM "XCapitalTerminal_CompanyBonds"')
            bonds_count = cursor.fetchone()[0]
            print(f"   Données prix: {bonds_count}")
            
            # Quelques exemples d'entreprises
            if companies_count > 0:
                cursor.execute('SELECT id, symbol, name FROM "XCapitalTerminal_Companies" LIMIT 5')
                companies = cursor.fetchall()
                print(f"\n   Exemples d'entreprises:")
                for comp in companies:
                    print(f"      ID: {comp[0]}, Symbol: {comp[1]}, Name: {comp[2][:30]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_sql_insert_template():
    """Créer un template d'insertion SQL"""
    print(f"\n📝 TEMPLATE D'INSERTION SQL")
    print("=" * 50)
    
    # Template pour XCapitalTerminal_Companies
    print("📋 INSERT XCapitalTerminal_Companies:")
    print("""
    INSERT INTO "XCapitalTerminal_Companies" (
        symbol, name, market_cap, sector, industry, 
        currency, listing_date, created_at, updated_at
    ) VALUES (
        %(symbol)s, %(name)s, %(market_cap)s, %(sector)s, %(industry)s,
        %(currency)s, %(listing_date)s, NOW(), NOW()
    ) ON CONFLICT (symbol) DO UPDATE SET
        name = EXCLUDED.name,
        market_cap = EXCLUDED.market_cap,
        updated_at = NOW()
    RETURNING id;
    """)
    
    # Template pour XCapitalTerminal_CompanyBonds
    print("\n📋 INSERT XCapitalTerminal_CompanyBonds:")
    print("""
    INSERT INTO "XCapitalTerminal_CompanyBonds" (
        company_id, date_trade, open_price, close_price, 
        high_price, low_price, volume, value_mad,
        price_change, price_change_percent, shares_traded,
        total_trades, created_at, updated_at
    ) VALUES (
        %(company_id)s, %(date_trade)s, %(open_price)s, %(close_price)s,
        %(high_price)s, %(low_price)s, %(volume)s, %(value_mad)s,
        %(price_change)s, %(price_change_percent)s, %(shares_traded)s,
        %(total_trades)s, NOW(), NOW()
    ) ON CONFLICT (company_id, date_trade) DO UPDATE SET
        open_price = EXCLUDED.open_price,
        close_price = EXCLUDED.close_price,
        high_price = EXCLUDED.high_price,
        low_price = EXCLUDED.low_price,
        volume = EXCLUDED.volume,
        value_mad = EXCLUDED.value_mad,
        price_change = EXCLUDED.price_change,
        price_change_percent = EXCLUDED.price_change_percent,
        shares_traded = EXCLUDED.shares_traded,
        total_trades = EXCLUDED.total_trades,
        updated_at = NOW();
    """)

def main():
    """Analyse principale"""
    print("🔍 ANALYSE BASE DE DONNÉES XCAPITAL")
    print("=" * 50)
    
    success = analyze_database_structure()
    
    if success:
        create_sql_insert_template()
        
        print(f"\n✅ ANALYSE TERMINÉE")
        print("   Les structures de base de données sont maintenant connues")
        print("   Prêt pour le mapping API -> DB")
    else:
        print(f"\n❌ ÉCHEC DE L'ANALYSE")
        print("   Vérifier la configuration Django et PostgreSQL")

if __name__ == "__main__":
    main()
