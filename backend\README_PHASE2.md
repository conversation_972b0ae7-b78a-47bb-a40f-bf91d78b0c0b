# XCapital Scraper - Phase 2 (Test Simple)

## ✅ Phase 1 & 2 - Statut

- **Phase 1** ✅ : Celery + Redis + Beat configurés
- **Phase 2** ✅ : Tâches de test implémentées  
- **Phase 3** 🚧 : À définir ensemble

## 🚀 Test rapide Phase 2

### 1. Installer Redis (si pas déjà fait)
```bash
# Windows: Télécharger Redis ou utiliser WSL
# Linux: sudo apt install redis-server
# Mac: brew install redis
```

### 2. Démarrer Redis
```bash
redis-server
```

### 3. Installer les dépendances 
```bash
pip install celery redis django-celery-beat
```

### 4. Démarrer les services (3 terminaux différents)

**Terminal 1 - Django:**
```bash
cd backend
python manage.py runserver
```

**Terminal 2 - Celery Worker:**
```bash
cd backend  
celery -A xcapital_backend worker --loglevel=info
```

**Terminal 3 - Celery Beat:**
```bash
cd backend
celery -A xcapital_backend beat --loglevel=info
```

## 🧪 Tester les APIs Phase 2

### Test principal (avec <PERSON><PERSON> ou curl):
```bash
POST http://localhost:8000/api/scraper/test/trigger/
```

**Résultat attendu:**
```json
{
    "status": "success",
    "message": "🚀 Scraper de test Phase 2 démarré avec succès",
    "task_id": "abc-123-def",
    "check_status_url": "/api/scraper/task-status/abc-123-def/"
}
```

### Vérifier le résultat:
```bash
GET http://localhost:8000/api/scraper/task-status/abc-123-def/
```

### Autres tests disponibles:
```bash
# Test connexion Celery simple
POST http://localhost:8000/api/scraper/test/celery/

# Test tables de base de données  
POST http://localhost:8000/api/scraper/test/database/

# Statut général Phase 2
GET http://localhost:8000/api/scraper/test/status/
```

## 📋 Ce qui a été configuré

1. **Celery configuré** avec Redis comme broker
2. **Beat programmé** pour 19:00 quotidien (crontab(hour=19, minute=0))
3. **Tâches de test** qui vérifient:
   - Connexion à la base de données PostgreSQL
   - Tables `XCapitalTerminal_Companies` et `XCapitalTerminal_CompanyBonds`
   - Cache Redis fonctionnel
   - Configuration Celery
   - Simulation de traitement

## 🔧 Vérifications rapides

### Redis fonctionne ?
```bash
redis-cli ping
# Doit retourner: PONG
```

### Celery workers actifs ?
```bash
celery -A xcapital_backend inspect active
```

### Tâche programmée pour 19:00 ?
```bash
celery -A xcapital_backend inspect scheduled
```

## 📝 Logs et debugging

- **Logs Django:** `xcapital_performance.log`
- **Logs Celery:** Affichés dans les terminaux worker/beat
- **Erreurs:** Consultez les APIs de statut ci-dessus

## ✨ Résultat Phase 2

Si tout fonctionne, le test principal retourne:
```json
{
    "status": "completed",
    "phase": "Phase 2 - Test Simple", 
    "tests": {
        "database_test": {"status": "success", "tables_found": [...], "record_counts": {...}},
        "cache_test": {"status": "success"},
        "configuration": {...},
        "processing_simulation": "completed"
    },
    "message": "🎉 Tous les tests de la Phase 2 sont terminés avec succès!",
    "next_phase": "Phase 3 - Prêt pour l'implémentation complète"
}
```

## 🎯 Phase 3 - Prochaines étapes

Une fois Phase 2 validée, nous définirons ensemble:

1. **Quelles données** extraire des Company Bonds ?
2. **Quelles sources** scraper ?
3. **Quelle logique métier** appliquer ?
4. **Quels traitements** effectuer sur les données ?
5. **Comment** stocker/utiliser les résultats ?

---

**Questions/problèmes ?** Testez d'abord les APIs de statut ci-dessus pour diagnostiquer.
