from django.urls import path
from . import views

app_name = 'predictions'

urlpatterns = [
    # Prediction model endpoints
    path('models/', views.PredictionModelListView.as_view(), name='model-list'),
    path('models/<str:ticker>/', views.get_model_info, name='model-info'),
    
    # Prediction endpoints
    path('predictions/', views.PredictionListView.as_view(), name='prediction-list'),
    path('predictions/<str:ticker>/', views.PredictionListView.as_view(), name='prediction-list-ticker'),
    
    # ARIMA prediction endpoints
    path('arima-predict/', views.arima_predict, name='arima-predict'),  # Compatible avec l'ancien API
    path('daily-predict/', views.daily_price_prediction, name='daily-predict'),  # Nouvelle API jour par jour
    
    # Prediction request endpoints
    path('predict/', views.create_prediction_request, name='create-prediction'),
    path('requests/<int:request_id>/', views.get_prediction_request_status, name='prediction-request-status'),
]
