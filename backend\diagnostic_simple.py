#!/usr/bin/env python3
"""
Test Simple - Diagnostic Base de Données
"""

print("🔍 DIAGNOSTIC BASE DE DONNÉES")
print("=" * 40)

# 1. Test imports Django
try:
    import os
    import sys
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
    
    import django
    django.setup()
    print("✅ Django configuré")
    
    from django.db import connection
    print("✅ Django DB connection disponible")
    
except Exception as e:
    print(f"❌ Erreur Django: {e}")
    sys.exit(1)

# 2. Test connexion base de données
try:
    with connection.cursor() as cursor:
        cursor.execute("SELECT version()")
        db_version = cursor.fetchone()[0]
        print(f"✅ PostgreSQL connecté: {db_version[:50]}...")
        
except Exception as e:
    print(f"❌ Erreur connexion PostgreSQL: {e}")
    sys.exit(1)

# 3. Test existence tables
try:
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('XCapitalTerminal_Companies', 'XCapitalTerminal_CompanyBonds')
        """)
        tables = [row[0] for row in cursor.fetchall()]
        
        if 'XCapitalTerminal_Companies' in tables:
            print("✅ Table XCapitalTerminal_Companies existe")
        else:
            print("❌ Table XCapitalTerminal_Companies manquante")
            
        if 'XCapitalTerminal_CompanyBonds' in tables:
            print("✅ Table XCapitalTerminal_CompanyBonds existe")
        else:
            print("❌ Table XCapitalTerminal_CompanyBonds manquante")
            
except Exception as e:
    print(f"❌ Erreur vérification tables: {e}")

# 4. Test insertion simple
try:
    from decimal import Decimal
    from datetime import date
    
    with connection.cursor() as cursor:
        # Test insertion entreprise
        cursor.execute("""
            INSERT INTO "XCapitalTerminal_Companies" 
            (company_id, symbol, nom_francais, nom_anglais, nom_arabe)
            VALUES (%s, %s, %s, %s, %s)
            ON CONFLICT (company_id) DO UPDATE SET
            symbol = EXCLUDED.symbol,
            nom_francais = EXCLUDED.nom_francais
            RETURNING id
        """, ['TEST468', 'BOA_TEST', 'Bank of Africa Test', 'Bank of Africa Test', ''])
        
        company_result = cursor.fetchone()
        if company_result:
            company_id = company_result[0]
            print(f"✅ Entreprise test insérée: ID={company_id}")
            
            # Test insertion prix
            cursor.execute("""
                INSERT INTO "XCapitalTerminal_CompanyBonds"
                (company_id, date_trade, open_price, high_price, low_price, close_price, volume, value_mad)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (company_id, date_trade) DO UPDATE SET
                close_price = EXCLUDED.close_price
                RETURNING id
            """, [
                company_id, date.today(),
                Decimal('180.00'), Decimal('185.00'), Decimal('178.00'), Decimal('182.50'),
                10000, Decimal('1825000.00')
            ])
            
            bond_result = cursor.fetchone()
            if bond_result:
                bond_id = bond_result[0]
                print(f"✅ Prix test inséré: ID={bond_id}")
                
                # Vérifier les données
                cursor.execute("""
                    SELECT c.symbol, c.nom_francais, b.close_price, b.volume
                    FROM "XCapitalTerminal_Companies" c
                    JOIN "XCapitalTerminal_CompanyBonds" b ON c.id = b.company_id
                    WHERE c.id = %s
                """, [company_id])
                
                result = cursor.fetchone()
                if result:
                    print(f"✅ Vérification: {result[0]} - {result[1]} - {result[2]} MAD - Vol: {result[3]}")
                else:
                    print("❌ Données non trouvées après insertion")
            else:
                print("❌ Échec insertion prix")
        else:
            print("❌ Échec insertion entreprise")
            
except Exception as e:
    print(f"❌ Erreur test insertion: {e}")
    import traceback
    traceback.print_exc()

# 5. Test du module amélioré
try:
    print("\n📊 Test module improved_database_saver:")
    
    from improved_database_saver import save_company_data, get_latest_prices
    
    test_data = {
        'company_id': 468,
        'symbol': 'BOA',
        'company_name': 'Bank of Africa',
        'date_trade': date.today(),
        'open_price': Decimal('180.00'),
        'high_price': Decimal('185.00'),
        'low_price': Decimal('178.00'),
        'close_price': Decimal('182.50'),
        'volume': 12500,
        'value_mad': Decimal('2281250.00')
    }
    
    success = save_company_data(test_data)
    if success:
        print("✅ Module improved_database_saver fonctionne")
        
        # Vérifier les derniers prix
        prices = get_latest_prices(3)
        print(f"   Derniers prix: {len(prices)} enregistrements")
        for price in prices:
            print(f"      {price['symbol']}: {price['price']} MAD ({price['date']})")
    else:
        print("❌ Module improved_database_saver échec")
        
except Exception as e:
    print(f"❌ Erreur module improved_database_saver: {e}")

# 6. Test orchestrateur
try:
    print("\n🚀 Test improved_daily_orchestrator:")
    
    from improved_daily_orchestrator import improved_orchestrator
    
    result = improved_orchestrator.test_single_company(468)
    
    if result['success']:
        print(f"✅ Orchestrateur fonctionne:")
        print(f"   Entreprise: {result['company_name']}")
        print(f"   Prix: {result['closing_price']} MAD")
        print(f"   Sauvé BDD: {result['database_saved']}")
    else:
        print(f"❌ Orchestrateur échec: {result['error']}")
        
except Exception as e:
    print(f"❌ Erreur orchestrateur: {e}")

print(f"\n🎯 DIAGNOSTIC TERMINÉ")
print("Si tous les tests passent ✅, le système est opérationnel!")
