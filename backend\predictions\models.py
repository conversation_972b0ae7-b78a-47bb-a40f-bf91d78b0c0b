from django.db import models
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from market_data.models import Stock
import json


class PredictionModel(models.Model):
    """Model representing ML prediction models"""
    MODEL_TYPES = [
        ('ARIMA', 'ARIMA'),
        ('LSTM', 'LSTM'),
        ('LINEAR_REGRESSION', 'Linear Regression'),
        ('RANDOM_FOREST', 'Random Forest'),
        ('PROPHET', 'Prophet'),
    ]
    
    name = models.CharField(max_length=100)
    model_type = models.CharField(max_length=20, choices=MODEL_TYPES)
    stock = models.ForeignKey(Stock, on_delete=models.CASCADE, related_name='prediction_models')
    file_path = models.CharField(max_length=500)  # Path to the saved model file
    parameters = models.J<PERSON><PERSON>ield(default=dict)  # Model parameters (e.g., ARIMA order)
    accuracy_metrics = models.J<PERSON><PERSON>ield(default=dict)  # MAE, RMSE, MAPE, etc.
    training_start_date = models.DateField()
    training_end_date = models.DateField()
    is_active = models.<PERSON><PERSON>an<PERSON>ield(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'prediction_models'
        unique_together = ['stock', 'model_type', 'name']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.stock.ticker} - {self.model_type} - {self.name}"


class Prediction(models.Model):
    """Model representing individual predictions"""
    model = models.ForeignKey(PredictionModel, on_delete=models.CASCADE, related_name='predictions')
    prediction_date = models.DateField()  # Date when prediction was made
    target_date = models.DateField()  # Date being predicted
    predicted_value = models.DecimalField(max_digits=15, decimal_places=4)
    confidence_lower = models.DecimalField(max_digits=15, decimal_places=4, blank=True, null=True)
    confidence_upper = models.DecimalField(max_digits=15, decimal_places=4, blank=True, null=True)
    confidence_level = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        default=95.0,
        validators=[MinValueValidator(0.01), MaxValueValidator(99.99)]
    )
    actual_value = models.DecimalField(max_digits=15, decimal_places=4, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'predictions'
        unique_together = ['model', 'prediction_date', 'target_date']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['target_date']),
            models.Index(fields=['prediction_date']),
            models.Index(fields=['model', 'target_date']),
        ]

    def __str__(self):
        return f"{self.model.stock.ticker} - {self.target_date} - {self.predicted_value}"

    @property
    def prediction_error(self):
        """Calculate prediction error if actual value is available"""
        if self.actual_value is not None:
            return float(abs(self.predicted_value - self.actual_value))
        return None

    @property
    def prediction_accuracy(self):
        """Calculate prediction accuracy percentage if actual value is available"""
        if self.actual_value is not None and self.actual_value != 0:
            error_pct = abs(self.predicted_value - self.actual_value) / abs(self.actual_value) * 100
            return float(100 - error_pct)
        return None


class PredictionRequest(models.Model):
    """Model representing prediction requests from users"""
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
    ]
    
    stock = models.ForeignKey(Stock, on_delete=models.CASCADE)
    model_type = models.CharField(max_length=20, choices=PredictionModel.MODEL_TYPES)
    start_date = models.DateField()
    end_date = models.DateField()
    forecast_days = models.IntegerField(default=30, validators=[MinValueValidator(1), MaxValueValidator(365)])
    confidence_interval = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        default=95.0,
        validators=[MinValueValidator(0.01), MaxValueValidator(99.99)]
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    result_data = models.JSONField(blank=True, null=True)  # Store prediction results
    error_message = models.TextField(blank=True, null=True)
    requested_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(blank=True, null=True)
    
    class Meta:
        db_table = 'prediction_requests'
        ordering = ['-requested_at']

    def __str__(self):
        return f"{self.stock.ticker} - {self.model_type} - {self.status}"
