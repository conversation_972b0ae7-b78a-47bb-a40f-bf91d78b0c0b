#!/usr/bin/env python
"""
Test script to verify Phase 2 setup
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

try:
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    sys.exit(1)

# Test Redis connection
try:
    import redis
    r = redis.Redis(host='localhost', port=6379, db=0)
    ping_result = r.ping()
    print(f"✅ Redis connection: {ping_result}")
except Exception as e:
    print(f"❌ Redis connection failed: {e}")

# Test Celery configuration
try:
    from xcapital_backend.celery import app
    print(f"✅ Celery app loaded")
    print(f"   Broker: {app.conf.broker_url}")
    print(f"   Backend: {app.conf.result_backend}")
except Exception as e:
    print(f"❌ Celery configuration failed: {e}")

# Test task import
try:
    from scraper.tasks import test_connection, run_scraper, check_tables
    print("✅ Scraper tasks imported successfully")
    print(f"   Available tasks: test_connection, run_scraper, check_tables")
except Exception as e:
    print(f"❌ Task import failed: {e}")

# Test database connection
try:
    from django.db import connection
    with connection.cursor() as cursor:
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        print(f"✅ Database connection: {result}")
except Exception as e:
    print(f"❌ Database connection failed: {e}")

print("\n🎯 Phase 2 Component Test Complete!")
print("\nNext steps:")
print("1. Start Celery worker: celery -A xcapital_backend worker --loglevel=info --pool=solo")
print("2. Start Django server: python manage.py runserver")
print("3. Test API endpoints")
