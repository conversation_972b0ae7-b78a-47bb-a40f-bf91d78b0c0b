from django.urls import path
from . import views

app_name = 'market_data'

urlpatterns = [
    # Root endpoints
    path('', views.market_root, name='market-root'),
    
    # Stock endpoints
    path('stocks/', views.StockListView.as_view(), name='stock-list'),
    path('stocks/<str:ticker>/', views.StockDetailView.as_view(), name='stock-detail'),
    path('stocks/<str:ticker>/prices/', views.StockPriceListView.as_view(), name='stock-prices'),
    path('stocks/<str:ticker>/summary/', views.get_stock_summary, name='stock-summary'),
    path('stocks/<str:ticker>/chart/', views.get_stock_chart_data, name='stock-chart'),
    path('stocks/<str:ticker>/indicators/', views.get_technical_indicators, name='technical-indicators'),
    
    # Ticker endpoints
    path('tickers/', views.get_available_tickers, name='available-tickers'),
    
    # Market analysis endpoints
    path('market/overview/', views.get_market_overview, name='market-overview'),
    path('market/sectors/', views.get_sector_analysis, name='sector-analysis'),
    
    # Bulk data endpoints
    path('bulk-data/', views.get_bulk_stock_data, name='bulk-stock-data'),
    
    # Market index endpoints
    path('indices/', views.MarketIndexListView.as_view(), name='index-list'),
    path('indices/<str:index_code>/prices/', views.IndexPriceListView.as_view(), name='index-prices'),
    
    # Health check
    path('health/', views.health_check, name='health-check'),
]
