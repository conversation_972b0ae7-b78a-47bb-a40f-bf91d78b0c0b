# Generated by Django 5.2.5 on 2025-08-20 21:07

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('market_data', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FinancialInstrument',
            fields=[
                ('symbol', models.CharField(max_length=50, primary_key=True, serialize=False, unique=True)),
                ('label', models.CharField(max_length=200)),
                ('sector', models.CharField(max_length=100)),
                ('data_file', models.CharField(max_length=200)),
                ('predictions_file', models.CharField(blank=True, max_length=200, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('market_cap', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'financial_instruments',
                'ordering': ['symbol'],
            },
        ),
        migrations.CreateModel(
            name='MASIIndex',
            fields=[
                ('symbol', models.CharField(max_length=100, primary_key=True, serialize=False, unique=True)),
                ('label', models.CharField(max_length=200)),
                ('type', models.CharField(choices=[('MAIN', 'Main Index'), ('BLUE_CHIP', 'Blue Chip Index'), ('SECTORAL', 'Sectoral Index')], max_length=20)),
                ('description', models.TextField()),
                ('data_file', models.CharField(max_length=200)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'masi_indices',
                'ordering': ['symbol'],
            },
        ),
        migrations.CreateModel(
            name='InstrumentPrediction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('predicted_price', models.DecimalField(decimal_places=4, max_digits=15)),
                ('lower_ci', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('upper_ci', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('instrument', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='predictions', to='market_data.financialinstrument')),
            ],
            options={
                'db_table': 'instrument_predictions',
                'ordering': ['-date'],
                'indexes': [models.Index(fields=['instrument', 'date'], name='instrument__instrum_ae672c_idx'), models.Index(fields=['date'], name='instrument__date_9e58db_idx')],
                'unique_together': {('instrument', 'date')},
            },
        ),
        migrations.CreateModel(
            name='InstrumentPrice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('open_price', models.DecimalField(decimal_places=4, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.0001'))])),
                ('high_price', models.DecimalField(decimal_places=4, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.0001'))])),
                ('low_price', models.DecimalField(decimal_places=4, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.0001'))])),
                ('close_price', models.DecimalField(decimal_places=4, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.0001'))])),
                ('current_price', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('volume', models.BigIntegerField(validators=[django.core.validators.MinValueValidator(0)])),
                ('shares_traded', models.BigIntegerField(blank=True, null=True)),
                ('total_trades', models.IntegerField(blank=True, null=True)),
                ('market_cap', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('adjusted_close', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('consolidated_ratio', models.DecimalField(blank=True, decimal_places=6, max_digits=10, null=True)),
                ('instrument', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prices', to='market_data.financialinstrument')),
            ],
            options={
                'db_table': 'instrument_prices',
                'ordering': ['-date'],
                'indexes': [models.Index(fields=['instrument', 'date'], name='instrument__instrum_397f0b_idx'), models.Index(fields=['date'], name='instrument__date_b98cd8_idx'), models.Index(fields=['instrument', '-date'], name='instrument__instrum_20c8dd_idx')],
                'unique_together': {('instrument', 'date')},
            },
        ),
        migrations.CreateModel(
            name='MASIIndexValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('previous_value', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('daily_change', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('daily_change_pct', models.DecimalField(blank=True, decimal_places=4, max_digits=8, null=True)),
                ('index', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='values', to='market_data.masiindex')),
            ],
            options={
                'db_table': 'masi_index_values',
                'ordering': ['-date'],
                'indexes': [models.Index(fields=['index', 'date'], name='masi_index__index_i_d36193_idx'), models.Index(fields=['date'], name='masi_index__date_7a59c6_idx'), models.Index(fields=['index', '-date'], name='masi_index__index_i_d04053_idx')],
                'unique_together': {('index', 'date')},
            },
        ),
    ]
