#!/usr/bin/env python3
"""
Client API Casablanca Optimisé - Basé sur les URLs exactes fournies
URLs:
- Page 1: https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action?page%5Blimit%5D=50&page%5Boffset%5D=0
- Page 2: https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action?page%5Blimit%5D=50&page%5Boffset%5D=50
"""

import requests
import json
import logging
from datetime import datetime, date
from decimal import Decimal
import time
import os
import sys

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

try:
    import django
    django.setup()
    from django.db import connection, transaction
    DJANGO_AVAILABLE = True
except Exception as e:
    print(f"⚠️ Django non disponible: {e}")
    DJANGO_AVAILABLE = False

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/casablanca_api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CasablancaApiClientOptimized:
    """Client API optimisé pour la Bourse de Casablanca"""
    
    def __init__(self):
        self.base_url = "https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.casablanca-bourse.com/bourseweb/Negociation-Marche.aspx?Cat=24',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def fetch_page_data(self, page_offset=0, page_limit=50):
        """Récupérer les données d'une page spécifique"""
        try:
            # Utiliser l'URL exacte fournie
            params = {
                'page[limit]': page_limit,
                'page[offset]': page_offset
            }
            
            logger.info(f"Récupération page: offset={page_offset}, limit={page_limit}")
            
            response = self.session.get(
                self.base_url,
                params=params,
                timeout=15
            )
            
            logger.info(f"Réponse API: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    # Analyser la structure de la réponse
                    if isinstance(data, dict) and 'data' in data:
                        companies = data['data']
                        logger.info(f"✅ {len(companies)} entreprises récupérées")
                        
                        # Log de la structure pour debug
                        if len(companies) > 0:
                            first_company = companies[0]
                            logger.debug(f"Structure première entreprise: {list(first_company.keys())}")
                            if 'attributes' in first_company:
                                logger.debug(f"Attributs disponibles: {list(first_company['attributes'].keys())}")
                        
                        return companies
                    else:
                        logger.warning(f"Structure inattendue: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                        return []
                        
                except json.JSONDecodeError as e:
                    logger.error(f"Erreur JSON: {e}")
                    logger.debug(f"Réponse brute: {response.text[:500]}")
                    return []
            else:
                logger.error(f"Erreur HTTP {response.status_code}: {response.text[:200]}")
                return []
                
        except requests.exceptions.Timeout:
            logger.error("Timeout lors de la requête API")
            return []
        except requests.exceptions.ConnectionError:
            logger.error("Erreur de connexion à l'API")
            return []
        except Exception as e:
            logger.error(f"Erreur inattendue: {e}")
            return []
    
    def fetch_all_companies(self):
        """Récupérer toutes les entreprises en utilisant la pagination"""
        all_companies = []
        page_offset = 0
        page_limit = 50
        
        logger.info("🚀 Début récupération complète des entreprises")
        
        while True:
            companies = self.fetch_page_data(page_offset, page_limit)
            
            if not companies:
                logger.info(f"Fin de pagination à offset={page_offset}")
                break
            
            all_companies.extend(companies)
            logger.info(f"Page offset={page_offset}: {len(companies)} entreprises (+{len(all_companies)} total)")
            
            # Si moins d'entreprises que la limite, c'est la dernière page
            if len(companies) < page_limit:
                logger.info("Dernière page atteinte")
                break
            
            page_offset += page_limit
            
            # Pause entre les requêtes pour éviter la surcharge
            time.sleep(0.5)
        
        logger.info(f"✅ Total récupéré: {len(all_companies)} entreprises")
        return all_companies
    
    def parse_company_data(self, company_raw):
        """Parser les données d'une entreprise depuis l'API"""
        try:
            # Extraire l'ID
            company_id = company_raw.get('id')
            if not company_id:
                logger.warning("Entreprise sans ID, ignorée")
                return None
            
            # Extraire les attributs
            attributes = company_raw.get('attributes', {})
            if not attributes:
                logger.warning(f"Entreprise {company_id} sans attributs")
                return None
            
            # Mapping des champs API vers les champs de base de données
            parsed_data = {
                'api_id': str(company_id),
                'symbol': attributes.get('symbol', '').strip(),
                'company_name': attributes.get('instrument_name', '').strip(),
                'date_trade': date.today(),  # Date du jour par défaut
                
                # Prix (conversion en Decimal pour PostgreSQL)
                'open_price': self._safe_decimal(attributes.get('open_price')),
                'close_price': self._safe_decimal(attributes.get('close_price')),
                'high_price': self._safe_decimal(attributes.get('high_price')),
                'low_price': self._safe_decimal(attributes.get('low_price')),
                
                # Volume et valeur
                'volume': self._safe_int(attributes.get('volume')),
                'value_mad': self._safe_decimal(attributes.get('value')),
                
                # Variations
                'price_change': self._safe_decimal(attributes.get('variation')),
                'price_change_percent': self._safe_decimal(attributes.get('variation_percent')),
                
                # Autres champs possibles
                'shares_traded': self._safe_float(attributes.get('shares_traded')),
                'total_trades': self._safe_int(attributes.get('total_trades')),
                
                # Métadonnées
                'currency': attributes.get('currency', 'MAD'),
                'api_source': 'casablanca_api',
                'last_update': datetime.now()
            }
            
            # Validation des champs requis
            if not parsed_data['symbol']:
                logger.warning(f"Entreprise {company_id} sans symbole")
                return None
            
            logger.debug(f"✅ Données parsées pour {parsed_data['symbol']}: {parsed_data['close_price']} MAD")
            return parsed_data
            
        except Exception as e:
            logger.error(f"Erreur parsing entreprise {company_raw.get('id', 'UNKNOWN')}: {e}")
            return None
    
    def _safe_decimal(self, value):
        """Conversion sécurisée vers Decimal"""
        if value is None or value == '':
            return None
        try:
            return Decimal(str(value).replace(',', '.'))
        except:
            return None
    
    def _safe_int(self, value):
        """Conversion sécurisée vers int"""
        if value is None or value == '':
            return None
        try:
            return int(float(str(value).replace(',', '.')))
        except:
            return None
    
    def _safe_float(self, value):
        """Conversion sécurisée vers float"""
        if value is None or value == '':
            return None
        try:
            return float(str(value).replace(',', '.'))
        except:
            return None
    
    def save_company_to_database(self, company_data):
        """Sauvegarder une entreprise en base de données"""
        if not DJANGO_AVAILABLE:
            logger.error("Django non disponible pour la sauvegarde")
            return False
        
        try:
            with transaction.atomic():
                # 1. Insérer/mettre à jour l'entreprise
                company_id = self._upsert_company(company_data)
                
                if company_id:
                    # 2. Insérer les données de marché
                    return self._insert_market_data(company_id, company_data)
                
                return False
                
        except Exception as e:
            logger.error(f"Erreur sauvegarde {company_data.get('symbol', 'UNKNOWN')}: {e}")
            return False
    
    def _upsert_company(self, company_data):
        """Insérer ou mettre à jour l'entreprise dans XCapitalTerminal_Companies"""
        try:
            with connection.cursor() as cursor:
                # Vérifier si l'entreprise existe déjà
                cursor.execute(
                    'SELECT id FROM "XCapitalTerminal_Companies" WHERE symbol = %s',
                    [company_data['symbol']]
                )
                
                result = cursor.fetchone()
                
                if result:
                    # Mise à jour
                    company_id = result[0]
                    cursor.execute("""
                        UPDATE "XCapitalTerminal_Companies" 
                        SET name = %s, updated_at = NOW()
                        WHERE id = %s
                    """, [company_data['company_name'], company_id])
                    
                    logger.debug(f"Entreprise {company_data['symbol']} mise à jour (ID: {company_id})")
                else:
                    # Insertion
                    cursor.execute("""
                        INSERT INTO "XCapitalTerminal_Companies" 
                        (symbol, name, currency, created_at, updated_at)
                        VALUES (%s, %s, %s, NOW(), NOW())
                        RETURNING id
                    """, [
                        company_data['symbol'],
                        company_data['company_name'],
                        company_data['currency']
                    ])
                    
                    company_id = cursor.fetchone()[0]
                    logger.debug(f"Nouvelle entreprise {company_data['symbol']} créée (ID: {company_id})")
                
                return company_id
                
        except Exception as e:
            logger.error(f"Erreur upsert entreprise {company_data['symbol']}: {e}")
            return None
    
    def _insert_market_data(self, company_id, company_data):
        """Insérer les données de marché dans XCapitalTerminal_CompanyBonds"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO "XCapitalTerminal_CompanyBonds" (
                        company_id, date_trade, open_price, close_price,
                        high_price, low_price, volume, value_mad,
                        price_change, price_change_percent, shares_traded,
                        total_trades, created_at, updated_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW()
                    ) ON CONFLICT (company_id, date_trade) DO UPDATE SET
                        open_price = EXCLUDED.open_price,
                        close_price = EXCLUDED.close_price,
                        high_price = EXCLUDED.high_price,
                        low_price = EXCLUDED.low_price,
                        volume = EXCLUDED.volume,
                        value_mad = EXCLUDED.value_mad,
                        price_change = EXCLUDED.price_change,
                        price_change_percent = EXCLUDED.price_change_percent,
                        shares_traded = EXCLUDED.shares_traded,
                        total_trades = EXCLUDED.total_trades,
                        updated_at = NOW()
                """, [
                    company_id,
                    company_data['date_trade'],
                    company_data['open_price'],
                    company_data['close_price'],
                    company_data['high_price'],
                    company_data['low_price'],
                    company_data['volume'],
                    company_data['value_mad'],
                    company_data['price_change'],
                    company_data['price_change_percent'],
                    company_data['shares_traded'],
                    company_data['total_trades']
                ])
                
                logger.debug(f"Données marché sauvées pour entreprise ID {company_id}")
                return True
                
        except Exception as e:
            logger.error(f"Erreur insertion données marché pour ID {company_id}: {e}")
            return False

def update_all_companies():
    """Fonction principale pour mettre à jour toutes les entreprises"""
    logger.info("🚀 DÉBUT MISE À JOUR COMPLÈTE API CASABLANCA")
    
    start_time = datetime.now()
    
    # Créer le client API
    client = CasablancaApiClientOptimized()
    
    # Statistiques
    stats = {
        'companies_fetched': 0,
        'companies_parsed': 0,
        'companies_saved': 0,
        'companies_failed': 0,
        'errors': []
    }
    
    try:
        # 1. Récupérer toutes les entreprises
        logger.info("📥 Récupération des données API...")
        all_companies = client.fetch_all_companies()
        stats['companies_fetched'] = len(all_companies)
        
        if not all_companies:
            logger.error("❌ Aucune donnée récupérée de l'API")
            return {
                'success': False,
                'error': 'Aucune donnée API',
                'stats': stats
            }
        
        # 2. Parser et sauvegarder chaque entreprise
        logger.info(f"🔄 Traitement de {len(all_companies)} entreprises...")
        
        for i, company_raw in enumerate(all_companies, 1):
            try:
                # Parser les données
                company_data = client.parse_company_data(company_raw)
                
                if company_data:
                    stats['companies_parsed'] += 1
                    
                    # Sauvegarder en base
                    if client.save_company_to_database(company_data):
                        stats['companies_saved'] += 1
                        logger.info(f"✅ [{i}/{len(all_companies)}] {company_data['symbol']}: {company_data['close_price']} MAD")
                    else:
                        stats['companies_failed'] += 1
                        logger.warning(f"❌ [{i}/{len(all_companies)}] Échec sauvegarde {company_data['symbol']}")
                else:
                    stats['companies_failed'] += 1
                    logger.warning(f"❌ [{i}/{len(all_companies)}] Échec parsing")
                    
            except Exception as e:
                stats['companies_failed'] += 1
                stats['errors'].append(str(e))
                logger.error(f"❌ [{i}/{len(all_companies)}] Erreur: {e}")
        
        # 3. Résumé final
        duration = (datetime.now() - start_time).total_seconds()
        
        logger.info(f"✅ MISE À JOUR TERMINÉE en {duration:.2f}s")
        logger.info(f"📊 Récupérées: {stats['companies_fetched']}")
        logger.info(f"📊 Parsées: {stats['companies_parsed']}")
        logger.info(f"📊 Sauvées: {stats['companies_saved']}")
        logger.info(f"📊 Échecs: {stats['companies_failed']}")
        
        return {
            'success': True,
            'stats': stats,
            'duration_seconds': duration
        }
        
    except Exception as e:
        logger.error(f"❌ ERREUR CRITIQUE: {e}")
        return {
            'success': False,
            'error': str(e),
            'stats': stats
        }

# Instance globale
casablanca_client = CasablancaApiClientOptimized()

if __name__ == "__main__":
    # Test direct
    result = update_all_companies()
    
    if result['success']:
        print(f"🎉 Mise à jour réussie!")
        print(f"   Entreprises sauvées: {result['stats']['companies_saved']}")
        print(f"   Durée: {result['duration_seconds']:.2f}s")
    else:
        print(f"❌ Échec de la mise à jour: {result.get('error', 'Erreur inconnue')}")
        sys.exit(1)
