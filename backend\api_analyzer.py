#!/usr/bin/env python3
"""
Analyseur API Casablanca - Analyse du format JSON
"""

import requests
import json
from datetime import datetime
import sys

def analyze_api_response():
    """Analyser la structure des réponses API"""
    print("🔍 ANALYSE API CASABLANCA - FORMAT JSON")
    print("=" * 60)
    
    # URLs à analyser
    urls = [
        "https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action?page%5Blimit%5D=50&page%5Boffset%5D=0",
        "https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action?page%5Blimit%5D=50&page%5Boffset%5D=50"
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://www.casablanca-bourse.com/bourseweb/Negociation-Marche.aspx?Cat=24',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    
    all_companies = []
    
    for i, url in enumerate(urls, 1):
        print(f"\n📄 ANALYSE PAGE {i}")
        print("-" * 30)
        print(f"URL: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=15)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ JSON valide")
                    
                    # Analyser la structure
                    print(f"📊 Type réponse: {type(data)}")
                    
                    if isinstance(data, dict):
                        print(f"📋 Clés principales: {list(data.keys())}")
                        
                        # Analyser la section 'data'
                        if 'data' in data:
                            companies = data['data']
                            print(f"🏢 Nombre d'entreprises: {len(companies)}")
                            
                            if len(companies) > 0:
                                # Analyser le premier élément en détail
                                first_company = companies[0]
                                print(f"\n📋 STRUCTURE PREMIÈRE ENTREPRISE:")
                                print(f"   Type: {type(first_company)}")
                                print(f"   Clés: {list(first_company.keys())}")
                                
                                # Analyser l'ID
                                if 'id' in first_company:
                                    print(f"   ID: {first_company['id']} (type: {type(first_company['id'])})")
                                
                                # Analyser les attributs
                                if 'attributes' in first_company:
                                    attrs = first_company['attributes']
                                    print(f"\n📊 ATTRIBUTS DISPONIBLES:")
                                    for key, value in attrs.items():
                                        print(f"      {key}: {value} (type: {type(value)})")
                                
                                # Analyser les relations
                                if 'relationships' in first_company:
                                    rels = first_company['relationships']
                                    print(f"\n🔗 RELATIONS:")
                                    print(f"      Clés: {list(rels.keys())}")
                                
                                # Sauvegarder pour analyse complète
                                all_companies.extend(companies)
                        
                        # Analyser les métadonnées
                        if 'meta' in data:
                            meta = data['meta']
                            print(f"\n📊 MÉTADONNÉES:")
                            for key, value in meta.items():
                                print(f"      {key}: {value}")
                        
                        if 'links' in data:
                            links = data['links']
                            print(f"\n🔗 LIENS:")
                            for key, value in links.items():
                                print(f"      {key}: {value}")
                                
                    elif isinstance(data, list):
                        print(f"📊 Liste de {len(data)} éléments")
                        if len(data) > 0:
                            first_item = data[0]
                            print(f"Premier élément: {type(first_item)}")
                            if isinstance(first_item, dict):
                                print(f"Clés: {list(first_item.keys())}")
                    
                except json.JSONDecodeError as e:
                    print(f"❌ Erreur JSON: {e}")
                    print(f"Réponse brute (500 premiers caractères):")
                    print(response.text[:500])
            else:
                print(f"❌ Erreur HTTP: {response.status_code}")
                print(f"Réponse: {response.text[:200]}")
                
        except requests.exceptions.Timeout:
            print("❌ Timeout")
        except requests.exceptions.ConnectionError:
            print("❌ Erreur de connexion")
        except Exception as e:
            print(f"❌ Erreur: {e}")
    
    # Analyse globale
    if all_companies:
        print(f"\n📊 ANALYSE GLOBALE")
        print("=" * 30)
        print(f"Total entreprises récupérées: {len(all_companies)}")
        
        # Analyser les champs communs
        if len(all_companies) > 0:
            print(f"\n📋 CHAMPS DISPONIBLES DANS TOUTES LES ENTREPRISES:")
            
            # Prendre quelques échantillons
            sample_size = min(5, len(all_companies))
            samples = all_companies[:sample_size]
            
            all_attr_keys = set()
            for company in samples:
                if 'attributes' in company:
                    all_attr_keys.update(company['attributes'].keys())
            
            print(f"Champs attributs trouvés: {sorted(all_attr_keys)}")
            
            # Analyser les valeurs typiques
            print(f"\n📊 EXEMPLES DE VALEURS:")
            for i, company in enumerate(samples[:3]):
                if 'attributes' in company:
                    attrs = company['attributes']
                    print(f"\nEntreprise {i+1}:")
                    for key in ['symbol', 'instrument_name', 'close_price', 'volume', 'value', 'variation', 'variation_percent']:
                        if key in attrs:
                            print(f"   {key}: {attrs[key]} (type: {type(attrs[key])})")
    
    return all_companies

def analyze_database_structure():
    """Analyser la structure de la table XCapitalTerminal_CompanyBonds"""
    print(f"\n🗄️ ANALYSE STRUCTURE BASE DE DONNÉES")
    print("=" * 50)
    
    try:
        import os
        import django
        
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
        django.setup()
        
        from django.db import connection
        
        # Analyser la table XCapitalTerminal_CompanyBonds
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'XCapitalTerminal_CompanyBonds'
                ORDER BY ordinal_position
            """)
            
            columns = cursor.fetchall()
            
            print("📋 COLONNES XCapitalTerminal_CompanyBonds:")
            print("-" * 40)
            for col in columns:
                name, data_type, nullable, default = col
                print(f"   {name}: {data_type} {'(nullable)' if nullable == 'YES' else '(required)'}")
                if default:
                    print(f"      Default: {default}")
        
        # Analyser la table XCapitalTerminal_Companies
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'XCapitalTerminal_Companies'
                ORDER BY ordinal_position
            """)
            
            columns = cursor.fetchall()
            
            print(f"\n📋 COLONNES XCapitalTerminal_Companies:")
            print("-" * 40)
            for col in columns:
                name, data_type, nullable, default = col
                print(f"   {name}: {data_type} {'(nullable)' if nullable == 'YES' else '(required)'}")
                if default:
                    print(f"      Default: {default}")
        
        # Vérifier les relations
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    tc.constraint_name,
                    tc.table_name,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name 
                FROM 
                    information_schema.table_constraints AS tc 
                    JOIN information_schema.key_column_usage AS kcu
                      ON tc.constraint_name = kcu.constraint_name
                    JOIN information_schema.constraint_column_usage AS ccu
                      ON ccu.constraint_name = tc.constraint_name
                WHERE tc.constraint_type = 'FOREIGN KEY' 
                AND tc.table_name = 'XCapitalTerminal_CompanyBonds'
            """)
            
            foreign_keys = cursor.fetchall()
            
            print(f"\n🔗 RELATIONS (FOREIGN KEYS):")
            print("-" * 30)
            for fk in foreign_keys:
                constraint_name, table_name, column_name, foreign_table, foreign_column = fk
                print(f"   {column_name} -> {foreign_table}.{foreign_column}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur base de données: {e}")
        return False

def create_mapping_guide(companies_data):
    """Créer un guide de mapping API -> Base de données"""
    print(f"\n🗺️ GUIDE DE MAPPING API -> BASE DE DONNÉES")
    print("=" * 60)
    
    if not companies_data:
        print("❌ Aucune donnée API disponible pour le mapping")
        return
    
    # Champs API typiques (basés sur l'analyse)
    api_fields = [
        'id', 'symbol', 'instrument_name', 'close_price', 'open_price',
        'high_price', 'low_price', 'volume', 'value', 'variation',
        'variation_percent', 'last_trade_date', 'currency'
    ]
    
    # Mapping probable vers la base de données
    field_mapping = {
        'id': 'company_id (relation vers XCapitalTerminal_Companies)',
        'symbol': 'symbol (dans XCapitalTerminal_Companies)',
        'instrument_name': 'company_name (dans XCapitalTerminal_Companies)',
        'close_price': 'close_price',
        'open_price': 'open_price', 
        'high_price': 'high_price',
        'low_price': 'low_price',
        'volume': 'volume',
        'value': 'value_mad',
        'variation': 'price_change',
        'variation_percent': 'price_change_percent',
        'last_trade_date': 'date_trade'
    }
    
    print("📋 MAPPING PROPOSÉ:")
    print("-" * 30)
    for api_field, db_field in field_mapping.items():
        print(f"   API.{api_field} -> DB.{db_field}")
    
    # Vérifier quels champs sont disponibles dans les données réelles
    if len(companies_data) > 0:
        sample = companies_data[0]
        if 'attributes' in sample:
            available_fields = sample['attributes'].keys()
            
            print(f"\n✅ CHAMPS DISPONIBLES DANS L'API:")
            print("-" * 35)
            for field in api_fields:
                if field in available_fields:
                    print(f"   ✅ {field}")
                else:
                    print(f"   ❌ {field} (non disponible)")

def main():
    """Analyse principale"""
    print("🔍 ANALYSEUR API CASABLANCA")
    print("=" * 50)
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. Analyser les réponses API
    companies_data = analyze_api_response()
    
    # 2. Analyser la structure de la base de données
    db_analysis_success = analyze_database_structure()
    
    # 3. Créer le guide de mapping
    create_mapping_guide(companies_data)
    
    # 4. Résumé et recommandations
    print(f"\n🎯 RÉSUMÉ ET RECOMMANDATIONS")
    print("=" * 40)
    
    if companies_data:
        print(f"✅ API accessible - {len(companies_data)} entreprises analysées")
    else:
        print(f"❌ API inaccessible - Utiliser les données mock")
    
    if db_analysis_success:
        print(f"✅ Base de données analysée")
    else:
        print(f"❌ Problème d'accès à la base de données")
    
    print(f"\n📋 PROCHAINES ÉTAPES:")
    print("   1. Vérifier la structure des champs API vs DB")
    print("   2. Adapter le casablanca_api_client.py")
    print("   3. Tester l'insertion des données")
    print("   4. Valider les relations company_id")

if __name__ == "__main__":
    main()
