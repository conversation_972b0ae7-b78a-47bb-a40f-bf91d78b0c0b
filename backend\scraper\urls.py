"""
URLs pour l'API du scraper Casablanca Stock Exchange
"""

from django.urls import path
from . import views

app_name = 'scraper'

urlpatterns = [
    # Dashboard HTML
    path('dashboard/', views.dashboard_view, name='dashboard'),

    # API Casablanca Stock Exchange
    path('trigger-casablanca/', views.trigger_casablanca_scraper, name='trigger_casablanca_scraper'),
    path('test-casablanca-connectivity/', views.test_casablanca_connectivity, name='test_casablanca_connectivity'),
    path('casablanca-dashboard/', views.casablanca_dashboard, name='casablanca_dashboard'),

    # API de test et legacy
    path('test/trigger/', views.trigger_test_scraper, name='trigger_test_scraper'),
    path('test/celery/', views.test_celery_connection, name='test_celery_connection'),
    path('test/database/', views.test_database_tables, name='test_database_tables'),
    path('test/status/', views.phase2_status, name='phase2_status'),

    # API commune
    path('task-status/<str:task_id>/', views.task_status, name='task_status'),
]
