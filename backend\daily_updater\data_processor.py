"""
Processeur de données pour la Bourse de Casablanca
Traite et enrichit les données validées
"""

import logging
import json
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import pandas as pd
from decimal import Decimal

from config import Config

class DataProcessor:
    """
    Processeur et enrichisseur de données financières
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Statistiques de traitement
        self.companies_processed = 0
        self.records_processed = 0
        self.processing_errors = []
    
    def process_company_data(self, company_id: int, company_name: str, 
                           validated_records: List[Dict]) -> Tuple[pd.DataFrame, Dict]:
        """
        Traite et enrichit les données d'une entreprise
        
        Args:
            company_id: ID de l'entreprise
            company_name: Nom de l'entreprise
            validated_records: Enregistrements validés
        
        Returns:
            Tuple (processed_dataframe, daily_summary)
        """
        self.companies_processed += 1
        self.logger.info(f"🔄 Traitement des données - {company_name} ({len(validated_records)} enregistrements)")
        
        if not validated_records:
            return pd.DataFrame(), self.get_empty_summary(company_id, company_name)
        
        # Convertir en DataFrame
        df = pd.DataFrame(validated_records)
        
        # Conversion des types
        df['date'] = pd.to_datetime(df['date'])
        df['date_only'] = df['date'].dt.date
        
        # Colonnes numériques
        numeric_columns = ['opening_price', 'closing_price', 'high_price', 'low_price',
                          'quantity', 'volume', 'total_trades', 'variation', 
                          'variation_percent', 'last_price', 'average_price']
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Tri par date
        df = df.sort_values('date')
        
        # Enrichissement des données
        df = self.enrich_data(df)
        
        # Agrégation par jour (si plusieurs enregistrements par jour)
        daily_df = self.aggregate_daily_data(df)
        
        # Création du résumé quotidien
        daily_summary = self.create_daily_summary(company_id, company_name, daily_df)
        
        self.records_processed += len(validated_records)
        self.logger.info(f"✅ Traitement terminé - {company_name}")
        
        return daily_df, daily_summary
    
    def enrich_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Enrichit les données avec des calculs supplémentaires"""
        try:
            if df.empty:
                return df
            
            # Calculs de variations
            df['price_range'] = df['high_price'] - df['low_price']
            df['price_range_pct'] = ((df['high_price'] - df['low_price']) / df['low_price'] * 100).round(4)
            
            # Volume moyen pondéré par le prix (VWAP approximatif)
            df['vwap_approx'] = ((df['high_price'] + df['low_price'] + df['closing_price']) / 3).round(4)
            
            # Volatilité intraday
            df['intraday_volatility'] = (df['price_range'] / df['opening_price'] * 100).round(4)
            
            # Momentum simple
            df['momentum'] = ((df['closing_price'] - df['opening_price']) / df['opening_price'] * 100).round(4)
            
            # Indicateur de liquidité
            df['liquidity_indicator'] = df['volume'] * df['closing_price']
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ Erreur enrichissement: {e}")
            return df
    
    def aggregate_daily_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Agrège les données par jour"""
        try:
            if df.empty:
                return df
            
            # Grouper par date (jour uniquement)
            daily_agg = df.groupby('date_only').agg({
                'opening_price': 'first',  # Premier prix d'ouverture du jour
                'closing_price': 'last',   # Dernier prix de fermeture du jour
                'high_price': 'max',       # Prix le plus haut du jour
                'low_price': 'min',        # Prix le plus bas du jour
                'volume': 'sum',           # Volume total du jour
                'quantity': 'sum',         # Quantité totale du jour
                'total_trades': 'sum',     # Total des transactions du jour
                'liquidity_indicator': 'sum',  # Liquidité totale du jour
                'company_id': 'first',
                'company_name': 'first'
            }).reset_index()
            
            # Recalculer les indicateurs sur les données agrégées
            daily_agg['daily_change'] = daily_agg['closing_price'] - daily_agg['opening_price']
            daily_agg['daily_change_pct'] = ((daily_agg['closing_price'] - daily_agg['opening_price']) / daily_agg['opening_price'] * 100).round(4)
            daily_agg['price_range'] = daily_agg['high_price'] - daily_agg['low_price']
            daily_agg['price_range_pct'] = ((daily_agg['high_price'] - daily_agg['low_price']) / daily_agg['low_price'] * 100).round(4)
            
            # VWAP quotidien
            daily_agg['daily_vwap'] = ((daily_agg['high_price'] + daily_agg['low_price'] + daily_agg['closing_price']) / 3).round(4)
            
            # Volatilité quotidienne
            daily_agg['daily_volatility'] = (daily_agg['price_range'] / daily_agg['opening_price'] * 100).round(4)
            
            return daily_agg
            
        except Exception as e:
            self.logger.error(f"❌ Erreur agrégation: {e}")
            return df
    
    def create_daily_summary(self, company_id: int, company_name: str, 
                           daily_df: pd.DataFrame) -> Dict:
        """Crée un résumé quotidien pour l'entreprise"""
        try:
            if daily_df.empty:
                return self.get_empty_summary(company_id, company_name)
            
            # Prendre les données du jour le plus récent
            latest_data = daily_df.iloc[-1]
            
            summary = {
                'company_id': company_id,
                'company_name': company_name,
                'date': latest_data['date_only'].strftime('%Y-%m-%d'),
                'opening_price': float(latest_data['opening_price']),
                'closing_price': float(latest_data['closing_price']),
                'high_price': float(latest_data['high_price']),
                'low_price': float(latest_data['low_price']),
                'volume': int(latest_data['volume']),
                'quantity': int(latest_data['quantity']),
                'total_trades': int(latest_data['total_trades']),
                'daily_change': float(latest_data['daily_change']),
                'daily_change_pct': float(latest_data['daily_change_pct']),
                'price_range': float(latest_data['price_range']),
                'daily_vwap': float(latest_data['daily_vwap']),
                'daily_volatility': float(latest_data['daily_volatility']),
                'liquidity_indicator': float(latest_data['liquidity_indicator']),
                'records_count': len(daily_df),
                'processing_timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"❌ Erreur création résumé: {e}")
            return self.get_empty_summary(company_id, company_name)
    
    def get_empty_summary(self, company_id: int, company_name: str) -> Dict:
        """Retourne un résumé vide pour une entreprise sans données"""
        return {
            'company_id': company_id,
            'company_name': company_name,
            'date': Config.get_target_date(),
            'opening_price': 0.0,
            'closing_price': 0.0,
            'high_price': 0.0,
            'low_price': 0.0,
            'volume': 0,
            'quantity': 0,
            'total_trades': 0,
            'daily_change': 0.0,
            'daily_change_pct': 0.0,
            'price_range': 0.0,
            'daily_vwap': 0.0,
            'daily_volatility': 0.0,
            'liquidity_indicator': 0.0,
            'records_count': 0,
            'processing_timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'no_data'
        }
    
    def save_processed_data(self, df: pd.DataFrame, company_id: int, 
                          company_name: str, target_date: str) -> str:
        """Sauvegarde les données traitées en CSV"""
        try:
            # Nom de fichier sécurisé
            safe_name = company_name.replace(' ', '_').replace('/', '_').replace('\\', '_')
            filename = f"{company_id}_{safe_name}_{target_date}.csv"
            filepath = Config.PROCESSED_DATA_DIR / filename
            
            # Sauvegarder en CSV
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            self.logger.info(f"💾 Données sauvegardées: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"❌ Erreur sauvegarde CSV: {e}")
            return ""
    
    def save_daily_summary(self, summary: Dict, target_date: str) -> str:
        """Sauvegarde le résumé quotidien en JSON"""
        try:
            filename = f"summary_{summary['company_id']}_{target_date}.json"
            filepath = Config.DAILY_DATA_DIR / filename
            
            # Sauvegarder en JSON
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            self.logger.debug(f"💾 Résumé sauvegardé: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"❌ Erreur sauvegarde résumé: {e}")
            return ""
    
    def get_processing_statistics(self) -> Dict:
        """Retourne les statistiques de traitement"""
        return {
            'companies_processed': self.companies_processed,
            'records_processed': self.records_processed,
            'total_errors': len(self.processing_errors),
            'recent_errors': self.processing_errors[-5:] if self.processing_errors else []
        }
