# Core Django packages
Django==5.2.5
djangorestframework==3.15.2

# Database
psycopg2-binary==2.9.9

# CORS handling
django-cors-headers==4.3.1

# Filtering for DRF
django-filter==23.3

# Data analysis and manipulation
pandas==2.1.3
numpy==1.25.2

# Machine learning and statistical analysis
scikit-learn==1.3.2
statsmodels==0.14.1

# Date and time utilities
python-dateutil==2.8.2

# HTTP requests
requests==2.31.0

# Development and debugging
python-decouple==3.8

# Timezone handling
pytz==2023.3

# Environment variables management
python-dotenv==1.0.0

# Caching (Redis support if needed)
redis==5.0.1
django-redis==5.4.0

# Performance monitoring
django-debug-toolbar==4.2.0

# API documentation
drf-spectacular==0.26.5

# File handling and utilities
pathlib2==2.3.7

# Logging and monitoring
structlog==23.2.0

# Security
cryptography==41.0.7

# Testing
pytest==7.4.3
pytest-django==4.7.0
factory-boy==3.3.0

# Code quality
flake8==6.1.0
black==23.11.0
isort==5.12.0

# Production server
gunicorn==21.2.0
whitenoise==6.6.0

# AWS SDK (if using AWS services)
boto3==1.34.0
botocore==1.34.0

# Pillow for image handling (if needed)
Pillow==10.1.0

# JSON Web Token support (if authentication needed)
PyJWT==2.8.0

# Background tasks (if needed)
celery==5.3.4
django-celery-beat==2.7.0
django-celery-results==2.5.1

# Email backend
django-anymail==10.2

# Static file compression
django-compressor==4.4

# Admin interface enhancements
django-admin-interface==0.28.6

# API rate limiting
django-ratelimit==4.1.0

# Model utilities
django-model-utils==4.3.1

# Data validation
cerberus==1.3.5

# Excel file support (if needed for data import)
openpyxl==3.1.2
xlsxwriter==3.1.9

# CSV handling enhancements
csvkit==1.1.1

# Mathematical computations
scipy==1.11.4

# Plotting (if charts generation needed)
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# Financial calculations
QuantLib-Python==1.18

# WebSocket support (if real-time features needed)
channels==4.0.0
channels-redis==4.1.0

# Health checks
django-health-check==3.17.0

# API versioning
djangorestframework-api-key==3.0.0

# Data serialization
msgpack==1.0.7

# Memory profiling (development)
memory-profiler==0.61.0

# Performance profiling
django-silk==5.0.4

# Monitoring and APM
sentry-sdk==1.38.0

# Configuration management
django-configurations==2.5

# Database connection pooling
django-db-connection-pool==1.2.4

# Backup utilities
django-dbbackup==4.0.2

# Search functionality (if needed)
elasticsearch==8.11.0
django-elasticsearch-dsl==7.3

# Cache warming
django-cache-machine==1.2.0

# API throttling
djangorestframework-simplejwt==5.3.0

# Time series analysis (additional)
arch==6.2.0
pykalman==0.9.5

# Financial data providers APIs
yfinance==0.2.22
alpha-vantage==2.3.1
quandl==3.7.0

# Data quality and cleaning
ydata-profiling==4.6.4
great-expectations==0.18.8

# Task queue management
dramatiq==1.14.2

# Configuration file formats
PyYAML==6.0.1
toml==0.10.2

# Utilities
python-slugify==8.0.1
python-magic==0.4.27
humanize==4.8.0

# Development tools
ipython==8.17.2
jupyter==1.0.0
