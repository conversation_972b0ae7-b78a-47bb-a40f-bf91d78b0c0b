"""
Configuration Celery pour XCapital Backend
"""

import os
from celery import Celery
from django.conf import settings

# Définir le module de configuration Django pour Celery
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

# Créer l'instance Celery
app = Celery('xcapital_backend')

# Configuration Celery à partir des settings Django
app.config_from_object('django.conf:settings', namespace='CELERY')

# Découverte automatique des tasks dans les apps Django
app.autodiscover_tasks()

# Configuration pour Redis comme broker et backend
app.conf.update(
    broker_connection_retry_on_startup=True,
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # Configuration pour les retry
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    
    # Configuration pour les locks Redis
    task_routes={
        'scraper.tasks.run_scraper': {'queue': 'scraper'},
    },
    
    # Configuration pour les logs
    worker_log_format='[%(asctime)s: %(levelname)s/%(processName)s] %(message)s',
    worker_task_log_format='[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s',
)

# Configuration pour les tâches périodiques
from celery.schedules import crontab

app.conf.beat_schedule = {
    # Tâche principale de scraping quotidien Casablanca Stock Exchange à 19:00
    'casablanca-daily-scraper': {
        'task': 'scraper.tasks.run_casablanca_scraper',
        'schedule': crontab(hour=19, minute=0),  # Tous les jours à 19:00 (après fermeture des marchés)
        'options': {
            'expires': 3600,  # Expirer après 1 heure si pas exécutée
            'retry': True,
            'retry_policy': {
                'max_retries': 3,
                'interval_start': 60,  # 1 minute
                'interval_step': 60,   # Augmenter de 1 minute à chaque retry
                'interval_max': 300,   # Maximum 5 minutes
            }
        }
    },

    # Tâche de test pour vérifier la connectivité API (tous les jours à 18:30)
    'casablanca-api-health-check': {
        'task': 'scraper.tasks.test_casablanca_api_connectivity',
        'schedule': crontab(hour=18, minute=30),  # 30 minutes avant le scraping principal
        'options': {
            'expires': 300,  # Expirer après 5 minutes
        }
    },

    # Tâche de nettoyage hebdomadaire (dimanche à 02:00)
    'weekly-cleanup': {
        'task': 'scraper.tasks.cleanup_old_data',
        'schedule': crontab(hour=2, minute=0, day_of_week=0),  # Dimanche à 02:00
        'kwargs': {'days_to_keep': 30},  # Garder 30 jours de données
    },

    # Tâche de maintenance mensuelle (premier du mois à 03:00)
    'monthly-maintenance': {
        'task': 'scraper.tasks.monthly_maintenance',
        'schedule': crontab(hour=3, minute=0, day_of_month=1),  # Premier du mois à 03:00
        'options': {
            'expires': 7200,  # Expirer après 2 heures
        }
    },
}

@app.task(bind=True)
def debug_task(self):
    """Task de debug pour tester Celery"""
    print(f'Request: {self.request!r}')
