from rest_framework import serializers
from .models import Stock, StockPrice, MarketIndex, IndexPrice, TradingSession
from decimal import Decimal


class StockSerializer(serializers.ModelSerializer):
    class Meta:
        model = Stock
        fields = ['ticker', 'name', 'sector', 'market_cap', 'currency', 'is_active', 'created_at', 'updated_at']


class StockPriceSerializer(serializers.ModelSerializer):
    daily_return = serializers.ReadOnlyField()
    
    class Meta:
        model = StockPrice
        fields = [
            'date', 'open_price', 'high_price', 'low_price', 
            'close_price', 'volume', 'adj_close', 'daily_return'
        ]


class StockWithPricesSerializer(serializers.ModelSerializer):
    prices = StockPriceSerializer(many=True, read_only=True)
    latest_price = serializers.SerializerMethodField()
    
    class Meta:
        model = Stock
        fields = ['ticker', 'name', 'sector', 'market_cap', 'currency', 'is_active', 'prices', 'latest_price']
    
    def get_latest_price(self, obj):
        latest = obj.prices.first()
        return StockPriceSerializer(latest).data if latest else None


class MarketIndexSerializer(serializers.ModelSerializer):
    class Meta:
        model = MarketIndex
        fields = ['code', 'name', 'description', 'currency', 'is_active', 'created_at', 'updated_at']


class IndexPriceSerializer(serializers.ModelSerializer):
    class Meta:
        model = IndexPrice
        fields = [
            'date', 'open_value', 'high_value', 'low_value', 
            'close_value', 'volume'
        ]


class TradingSessionSerializer(serializers.ModelSerializer):
    class Meta:
        model = TradingSession
        fields = [
            'date', 'is_trading_day', 'session_start', 'session_end',
            'total_volume', 'total_value', 'notes'
        ]


class StockPriceRangeSerializer(serializers.Serializer):
    """Serializer for querying stock prices within a date range"""
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    
    def validate(self, data):
        if data['start_date'] > data['end_date']:
            raise serializers.ValidationError("Start date must be before end date")
        return data


class BulkStockDataSerializer(serializers.Serializer):
    """Serializer for bulk stock data operations"""
    tickers = serializers.ListField(
        child=serializers.CharField(max_length=20),
        min_length=1,
        max_length=50
    )
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    include_volume = serializers.BooleanField(default=True)
    
    def validate(self, data):
        if data['start_date'] > data['end_date']:
            raise serializers.ValidationError("Start date must be before end date")
        return data
