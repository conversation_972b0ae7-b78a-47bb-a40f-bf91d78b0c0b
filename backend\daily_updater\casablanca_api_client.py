"""
Client API pour la Bourse de Casablanca
Gère toutes les interactions avec l'API officielle
"""

import requests
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import urllib3

from config import Config

# Désactiver les avertissements SSL
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class CasablancaBourseAPIClient:
    """
    Client API pour la Bourse de Casablanca
    """
    
    def __init__(self):
        self.base_url = Config.CASABLANCA_BASE_URL
        self.session = requests.Session()
        self.session.headers.update(Config.DEFAULT_HEADERS)
        self.session.verify = False
        
        # Configuration du logging
        self.logger = logging.getLogger(__name__)
        
        # Statistiques
        self.requests_made = 0
        self.successful_requests = 0
        self.failed_requests = 0
    
    def build_api_url(self, endpoint: str = None) -> str:
        """Construit l'URL de l'API"""
        if endpoint is None:
            endpoint = Config.INSTRUMENTS_ENDPOINT
        return f"{self.base_url}{endpoint}"
    
    def build_request_params(self, company_id: int, target_date: str = None, 
                           page_offset: int = 0, page_limit: int = None) -> Dict:
        """
        Construit les paramètres de requête pour l'API
        
        Args:
            company_id: ID de l'entreprise
            target_date: Date cible (YYYY-MM-DD)
            page_offset: Offset de pagination
            page_limit: Limite de pagination
        
        Returns:
            Dict des paramètres
        """
        if target_date is None:
            target_date = Config.get_target_date()
        
        if page_limit is None:
            page_limit = Config.DEFAULT_PAGE_SIZE
        
        params = {
            'include': 'emetteur',
            'page[offset]': page_offset,
            'page[limit]': page_limit,
            'filter[emetteur]': company_id,
            'filter[dateTime][gte]': f"{target_date}T00:00:00.000Z",
            'filter[dateTime][lte]': f"{target_date}T23:59:59.999Z",
            'sort': '-dateTime'
        }
        
        return params
    
    def make_api_request(self, company_id: int, target_date: str = None,
                        page_offset: int = 0, page_limit: int = None) -> Tuple[bool, Optional[Dict]]:
        """
        Effectue une requête API vers la Bourse de Casablanca
        
        Args:
            company_id: ID de l'entreprise
            target_date: Date cible
            page_offset: Offset de pagination
            page_limit: Limite de pagination
        
        Returns:
            Tuple (success, data)
        """
        self.requests_made += 1
        
        try:
            url = self.build_api_url()
            params = self.build_request_params(company_id, target_date, page_offset, page_limit)
            
            self.logger.debug(f"Requête API: {url} - Params: {params}")
            
            response = self.session.get(
                url,
                params=params,
                timeout=Config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    self.successful_requests += 1
                    self.logger.debug(f"✅ Succès API - Entreprise {company_id}")
                    return True, data
                    
                except json.JSONDecodeError as e:
                    self.failed_requests += 1
                    self.logger.error(f"❌ Erreur JSON - Entreprise {company_id}: {e}")
                    return False, None
            
            else:
                self.failed_requests += 1
                self.logger.error(f"❌ Erreur HTTP {response.status_code} - Entreprise {company_id}")
                return False, None
        
        except requests.exceptions.RequestException as e:
            self.failed_requests += 1
            self.logger.error(f"❌ Erreur requête - Entreprise {company_id}: {e}")
            return False, None
    
    def fetch_company_data_with_pagination(self, company_id: int, target_date: str = None) -> Tuple[bool, str, List[Dict]]:
        """
        Récupère toutes les données d'une entreprise avec pagination
        
        Args:
            company_id: ID de l'entreprise
            target_date: Date cible
        
        Returns:
            Tuple (success, company_name, data_records)
        """
        if target_date is None:
            target_date = Config.get_target_date()
        
        self.logger.info(f"📡 Récupération données - Entreprise {company_id} - Date {target_date}")
        
        all_data = []
        company_name = f"Entreprise_{company_id}"
        page_offset = 0
        
        # Pagination avec deux requêtes principales
        for offset in [0, Config.DEFAULT_PAGE_SIZE]:
            success, response_data = self.make_api_request(
                company_id=company_id,
                target_date=target_date,
                page_offset=offset,
                page_limit=Config.DEFAULT_PAGE_SIZE
            )
            
            if success and response_data:
                # Extraire le nom de l'entreprise
                if 'included' in response_data and len(response_data['included']) > 0:
                    company_info = response_data['included'][0].get('attributes', {})
                    company_name = (company_info.get('libelleFR') or 
                                  company_info.get('libelleEN') or 
                                  company_info.get('libelleAR') or 
                                  f"Entreprise_{company_id}")
                
                # Extraire les données
                if 'data' in response_data and len(response_data['data']) > 0:
                    all_data.extend(response_data['data'])
            
            # Délai entre les requêtes
            time.sleep(Config.REQUEST_DELAY)
        
        if all_data:
            self.logger.info(f"✅ {len(all_data)} enregistrements récupérés pour {company_name}")
            return True, company_name, all_data
        else:
            self.logger.warning(f"⚠️ Aucune donnée pour l'entreprise {company_id}")
            return False, company_name, []
    
    def fetch_company_data_with_retries(self, company_id: int, target_date: str = None) -> Tuple[bool, str, List[Dict]]:
        """
        Récupère les données d'une entreprise avec gestion des tentatives
        """
        for attempt in range(Config.MAX_RETRIES):
            try:
                success, company_name, data = self.fetch_company_data_with_pagination(company_id, target_date)
                
                if success:
                    return True, company_name, data
                
                if attempt < Config.MAX_RETRIES - 1:
                    self.logger.warning(f"🔄 Tentative {attempt + 1}/{Config.MAX_RETRIES} échouée pour entreprise {company_id}")
                    time.sleep(Config.RETRY_DELAY)
                
            except Exception as e:
                self.logger.error(f"❌ Erreur tentative {attempt + 1} pour entreprise {company_id}: {e}")
                if attempt < Config.MAX_RETRIES - 1:
                    time.sleep(Config.RETRY_DELAY)
        
        return False, f"Entreprise_{company_id}", []
    
    def test_connection(self) -> bool:
        """Teste la connexion à l'API"""
        try:
            self.logger.info("🔍 Test de connexion à l'API...")
            
            # Test avec une entreprise connue (Attijariwafa Bank)
            success, _, _ = self.fetch_company_data_with_pagination(511, Config.get_target_date())
            
            if success:
                self.logger.info("✅ Connexion API réussie")
                return True
            else:
                self.logger.error("❌ Échec du test de connexion")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Erreur test connexion: {e}")
            return False
    
    def get_statistics(self) -> Dict:
        """Retourne les statistiques des requêtes"""
        success_rate = (self.successful_requests / max(1, self.requests_made)) * 100
        
        return {
            'total_requests': self.requests_made,
            'successful_requests': self.successful_requests,
            'failed_requests': self.failed_requests,
            'success_rate': round(success_rate, 2)
        }
    
    def close(self):
        """Ferme la session"""
        if self.session:
            self.session.close()
