<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Casablanca Stock Exchange Scraper</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.1em;
        }
        .stat-card .number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin: 10px 0;
        }
        .stat-card .label {
            color: #666;
            font-size: 0.9em;
        }
        .status-success { color: #28a745; }
        .status-failed { color: #dc3545; }
        .status-pending { color: #ffc107; }
        .status-running { color: #17a2b8; }
        .status-completed { color: #28a745; }
        
        .section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .section h2 {
            margin: 0 0 20px 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .execution-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .execution-item:hover {
            background-color: #f8f9fa;
        }
        .execution-info {
            flex: 1;
        }
        .execution-stats {
            text-align: right;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #333; }
        .btn-danger { background: #dc3545; }
        
        .actions {
            text-align: center;
            margin: 30px 0;
        }
        
        .refresh-info {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            .execution-item {
                flex-direction: column;
                align-items: flex-start;
            }
            .execution-stats {
                text-align: left;
                margin-top: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏛️ Casablanca Stock Exchange Scraper</h1>
            <p>Dashboard de monitoring en temps réel</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <h3>Total Items</h3>
                <div class="number" id="total-items">-</div>
                <div class="label">Données scrapées</div>
            </div>
            <div class="stat-card">
                <h3>Dernières 24h</h3>
                <div class="number" id="recent-items">-</div>
                <div class="label">Nouveaux items</div>
            </div>
            <div class="stat-card">
                <h3>Taux de succès</h3>
                <div class="number" id="success-rate">-</div>
                <div class="label">Dernière exécution</div>
            </div>
            <div class="stat-card">
                <h3>Statut</h3>
                <div class="number" id="last-status">-</div>
                <div class="label">Dernière exécution</div>
            </div>
        </div>

        <div class="actions">
            <button class="btn" onclick="triggerScraper()">🚀 Lancer le Scraper</button>
            <button class="btn btn-warning" onclick="testConnectivity()">🔍 Test Connectivité</button>
            <button class="btn btn-success" onclick="refreshDashboard()">🔄 Actualiser</button>
            <a href="/admin/scraper/" class="btn btn-danger">⚙️ Administration</a>
        </div>

        <div class="section">
            <h2>📊 Dernière Exécution</h2>
            <div id="last-execution">
                <p>Chargement...</p>
            </div>
        </div>

        <div class="section">
            <h2>📈 Historique des Exécutions</h2>
            <div id="recent-executions">
                <p>Chargement...</p>
            </div>
        </div>

        <div class="refresh-info">
            <p>Dernière mise à jour: <span id="last-update">-</span></p>
            <p>Actualisation automatique toutes les 30 secondes</p>
        </div>
    </div>

    <script>
        // Variables globales
        let refreshInterval;

        // Fonction pour formater les dates
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleString('fr-FR');
        }

        // Fonction pour formater les pourcentages
        function formatPercentage(value) {
            if (value === null || value === undefined) return '-';
            return value.toFixed(1) + '%';
        }

        // Fonction pour obtenir la classe CSS du statut
        function getStatusClass(status) {
            const statusClasses = {
                'success': 'status-success',
                'completed': 'status-completed',
                'failed': 'status-failed',
                'pending': 'status-pending',
                'running': 'status-running'
            };
            return statusClasses[status] || '';
        }

        // Fonction pour charger les données du dashboard
        async function loadDashboard() {
            try {
                const response = await fetch('/api/scraper/casablanca-dashboard/');
                const data = await response.json();

                if (data.status === 'success') {
                    updateStats(data.statistics);
                    updateLastExecution(data.last_execution);
                    updateRecentExecutions(data.recent_executions);
                    document.getElementById('last-update').textContent = formatDate(data.timestamp);
                } else {
                    console.error('Erreur lors du chargement du dashboard:', data.message);
                }
            } catch (error) {
                console.error('Erreur réseau:', error);
            }
        }

        // Fonction pour mettre à jour les statistiques
        function updateStats(stats) {
            document.getElementById('total-items').textContent = stats.total_items || 0;
            document.getElementById('recent-items').textContent = stats.recent_items_24h || 0;
        }

        // Fonction pour mettre à jour la dernière exécution
        function updateLastExecution(execution) {
            const container = document.getElementById('last-execution');
            
            if (!execution) {
                container.innerHTML = '<p>Aucune exécution trouvée</p>';
                document.getElementById('success-rate').textContent = '-';
                document.getElementById('last-status').textContent = '-';
                return;
            }

            document.getElementById('success-rate').textContent = formatPercentage(execution.success_rate);
            document.getElementById('last-status').innerHTML = `<span class="${getStatusClass(execution.status)}">${execution.status}</span>`;

            container.innerHTML = `
                <div class="execution-item">
                    <div class="execution-info">
                        <strong>Task ID:</strong> ${execution.task_id || '-'}<br>
                        <strong>Démarré:</strong> ${formatDate(execution.started_at)}<br>
                        <strong>Statut:</strong> <span class="${getStatusClass(execution.status)}">${execution.status || '-'}</span>
                    </div>
                    <div class="execution-stats">
                        <strong>Entreprises traitées:</strong> ${execution.companies_processed || 0}<br>
                        <strong>Taux de succès:</strong> ${formatPercentage(execution.success_rate)}
                    </div>
                </div>
            `;
        }

        // Fonction pour mettre à jour l'historique des exécutions
        function updateRecentExecutions(executions) {
            const container = document.getElementById('recent-executions');
            
            if (!executions || executions.length === 0) {
                container.innerHTML = '<p>Aucune exécution récente</p>';
                return;
            }

            const executionsHtml = executions.map(exec => `
                <div class="execution-item">
                    <div class="execution-info">
                        <strong>${exec.task_id}</strong><br>
                        <small>${formatDate(exec.started_at)}</small>
                    </div>
                    <div class="execution-stats">
                        <span class="${getStatusClass(exec.status)}">${exec.status}</span><br>
                        <small>${exec.companies_processed} entreprises - ${formatPercentage(exec.success_rate)}</small>
                    </div>
                </div>
            `).join('');

            container.innerHTML = executionsHtml;
        }

        // Fonction pour déclencher le scraper
        async function triggerScraper() {
            try {
                const response = await fetch('/api/scraper/trigger-casablanca/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                const data = await response.json();
                
                if (data.status === 'success') {
                    alert('✅ Scraper démarré avec succès!\nTask ID: ' + data.task_id);
                    setTimeout(loadDashboard, 2000); // Actualiser après 2 secondes
                } else {
                    alert('❌ Erreur: ' + data.message);
                }
            } catch (error) {
                alert('❌ Erreur réseau: ' + error.message);
            }
        }

        // Fonction pour tester la connectivité
        async function testConnectivity() {
            try {
                const response = await fetch('/api/scraper/test-casablanca-connectivity/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                const data = await response.json();
                
                if (data.status === 'success') {
                    alert('✅ Test de connectivité démarré!\nTask ID: ' + data.task_id);
                } else {
                    alert('❌ Erreur: ' + data.message);
                }
            } catch (error) {
                alert('❌ Erreur réseau: ' + error.message);
            }
        }

        // Fonction pour actualiser le dashboard
        function refreshDashboard() {
            loadDashboard();
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboard();
            
            // Actualisation automatique toutes les 30 secondes
            refreshInterval = setInterval(loadDashboard, 30000);
        });

        // Nettoyage lors de la fermeture de la page
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
