from rest_framework import serializers
from .models import PredictionModel, Prediction, PredictionRequest
from market_data.serializers import StockSerializer


class PredictionModelSerializer(serializers.ModelSerializer):
    stock = StockSerializer(read_only=True)
    
    class Meta:
        model = PredictionModel
        fields = [
            'id', 'name', 'model_type', 'stock', 'parameters', 'accuracy_metrics',
            'training_start_date', 'training_end_date', 'is_active', 'created_at', 'updated_at'
        ]


class PredictionSerializer(serializers.ModelSerializer):
    prediction_error = serializers.ReadOnlyField()
    prediction_accuracy = serializers.ReadOnlyField()
    
    class Meta:
        model = Prediction
        fields = [
            'id', 'prediction_date', 'target_date', 'predicted_value',
            'confidence_lower', 'confidence_upper', 'confidence_level',
            'actual_value', 'prediction_error', 'prediction_accuracy', 'created_at'
        ]


class PredictionRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = PredictionRequest
        fields = [
            'id', 'stock', 'model_type', 'start_date', 'end_date',
            'forecast_days', 'confidence_interval', 'status', 'result_data',
            'error_message', 'requested_at', 'completed_at'
        ]
        read_only_fields = ['id', 'status', 'result_data', 'error_message', 'requested_at', 'completed_at']


class CreatePredictionRequestSerializer(serializers.Serializer):
    """Serializer for creating prediction requests"""
    ticker = serializers.CharField(max_length=20)
    model_type = serializers.ChoiceField(choices=PredictionModel.MODEL_TYPES)
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    forecast_days = serializers.IntegerField(min_value=1, max_value=365, default=30)
    confidence_interval = serializers.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        min_value=0.01, 
        max_value=99.99, 
        default=95.0
    )
    
    def validate(self, data):
        if data['start_date'] > data['end_date']:
            raise serializers.ValidationError("Start date must be before end date")
        return data


class PredictionResultSerializer(serializers.Serializer):
    """Serializer for prediction results"""
    ticker = serializers.CharField()
    model_type = serializers.CharField()
    forecast_dates = serializers.ListField(child=serializers.DateField())
    forecast_values = serializers.ListField(child=serializers.DecimalField(max_digits=15, decimal_places=4))
    confidence_lower = serializers.ListField(child=serializers.DecimalField(max_digits=15, decimal_places=4))
    confidence_upper = serializers.ListField(child=serializers.DecimalField(max_digits=15, decimal_places=4))
    test_dates = serializers.ListField(child=serializers.DateField(), required=False)
    test_actuals = serializers.ListField(child=serializers.DecimalField(max_digits=15, decimal_places=4), required=False)
    test_predictions = serializers.ListField(child=serializers.DecimalField(max_digits=15, decimal_places=4), required=False)
    model_metrics = serializers.DictField(required=False)
    status = serializers.CharField()
    message = serializers.CharField()


class ModelInfoSerializer(serializers.Serializer):
    """Serializer for model information"""
    ticker = serializers.CharField()
    available_models = serializers.ListField(child=serializers.CharField())
    model_details = serializers.DictField()
    last_updated = serializers.DateTimeField()
    training_period = serializers.DictField()
    performance_metrics = serializers.DictField()
