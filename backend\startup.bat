@echo off
REM XCapital Terminal Backend - Complete Startup Script
REM This script starts <PERSON><PERSON>, Celery Worker, and Celery Beat for automatic scheduling

echo ========================================
echo 🚀 XCAPITAL TERMINAL BACKEND STARTUP
echo ========================================
echo 📅 Date: %date% %time%
echo.

REM Check if Redis is running
echo 1. Checking Redis...
redis-windows\redis-cli.exe ping >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Redis is running
) else (
    echo ❌ Redis not running. Starting Redis...
    echo Please open a new terminal and run:
    echo    cd redis-windows
    echo    redis-server.exe
    echo.
    echo Press any key when Redis is started...
    pause
)

REM Create the daily schedule
echo.
echo 2. Setting up daily schedule...
python manage.py shell -c "
from django_celery_beat.models import PeriodicTask, CrontabSchedule
import json

# Create daily schedule at 19:00
schedule, created = CrontabSchedule.objects.get_or_create(
    minute=0,
    hour=19,
    day_of_week='*',
    day_of_month='*',
    month_of_year='*',
)

# Remove existing task if exists
PeriodicTask.objects.filter(name='Daily Casablanca Stock Exchange Update').delete()

# Create daily task
task = PeriodicTask.objects.create(
    crontab=schedule,
    name='Daily Casablanca Stock Exchange Update',
    task='scraper.tasks.run_daily_update',
    kwargs=json.dumps({}),
    enabled=True,
)

print('✅ Daily task configured: Every day at 19:00')
"

echo.
echo 3. Starting Celery components...
echo.
echo 📋 MANUAL STEPS REQUIRED:
echo ========================
echo.
echo Terminal 1 - Celery Worker:
echo   celery -A xcapital_backend worker --loglevel=info --pool=solo
echo.
echo Terminal 2 - Celery Beat (Scheduler):
echo   celery -A xcapital_backend beat --loglevel=info
echo.
echo Terminal 3 - Django Server (Optional):
echo   python manage.py runserver
echo.
echo 🎉 SYSTEM READY!
echo ===============
echo ✅ Redis running
echo ✅ Daily schedule configured (19:00)
echo ✅ 77 Moroccan companies ready
echo ✅ Phase 3 fully operational
echo.
echo 📊 TEST COMMANDS:
echo ================
echo Test single company:
echo   python manage.py shell -c "from scraper.tasks import test_single_company; result = test_single_company.delay(391); print('Task ID:', result.id)"
echo.
echo Test 5 companies:
echo   python manage.py shell -c "from scraper.tasks import run_test_update; result = run_test_update.delay(); print('Task ID:', result.id)"
echo.
echo Manual daily update:
echo   python manage.py shell -c "from scraper.tasks import run_daily_update; result = run_daily_update.delay(); print('Task ID:', result.id)"
echo.
echo Check system status:
echo   python manage.py shell -c "from scraper.tasks import get_system_status; result = get_system_status.delay(); print(result.get())"
echo.
pause
