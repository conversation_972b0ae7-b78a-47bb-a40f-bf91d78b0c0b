"""
Configuration pour le système de mise à jour quotidienne
"""

import os
from datetime import datetime, timedelta
from pathlib import Path

class Config:
    """Configuration principale"""
    
    # ===============================================================================
    # CHEMINS ET DOSSIERS
    # ===============================================================================
    
    BASE_DIR = Path(__file__).parent
    DATA_DIR = BASE_DIR / "data"
    LOGS_DIR = BASE_DIR / "logs"
    CACHE_DIR = BASE_DIR / "cache"
    
    # Dossiers de sortie
    RAW_DATA_DIR = DATA_DIR / "raw"
    PROCESSED_DATA_DIR = DATA_DIR / "processed"
    DAILY_DATA_DIR = DATA_DIR / "daily"
    
    # Créer les dossiers si ils n'existent pas
    for directory in [DATA_DIR, LOGS_DIR, CACHE_DIR, RAW_DATA_DIR, 
                      PROCESSED_DATA_DIR, DAILY_DATA_DIR]:
        directory.mkdir(exist_ok=True, parents=True)
    
    # ===============================================================================
    # API CONFIGURATION BOURSE DE CASABLANCA
    # ===============================================================================
    
    CASABLANCA_BASE_URL = "https://www.casablanca-bourse.com/api"
    INSTRUMENTS_ENDPOINT = "/instruments/svm"
    
    # Headers pour les requêtes
    DEFAULT_HEADERS = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/vnd.api+json',
        'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Referer': 'https://www.casablanca-bourse.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
    }
    
    # ===============================================================================
    # LISTE COMPLÈTE DES 77 ENTREPRISES MAROCAINES
    # ===============================================================================
    
    MOROCCAN_COMPANIES = [
        391, 385, 498, 491, 305583, 379, 490, 437, 365, 511, 
        452, 494, 455, 373, 509, 476, 521, 458, 4439819, 449, 
        464, 9540094, 530, 465, 428, 512, 370, 371, 466, 451, 
        536, 525, 381, 430, 532, 535, 504, 388, 534, 510, 384, 
        377, 460, 474, 360, 499, 400, 500, 434, 489, 368, 374, 
        423, 479, 541, 533, 482, 389, 515, 386, 369, 492, 382, 
        398, 367, 409, 392, 394, 538, 366, 383, 387, 445, 390, 
        503, 14453760, 488, 485
    ]
    
    # ===============================================================================
    # PARAMÈTRES DE RÉCUPÉRATION
    # ===============================================================================
    
    # Délais entre requêtes (en secondes)
    REQUEST_DELAY = 2.0
    RETRY_DELAY = 5.0
    
    # Nombre de tentatives
    MAX_RETRIES = 3
    
    # Timeout des requêtes
    REQUEST_TIMEOUT = 30
    
    # Pagination
    DEFAULT_PAGE_SIZE = 100
    MAX_RECORDS_PER_COMPANY = 500
    
    # ===============================================================================
    # DATES ET PÉRIODES
    # ===============================================================================
    
    @staticmethod
    def get_target_date(days_ago=0):
        """Retourne la date cible"""
        return (datetime.now() - timedelta(days=days_ago)).strftime("%Y-%m-%d")
    
    @staticmethod
    def get_date_range(days_back=7):
        """Retourne une plage de dates"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        return start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")
    
    # ===============================================================================
    # VALIDATION DES DONNÉES
    # ===============================================================================
    
    # Champs obligatoires dans les données reçues
    REQUIRED_FIELDS = [
        'dateTime',
        'openingPrice',
        'closingPrice',
        'highPrice',
        'lowPrice',
        'quantity',
        'cumulVolumeEchange'
    ]
    
    # Seuils de validation
    MIN_RECORDS_PER_DAY = 1
    MAX_PRICE_VARIATION = 20.0  # % maximum de variation de prix
    
    # ===============================================================================
    # LOGGING
    # ===============================================================================
    
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
    
    @staticmethod
    def get_log_filename(prefix="daily_fetcher"):
        """Génère un nom de fichier de log"""
        return f"{prefix}_{datetime.now().strftime('%Y%m%d')}.log"
