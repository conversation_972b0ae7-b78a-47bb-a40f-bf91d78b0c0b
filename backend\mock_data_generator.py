#!/usr/bin/env python3
"""
Mock Data Generator for Testing Database Integration
Generates realistic stock market data for Moroccan companies when API is not available
"""

import os
import sys
import django
import random
import logging
from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import Dict, List, Optional

# Setup Django
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

try:
    django.setup()
    from market_data.postgres_models import XCapitalCompany, XCapitalCompanyBond
    from django.db import transaction
    DJANGO_AVAILABLE = True
    print("✅ Django models imported successfully")
except Exception as e:
    print(f"❌ Django setup error: {e}")
    DJANGO_AVAILABLE = False

class MockCasablancaDataGenerator:
    """
    Générateur de données fictives pour tester l'intégration avec la base de données
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Données des entreprises marocaines réelles
        self.company_data = {
            391: {
                'symbol': 'BOA',
                'nom_francais': 'Bank of Africa',
                'nom_arabe': 'بنك المغرب',
                'nom_anglais': 'Bank of Africa',
                'base_price': 180.0,  # Prix de base pour simulation
                'sector': 'Banking'
            },
            511: {
                'symbol': 'AWB', 
                'nom_francais': 'Attijariwafa Bank',
                'nom_arabe': 'التجاري وفا بنك',
                'nom_anglais': 'Attijariwafa Bank',
                'base_price': 520.0,
                'sector': 'Banking'
            },
            385: {
                'symbol': 'LHM',
                'nom_francais': 'Lafargeholcim Maroc',
                'nom_arabe': 'لافارج هولسيم المغرب',
                'nom_anglais': 'Lafargeholcim Morocco',
                'base_price': 1450.0,
                'sector': 'Construction Materials'
            },
            498: {
                'symbol': 'BMCE',
                'nom_francais': 'BMCE Bank',
                'nom_arabe': 'بنك المغرب للتجارة الخارجية',
                'nom_anglais': 'BMCE Bank',
                'base_price': 920.0,
                'sector': 'Banking'
            },
            379: {
                'symbol': 'IAM',
                'nom_francais': 'Maroc Telecom',
                'nom_arabe': 'اتصالات المغرب',
                'nom_anglais': 'Maroc Telecom',
                'base_price': 125.0,
                'sector': 'Telecommunications'
            }
        }
        
        self.stats = {
            'companies_created': 0,
            'companies_updated': 0,
            'bonds_created': 0,
            'bonds_updated': 0,
            'errors': []
        }
    
    def generate_realistic_price_data(self, company_id: int, target_date: date = None) -> Dict:
        """
        Génère des données de prix réalistes pour une entreprise
        """
        
        if target_date is None:
            target_date = date.today()
        
        if company_id not in self.company_data:
            # Générer des données pour une entreprise inconnue
            base_price = random.uniform(50.0, 1000.0)
            company_info = {
                'symbol': f'COMP{company_id}',
                'nom_francais': f'Entreprise {company_id}',
                'nom_anglais': f'Company {company_id}',
                'base_price': base_price,
                'sector': 'Unknown'
            }
        else:
            company_info = self.company_data[company_id]
            base_price = company_info['base_price']
        
        # Générer des variations réalistes
        daily_variation = random.uniform(-0.05, 0.05)  # ±5% variation
        
        # Prix d'ouverture
        open_price = base_price * (1 + random.uniform(-0.02, 0.02))
        
        # Prix de clôture basé sur la variation quotidienne
        close_price = open_price * (1 + daily_variation)
        
        # Prix haut et bas
        high_price = max(open_price, close_price) * (1 + random.uniform(0.001, 0.03))
        low_price = min(open_price, close_price) * (1 - random.uniform(0.001, 0.03))
        
        # Volume réaliste
        base_volume = random.randint(1000, 50000)
        volume = base_volume * random.uniform(0.5, 2.0)
        
        # Valeur en MAD
        value_mad = volume * close_price
        
        return {
            'company_id': company_id,
            'company_info': company_info,
            'date_trade': target_date,
            'open_price': round(Decimal(str(open_price)), 4),
            'high_price': round(Decimal(str(high_price)), 4),
            'low_price': round(Decimal(str(low_price)), 4),
            'close_price': round(Decimal(str(close_price)), 4),
            'volume': int(volume),
            'value_mad': round(Decimal(str(value_mad)), 2),
            'variation': round(daily_variation * 100, 2),  # En pourcentage
            'timestamp': datetime.now()
        }
    
    def save_company_to_database(self, company_id: int, company_info: Dict) -> Optional['XCapitalCompany']:
        """
        Sauvegarde une entreprise dans XCapitalTerminal_Companies
        """
        
        if not DJANGO_AVAILABLE:
            self.logger.error("Django non disponible")
            return None
        
        try:
            # Chercher si l'entreprise existe déjà
            company, created = XCapitalCompany.objects.get_or_create(
                company_id=str(company_id),
                defaults={
                    'symbol': company_info['symbol'],
                    'nom_francais': company_info['nom_francais'],
                    'nom_arabe': company_info.get('nom_arabe', ''),
                    'nom_anglais': company_info.get('nom_anglais', ''),
                }
            )
            
            if created:
                self.stats['companies_created'] += 1
                self.logger.info(f"✅ Entreprise créée: {company.symbol} ({company_id})")
            else:
                # Mettre à jour les informations si nécessaire
                updated = False
                if company.nom_francais != company_info['nom_francais']:
                    company.nom_francais = company_info['nom_francais']
                    updated = True
                
                if updated:
                    company.save()
                    self.stats['companies_updated'] += 1
                    self.logger.info(f"🔄 Entreprise mise à jour: {company.symbol} ({company_id})")
            
            return company
            
        except Exception as e:
            error_msg = f"Erreur sauvegarde entreprise {company_id}: {e}"
            self.logger.error(error_msg)
            self.stats['errors'].append(error_msg)
            return None
    
    def save_bond_data_to_database(self, company: 'XCapitalCompany', price_data: Dict) -> Optional['XCapitalCompanyBond']:
        """
        Sauvegarde les données de prix dans XCapitalTerminal_CompanyBonds
        """
        
        if not DJANGO_AVAILABLE or not company:
            return None
        
        try:
            # Chercher si les données existent déjà pour cette date
            bond, created = XCapitalCompanyBond.objects.get_or_create(
                company=company,
                date_trade=price_data['date_trade'],
                defaults={
                    'open_price': price_data['open_price'],
                    'high_price': price_data['high_price'],
                    'low_price': price_data['low_price'],
                    'close_price': price_data['close_price'],
                    'volume': price_data['volume'],
                    'value_mad': price_data['value_mad'],
                }
            )
            
            if created:
                self.stats['bonds_created'] += 1
                self.logger.info(f"📊 Données prix créées: {company.symbol} - {price_data['date_trade']} - {price_data['close_price']} MAD")
            else:
                # Mettre à jour si les données ont changé
                updated = False
                for field in ['open_price', 'high_price', 'low_price', 'close_price', 'volume', 'value_mad']:
                    if getattr(bond, field) != price_data[field]:
                        setattr(bond, field, price_data[field])
                        updated = True
                
                if updated:
                    bond.save()
                    self.stats['bonds_updated'] += 1
                    self.logger.info(f"🔄 Données prix mises à jour: {company.symbol} - {price_data['date_trade']}")
            
            return bond
            
        except Exception as e:
            error_msg = f"Erreur sauvegarde prix {company.symbol}: {e}"
            self.logger.error(error_msg)
            self.stats['errors'].append(error_msg)
            return None
    
    def generate_and_save_company_data(self, company_id: int, target_date: date = None) -> Dict:
        """
        Génère et sauvegarde toutes les données pour une entreprise
        """
        
        result = {
            'success': False,
            'company_id': company_id,
            'error': None,
            'company_saved': False,
            'price_data_saved': False,
            'data': None
        }
        
        try:
            # Générer les données de prix
            price_data = self.generate_realistic_price_data(company_id, target_date)
            
            # Sauvegarder l'entreprise
            company = self.save_company_to_database(company_id, price_data['company_info'])
            
            if company:
                result['company_saved'] = True
                
                # Sauvegarder les données de prix
                bond = self.save_bond_data_to_database(company, price_data)
                
                if bond:
                    result['price_data_saved'] = True
                    result['success'] = True
                    result['data'] = {
                        'company_name': company.nom_francais,
                        'symbol': company.symbol,
                        'close_price': float(price_data['close_price']),
                        'volume': price_data['volume'],
                        'variation': price_data['variation'],
                        'date': str(price_data['date_trade'])
                    }
                else:
                    result['error'] = 'Échec sauvegarde données prix'
            else:
                result['error'] = 'Échec sauvegarde entreprise'
                
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"Erreur génération données {company_id}: {e}")
        
        return result
    
    def get_statistics(self) -> Dict:
        """Retourne les statistiques des opérations"""
        return {
            'companies_created': self.stats['companies_created'],
            'companies_updated': self.stats['companies_updated'],
            'bonds_created': self.stats['bonds_created'],
            'bonds_updated': self.stats['bonds_updated'],
            'total_errors': len(self.stats['errors']),
            'errors': self.stats['errors']
        }

# Test function
def test_mock_data_generation():
    """Test de génération de données fictives"""
    
    print("🧪 TEST GÉNÉRATEUR DE DONNÉES FICTIVES")
    print("=" * 50)
    
    generator = MockCasablancaDataGenerator()
    
    # Test avec Bank of Africa (391)
    print("1. Test Bank of Africa (391)...")
    result = generator.generate_and_save_company_data(391)
    
    if result['success']:
        print("✅ Succès!")
        print(f"   Entreprise: {result['data']['company_name']}")
        print(f"   Prix: {result['data']['close_price']} MAD")
        print(f"   Volume: {result['data']['volume']}")
        print(f"   Variation: {result['data']['variation']}%")
    else:
        print(f"❌ Échec: {result['error']}")
    
    # Afficher les statistiques
    stats = generator.get_statistics()
    print(f"\n📊 STATISTIQUES:")
    print(f"   Entreprises créées: {stats['companies_created']}")
    print(f"   Entreprises mises à jour: {stats['companies_updated']}")
    print(f"   Données prix créées: {stats['bonds_created']}")
    print(f"   Données prix mises à jour: {stats['bonds_updated']}")
    print(f"   Erreurs: {stats['total_errors']}")
    
    return result['success']

if __name__ == "__main__":
    test_mock_data_generation()
