from django.core.management.base import BaseCommand
from django.test import Client
import json

class Command(BaseCommand):
    help = 'Test les corrections de l\'API company-data avec périodes'
    
    def handle(self, *args, **options):
        self.stdout.write("=== Test API Company Data avec périodes ===\n")
        
        client = Client()
        url = '/api/v1/xcapital/secure/company-data/'
        
        # Test 1: Avec période 1M
        self.stdout.write("1. Test avec CIH et période 1M:")
        data1 = {
            "company_symbol": "CIH",
            "period": "1M"
        }
        
        response1 = client.post(
            url,
            data=json.dumps(data1),
            content_type='application/json'
        )
        
        self.stdout.write(f"Status Code: {response1.status_code}")
        
        if response1.status_code == 200:
            result = response1.json()
            self.stdout.write("✅ Succès!")
            
            if 'period' in result:
                period_info = result['period']
                self.stdout.write(f"   Période demandée: {period_info.get('requested_period')}")
                self.stdout.write(f"   Records trouvés: {period_info.get('total_records')}")
                if period_info.get('total_records', 0) > 0:
                    self.stdout.write(f"   Dates: {period_info.get('actual_start_date')} à {period_info.get('actual_end_date')}")
            
            if 'period_statistics' in result:
                stats = result['period_statistics']
                self.stdout.write(f"   Prix initial: {stats.get('first_price')}")
                self.stdout.write(f"   Prix final: {stats.get('last_price')}")
                self.stdout.write(f"   Variation: {stats.get('period_change_pct')}%")
        else:
            self.stdout.write("❌ Erreur:")
            try:
                error_data = response1.json()
                for key, value in error_data.items():
                    self.stdout.write(f"   {key}: {value}")
            except:
                self.stdout.write(f"   {response1.content}")
        
        # Test 2: Avec période 3M
        self.stdout.write("\n2. Test avec CIH et période 3M:")
        data2 = {
            "company_symbol": "CIH",
            "period": "3M"
        }
        
        response2 = client.post(
            url,
            data=json.dumps(data2),
            content_type='application/json'
        )
        
        self.stdout.write(f"Status Code: {response2.status_code}")
        
        if response2.status_code == 200:
            result = response2.json()
            self.stdout.write("✅ Succès!")
            
            if 'period' in result:
                period_info = result['period']
                self.stdout.write(f"   Période demandée: {period_info.get('requested_period')}")
                self.stdout.write(f"   Records trouvés: {period_info.get('total_records')}")
        else:
            self.stdout.write("❌ Erreur")
        
        # Test 3: Avec période invalide
        self.stdout.write("\n3. Test avec période invalide:")
        data3 = {
            "company_symbol": "CIH",
            "period": "INVALID"
        }
        
        response3 = client.post(
            url,
            data=json.dumps(data3),
            content_type='application/json'
        )
        
        self.stdout.write(f"Status Code: {response3.status_code}")
        
        if response3.status_code == 400:
            result = response3.json()
            self.stdout.write("✅ Erreur attendue pour période invalide")
            self.stdout.write(f"   Message: {result.get('message')}")
            self.stdout.write(f"   Périodes disponibles: {result.get('available_periods')}")
        else:
            self.stdout.write(f"❌ Réponse inattendue: {response3.status_code}")
        
        self.stdout.write("\n=== Tests terminés ===")
