#!/usr/bin/env python3
"""
Improved Daily Update Orchestrator with Database Integration and Mock Data Fallback
"""

import os
import sys
import logging
from datetime import datetime, date
from typing import Dict, Optional, List
import traceback

# Setup Django first
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

try:
    import django
    django.setup()
    from market_data.postgres_models import XCapitalCompany, XCapitalCompanyBond
    from django.db import transaction
    DJANGO_AVAILABLE = True
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    DJANGO_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class ImprovedDailyUpdateOrchestrator:
    """
    Orchestrateur amélioré avec intégration base de données et données de test
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Test companies for demo
        self.test_companies = [391, 511, 385, 498, 379]  # Bank of Africa, Attijariwafa, etc.
        
        # Statistics
        self.stats = {
            'companies_processed': 0,
            'companies_successful': 0,
            'companies_failed': 0,
            'database_saves': 0,
            'errors': []
        }
        
        self.logger.info("🚀 ImprovedDailyUpdateOrchestrator initialisé")
    
    def generate_mock_company_data(self, company_id: int, target_date: date = None) -> Dict:
        """
        Génère des données fictives réalistes pour une entreprise
        """
        
        if target_date is None:
            target_date = date.today()
        
        # Données des entreprises marocaines
        company_data = {
            391: {'symbol': 'BOA', 'name': 'Bank of Africa', 'base_price': 180.0},
            511: {'symbol': 'AWB', 'name': 'Attijariwafa Bank', 'base_price': 520.0},
            385: {'symbol': 'LHM', 'name': 'Lafargeholcim Maroc', 'base_price': 1450.0},
            498: {'symbol': 'BMCE', 'name': 'BMCE Bank', 'base_price': 920.0},
            379: {'symbol': 'IAM', 'name': 'Maroc Telecom', 'base_price': 125.0}
        }
        
        if company_id in company_data:
            info = company_data[company_id]
        else:
            info = {
                'symbol': f'COMP{company_id}',
                'name': f'Company {company_id}',
                'base_price': 100.0
            }
        
        # Génération de prix réalistes
        import random
        from decimal import Decimal
        
        base_price = info['base_price']
        variation = random.uniform(-0.03, 0.03)  # ±3% variation
        
        open_price = base_price * (1 + random.uniform(-0.01, 0.01))
        close_price = open_price * (1 + variation)
        high_price = max(open_price, close_price) * (1 + random.uniform(0.001, 0.02))
        low_price = min(open_price, close_price) * (1 - random.uniform(0.001, 0.02))
        
        volume = random.randint(1000, 50000)
        value_mad = volume * close_price
        
        return {
            'success': True,
            'company_id': company_id,
            'company_name': info['name'],
            'symbol': info['symbol'],
            'date_trade': target_date,
            'open_price': round(Decimal(str(open_price)), 4),
            'high_price': round(Decimal(str(high_price)), 4),
            'low_price': round(Decimal(str(low_price)), 4),
            'close_price': round(Decimal(str(close_price)), 4),
            'volume': volume,
            'value_mad': round(Decimal(str(value_mad)), 2),
            'variation_percent': round(variation * 100, 2),
            'processing_time': 0.5,
            'data_source': 'mock_generator'
        }
    
    def save_company_to_database(self, company_data: Dict) -> bool:
        """
        Sauvegarde une entreprise et ses données dans la base PostgreSQL
        """
        
        if not DJANGO_AVAILABLE:
            self.logger.warning("Django non disponible - saut sauvegarde")
            return False
        
        try:
            # Utiliser le module amélioré de sauvegarde
            from improved_database_saver import save_company_data
            
            success = save_company_data(company_data)
            
            if success:
                self.stats['database_saves'] += 1
                self.logger.info(f"� Données sauvées: {company_data['symbol']} - {company_data['close_price']} MAD")
            else:
                self.logger.error(f"❌ Échec sauvegarde: {company_data['symbol']}")
            
            return success
                
        except Exception as e:
            error_msg = f"Erreur sauvegarde base de données pour {company_data['company_id']}: {e}"
            self.logger.error(error_msg)
            self.stats['errors'].append(error_msg)
            return False
    
    def test_single_company(self, company_id: int) -> Dict:
        """
        Test une seule entreprise avec génération de données et sauvegarde
        """
        
        start_time = datetime.now()
        self.logger.info(f"🧪 Test entreprise {company_id}")
        
        try:
            self.stats['companies_processed'] += 1
            
            # Générer des données fictives
            company_data = self.generate_mock_company_data(company_id)
            
            if company_data['success']:
                # Sauvegarder en base de données
                database_success = self.save_company_to_database(company_data)
                
                # Préparer le résultat
                result = {
                    'success': True,
                    'company_id': company_id,
                    'company_name': company_data['company_name'],
                    'closing_price': float(company_data['close_price']),
                    'volume': company_data['volume'],
                    'variation_percent': company_data['variation_percent'],
                    'database_saved': database_success,
                    'processing_time': (datetime.now() - start_time).total_seconds(),
                    'data_source': 'mock_generator',
                    'timestamp': datetime.now().isoformat()
                }
                
                self.stats['companies_successful'] += 1
                self.logger.info(f"✅ Test entreprise {company_id} réussi - Prix: {company_data['close_price']} MAD")
                
                return result
            else:
                self.stats['companies_failed'] += 1
                return {
                    'success': False,
                    'company_id': company_id,
                    'error': 'Échec génération données fictives',
                    'timestamp': datetime.now().isoformat()
                }
        
        except Exception as e:
            self.stats['companies_failed'] += 1
            error_msg = f"Erreur test entreprise {company_id}: {e}"
            self.logger.error(error_msg)
            self.stats['errors'].append(error_msg)
            
            return {
                'success': False,
                'company_id': company_id,
                'error': error_msg,
                'timestamp': datetime.now().isoformat()
            }
    
    def run_daily_update(self, target_date: str = None, test_mode: bool = False) -> Dict:
        """
        Exécute la mise à jour quotidienne avec API Casablanca et données générées en fallback
        """
        
        start_time = datetime.now()
        self.logger.info(f"🚀 Début mise à jour quotidienne - Mode: {'TEST' if test_mode else 'PRODUCTION'}")
        
        # Réinitialiser les statistiques
        self.stats = {
            'companies_processed': 0,
            'companies_successful': 0,
            'companies_failed': 0,
            'database_saves': 0,
            'errors': []
        }
        
        target_date_obj = datetime.strptime(target_date, '%Y-%m-%d').date() if target_date else date.today()
        
        try:
            # 1. Essayer d'abord l'API Casablanca
            self.logger.info("📡 Tentative récupération depuis API Casablanca...")
            
            try:
                from casablanca_api_client import update_from_casablanca_api
                
                api_result = update_from_casablanca_api()
                
                if api_result['success'] and api_result['companies_saved'] > 0:
                    self.logger.info(f"✅ API Casablanca réussie: {api_result['companies_saved']} entreprises")
                    
                    # Utiliser les résultats de l'API
                    self.stats['companies_processed'] = api_result['companies_processed']
                    self.stats['companies_successful'] = api_result['companies_saved']
                    self.stats['companies_failed'] = api_result['companies_failed']
                    self.stats['database_saves'] = api_result['companies_saved']
                    
                    if api_result.get('errors'):
                        self.stats['errors'].extend(api_result['errors'])
                    
                    # Résultat de l'API
                    duration = (datetime.now() - start_time).total_seconds()
                    
                    return {
                        'success': True,
                        'companies_processed': self.stats['companies_processed'],
                        'companies_successful': self.stats['companies_successful'],
                        'companies_failed': self.stats['companies_failed'],
                        'database_saves': self.stats['database_saves'],
                        'total_records': self.stats['companies_successful'],
                        'duration_seconds': duration,
                        'target_date': str(target_date_obj),
                        'test_mode': test_mode,
                        'data_source': 'casablanca_api',
                        'errors': self.stats['errors'],
                        'timestamp': datetime.now().isoformat()
                    }
                else:
                    self.logger.warning(f"⚠️ API Casablanca échouée ou aucune donnée: {api_result.get('error', 'Inconnu')}")
                    
            except Exception as e:
                self.logger.warning(f"⚠️ Erreur API Casablanca: {e}")
            
            # 2. Fallback vers les données générées
            self.logger.info("🔄 Fallback vers génération de données...")
            
            # Liste des entreprises à traiter
            if test_mode:
                companies_to_process = self.test_companies[:5]  # 5 entreprises pour le test
            else:
                # Liste complète des 77 entreprises marocaines
                companies_to_process = [
                    391, 385, 498, 491, 305583, 379, 490, 437, 365, 511, 
                    452, 494, 455, 373, 509, 476, 521, 458, 4439819, 449, 
                    464, 9540094, 530, 465, 428, 512, 370, 371, 466, 451, 
                    536, 525, 381, 430, 532, 535, 504, 388, 534, 510, 384, 
                    377, 460, 474, 360, 499, 400, 500, 434, 489, 368, 374, 
                    423, 479, 541, 533, 482, 389, 515, 386, 369, 492, 382, 
                    398, 367, 409, 392, 394, 538, 366, 383, 387, 445, 390, 
                    503, 14453760, 488, 485
                ]
            
            # Traiter chaque entreprise avec des données générées
            for company_id in companies_to_process:
                try:
                    self.stats['companies_processed'] += 1
                    
                    # Générer et sauvegarder les données
                    company_data = self.generate_mock_company_data(company_id, target_date_obj)
                    
                    if company_data['success']:
                        database_success = self.save_company_to_database(company_data)
                        
                        if database_success:
                            self.stats['companies_successful'] += 1
                        else:
                            self.stats['companies_failed'] += 1
                            
                        self.logger.info(f"📊 [{self.stats['companies_processed']}/{len(companies_to_process)}] Entreprise {company_id} traitée - Prix: {company_data['close_price']} MAD")
                    else:
                        self.stats['companies_failed'] += 1
                        self.logger.warning(f"⚠️ Échec traitement entreprise {company_id}")
                        
                except Exception as e:
                    self.stats['companies_failed'] += 1
                    error_msg = f"Erreur traitement entreprise {company_id}: {e}"
                    self.logger.error(error_msg)
                    self.stats['errors'].append(error_msg)
            
            # Calcul du temps d'exécution
            duration = (datetime.now() - start_time).total_seconds()
            
            # Résultat final
            result = {
                'success': self.stats['companies_successful'] > 0,
                'companies_processed': self.stats['companies_processed'],
                'companies_successful': self.stats['companies_successful'],
                'companies_failed': self.stats['companies_failed'],
                'database_saves': self.stats['database_saves'],
                'total_records': self.stats['companies_successful'],
                'duration_seconds': duration,
                'target_date': str(target_date_obj),
                'test_mode': test_mode,
                'data_source': 'mock_generator_fallback',
                'errors': self.stats['errors'],
                'timestamp': datetime.now().isoformat()
            }
            
            if result['success']:
                self.logger.info(f"✅ Mise à jour quotidienne terminée - {self.stats['companies_successful']}/{self.stats['companies_processed']} entreprises réussies")
            else:
                self.logger.error(f"❌ Mise à jour quotidienne échouée - {self.stats['companies_failed']} échecs")
            
            return result
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            error_msg = f"Erreur globale mise à jour quotidienne: {e}"
            self.logger.error(error_msg)
            
            return {
                'success': False,
                'companies_processed': self.stats['companies_processed'],
                'companies_successful': self.stats['companies_successful'],
                'companies_failed': self.stats['companies_failed'],
                'database_saves': self.stats['database_saves'],
                'duration_seconds': duration,
                'target_date': str(target_date_obj),
                'test_mode': test_mode,
                'data_source': 'error',
                'error': error_msg,
                'errors': self.stats['errors'],
                'timestamp': datetime.now().isoformat()
            }
    
    def get_system_status(self) -> Dict:
        """
        Retourne le statut du système
        """
        
        try:
            # Test de Redis
            redis_available = False
            try:
                from django.core.cache import cache
                cache.set('test_key', 'test_value', timeout=5)
                redis_available = cache.get('test_key') == 'test_value'
            except:
                pass
            
            # Test de la base de données
            database_available = False
            companies_count = 0
            bonds_count = 0
            
            if DJANGO_AVAILABLE:
                try:
                    from improved_database_saver import get_companies_from_database, get_latest_prices
                    
                    companies = get_companies_from_database()
                    prices = get_latest_prices(1)
                    
                    companies_count = len(companies)
                    bonds_count = len(prices)
                    database_available = True
                    
                except Exception as e:
                    self.logger.warning(f"Erreur test base de données: {e}")
                    # Fallback vers l'ancienne méthode
                    try:
                        companies_count = XCapitalCompany.objects.count()
                        bonds_count = XCapitalCompanyBond.objects.count()
                        database_available = True
                    except:
                        pass
            
            return {
                'system_operational': True,
                'django_available': DJANGO_AVAILABLE,
                'database_available': database_available,
                'redis_available': redis_available,
                'daily_updater_available': True,  # Using mock data
                'mock_generator_available': True,
                'companies_in_database': companies_count,
                'bonds_in_database': bonds_count,
                'test_companies_configured': len(self.test_companies),
                'total_companies_configured': 77,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'system_operational': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

# Create global instance
improved_orchestrator = ImprovedDailyUpdateOrchestrator()

# Backward compatibility functions
def test_single_company(company_id: int) -> Dict:
    """Test d'une seule entreprise"""
    return improved_orchestrator.test_single_company(company_id)

def run_daily_update(target_date: str = None, test_mode: bool = False) -> Dict:
    """Mise à jour quotidienne"""
    return improved_orchestrator.run_daily_update(target_date, test_mode)

def get_system_status() -> Dict:
    """Statut du système"""
    return improved_orchestrator.get_system_status()

if __name__ == "__main__":
    # Test du système
    print("🧪 TEST DU SYSTÈME AMÉLIORÉ")
    print("=" * 50)
    
    # Test statut
    status = get_system_status()
    print("📊 Statut du système:")
    for key, value in status.items():
        print(f"   {key}: {value}")
    
    print()
    
    # Test entreprise unique
    print("🏦 Test Bank of Africa (391):")
    result = test_single_company(391)
    
    if result['success']:
        print(f"✅ Succès!")
        print(f"   Entreprise: {result['company_name']}")
        print(f"   Prix: {result['closing_price']} MAD")
        print(f"   Volume: {result['volume']}")
        print(f"   Sauvé en BDD: {result['database_saved']}")
    else:
        print(f"❌ Échec: {result['error']}")
    
    print("\n🎉 Test terminé!")
