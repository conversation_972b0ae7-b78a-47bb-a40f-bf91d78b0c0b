from rest_framework import generics
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters

from .csv_models import FinancialInstrument, InstrumentPrice, MASIIndex, MASIIndexValue
from .simple_serializers import (
    SimpleFinancialInstrumentSerializer, SimpleInstrumentPriceSerializer,
    SimpleMASIIndexSerializer, SimpleMASIIndexValueSerializer
)


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 200


# Vues simples pour tester
class SimpleFinancialInstrumentListView(generics.ListAPIView):
    """Liste simple des instruments financiers"""
    queryset = FinancialInstrument.objects.filter(is_active=True)
    serializer_class = SimpleFinancialInstrumentSerializer
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['sector']
    search_fields = ['symbol', 'label', 'sector']


class SimpleInstrumentDetailView(generics.RetrieveAPIView):
    """Détails simples d'un instrument"""
    queryset = FinancialInstrument.objects.filter(is_active=True)
    serializer_class = SimpleFinancialInstrumentSerializer
    lookup_field = 'symbol'


@api_view(['GET'])
def simple_market_overview(request):
    """Vue d'ensemble simple du marché"""
    total_instruments = FinancialInstrument.objects.filter(is_active=True).count()
    total_indices = MASIIndex.objects.filter(is_active=True).count()
    
    sectors = FinancialInstrument.objects.filter(is_active=True).values_list('sector', flat=True).distinct()
    
    return Response({
        'market_statistics': {
            'total_instruments': total_instruments,
            'total_indices': total_indices,
            'total_sectors': len(sectors),
            'data_coverage': '2022-present'
        },
        'status': 'operational'
    })


@api_view(['GET'])
def simple_available_symbols(request):
    """Liste simple des symboles"""
    instruments = list(FinancialInstrument.objects.filter(is_active=True).values('symbol', 'label', 'sector'))
    indices = list(MASIIndex.objects.filter(is_active=True).values('symbol', 'label', 'type'))
    
    return Response({
        'instruments': {
            'total_count': len(instruments),
            'data': instruments
        },
        'indices': {
            'total_count': len(indices), 
            'data': indices
        },
        'combined_total': len(instruments) + len(indices)
    })
