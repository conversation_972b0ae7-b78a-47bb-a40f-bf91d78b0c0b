#!/usr/bin/env python3
"""
Test simple de l'API Casablanca - sans Django
"""

import sys
import os
import requests
from datetime import datetime, date
import json

def test_direct_api():
    """Test direct de l'API Casablanca"""
    print("🧪 TEST DIRECT API CASABLANCA")
    print("=" * 40)
    
    # URL de l'API avec pagination
    url = "https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action"
    
    # Headers pour simuler un navigateur
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Referer': 'https://www.casablanca-bourse.com/bourseweb/Negociation-Marche.aspx?Cat=24',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    }
    
    try:
        # Test première page
        params = {
            'page[limit]': 5,
            'page[offset]': 0
        }
        
        print(f"🔗 URL: {url}")
        print(f"📋 Params: {params}")
        print(f"🛡️ Headers: User-Agent={headers['User-Agent'][:50]}...")
        
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📏 Content Length: {len(response.content)} bytes")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ JSON valide - Type: {type(data)}")
                
                if isinstance(data, dict):
                    print(f"📊 Clés principales: {list(data.keys())}")
                    
                    if 'data' in data:
                        companies = data['data']
                        print(f"🏢 Nombre d'entreprises: {len(companies)}")
                        
                        if len(companies) > 0:
                            print(f"📊 Exemple entreprise:")
                            example = companies[0]
                            print(f"   ID: {example.get('id', 'N/A')}")
                            
                            if 'attributes' in example:
                                attrs = example['attributes']
                                print(f"   Symbole: {attrs.get('symbol', 'N/A')}")
                                print(f"   Nom: {attrs.get('instrument_name', 'N/A')}")
                                print(f"   Prix fermeture: {attrs.get('close_price', 'N/A')}")
                                print(f"   Volume: {attrs.get('volume', 'N/A')}")
                            
                            return True
                        else:
                            print("⚠️ Aucune entreprise dans les données")
                    else:
                        print("⚠️ Pas de clé 'data' dans la réponse")
                        
                elif isinstance(data, list):
                    print(f"📊 Liste de {len(data)} éléments")
                    if len(data) > 0:
                        print(f"   Premier élément: {list(data[0].keys()) if isinstance(data[0], dict) else type(data[0])}")
                    return True
                    
                print(f"📄 Réponse (100 premiers caractères): {str(data)[:100]}...")
                
            except json.JSONDecodeError as e:
                print(f"❌ Erreur JSON: {e}")
                print(f"📄 Réponse brute (200 premiers caractères):")
                print(response.text[:200])
                
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            print(f"📄 Réponse: {response.text[:200]}")
            
        return False
        
    except requests.exceptions.Timeout:
        print("❌ Timeout de la requête")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Erreur de connexion")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def test_multiple_pages():
    """Test de la pagination"""
    print("\n🔄 TEST PAGINATION")
    print("=" * 30)
    
    url = "https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'fr-FR,fr;q=0.9',
        'Referer': 'https://www.casablanca-bourse.com/bourseweb/Negociation-Marche.aspx?Cat=24'
    }
    
    total_companies = 0
    
    for offset in [0, 50, 100]:
        try:
            params = {
                'page[limit]': 50,
                'page[offset]': offset
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, dict) and 'data' in data:
                    companies_count = len(data['data'])
                    total_companies += companies_count
                    print(f"   Page offset={offset}: {companies_count} entreprises")
                    
                    if companies_count == 0:
                        break
                else:
                    print(f"   Page offset={offset}: Format inattendu")
                    break
            else:
                print(f"   Page offset={offset}: Erreur {response.status_code}")
                break
                
        except Exception as e:
            print(f"   Page offset={offset}: Erreur {e}")
            break
    
    print(f"📊 Total entreprises récupérées: {total_companies}")
    return total_companies > 0

def main():
    """Test principal"""
    print("🧪 TESTS API CASABLANCA SIMPLE")
    print("=" * 50)
    
    # Test 1: Connexion API
    test1_success = test_direct_api()
    
    # Test 2: Pagination (seulement si le premier test passe)
    test2_success = False
    if test1_success:
        test2_success = test_multiple_pages()
    
    # Résumé
    print("\n📊 RÉSUMÉ")
    print("=" * 30)
    print(f"Connexion API: {'✅ OK' if test1_success else '❌ ECHEC'}")
    print(f"Pagination: {'✅ OK' if test2_success else '❌ ECHEC'}")
    
    if test1_success and test2_success:
        print("\n🎉 API CASABLANCA ACCESSIBLE!")
        print("   Prêt pour intégration avec Django")
    elif test1_success:
        print("\n⚠️ API accessible mais pagination limitée")
    else:
        print("\n❌ API non accessible")
        print("   Vérifier la connexion Internet ou l'URL")

if __name__ == "__main__":
    main()
