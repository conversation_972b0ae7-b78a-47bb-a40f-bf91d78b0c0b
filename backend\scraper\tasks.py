"""
Tâches Celery pour le scraper Casablanca Stock Exchange
Intégration complète avec XCapitalTerminal_Companies et XCapitalTerminal_CompanyBonds
"""

import time
import logging
import requests
import json
from datetime import datetime, date
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from celery import shared_task
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from django.db import connection, transaction
from django.db.models import Q
import urllib3

# Configuration du logger
logger = logging.getLogger('scraper')

# Désactiver les avertissements SSL si nécessaire
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Import des modèles Django
try:
    from market_data.postgres_models import XCapitalCompany, XCapitalCompanyBond
    from scraper.models import ScrapedItem, CasablancaApiExecution
    MODELS_AVAILABLE = True
except ImportError as e:
    logger.error(f"❌ Erreur import modèles Django: {e}")
    MODELS_AVAILABLE = False

# Import du système de mise à jour quotidienne (legacy)
try:
    from daily_update_orchestrator import daily_orchestrator
    PHASE3_AVAILABLE = True
except ImportError:
    logger.warning("⚠️ Phase 3 legacy non disponible - daily_update_orchestrator non trouvé")
    PHASE3_AVAILABLE = False

# Clés Redis pour les locks d'exécution concurrente
CASABLANCA_SCRAPER_LOCK_KEY = 'casablanca_scraper:running_lock'
SCRAPER_LOCK_KEY = 'scraper:running_lock'  # Legacy
SCRAPER_LOCK_TIMEOUT = 3600  # 1 heure maximum

# Configuration du scraper Casablanca
CASABLANCA_CONFIG = getattr(settings, 'CASABLANCA_SCRAPER_CONFIG', {})


@shared_task(bind=True, name='scraper.tasks.run_casablanca_scraper')
def run_casablanca_scraper(self, target_date=None, test_mode=False):
    """
    Tâche principale pour scraper la Bourse de Casablanca
    Récupère les données depuis les API endpoints et les sauvegarde dans la base de données

    Args:
        target_date (str, optional): Date cible au format YYYY-MM-DD
        test_mode (bool): Si True, traite seulement quelques entreprises pour test
    """

    # Vérifier et acquérir le lock pour éviter l'exécution concurrente
    lock_acquired = cache.add(CASABLANCA_SCRAPER_LOCK_KEY, self.request.id, SCRAPER_LOCK_TIMEOUT)

    if not lock_acquired:
        existing_task_id = cache.get(CASABLANCA_SCRAPER_LOCK_KEY)
        logger.warning(f"Scraper Casablanca déjà en cours d'exécution (task: {existing_task_id})")
        return {
            'status': 'skipped',
            'reason': f'Another Casablanca scraper task is already running: {existing_task_id}',
            'task_id': self.request.id,
            'timestamp': timezone.now().isoformat(),
            'scraper_type': 'casablanca_api'
        }

    if not MODELS_AVAILABLE:
        logger.error("❌ Modèles Django non disponibles")
        return {
            'status': 'error',
            'error': 'Django models not available',
            'task_id': self.request.id,
            'timestamp': timezone.now().isoformat(),
            'scraper_type': 'casablanca_api'
        }

    # Créer un enregistrement d'exécution pour l'audit
    execution = None
    try:
        execution_date = date.today()
        if target_date:
            try:
                execution_date = datetime.strptime(target_date, '%Y-%m-%d').date()
            except ValueError:
                logger.warning(f"Format de date invalide: {target_date}, utilisation de la date du jour")

        execution = CasablancaApiExecution.objects.create(
            task_id=self.request.id,
            execution_date=execution_date,
            status='running',
            api_urls_used=CASABLANCA_CONFIG.get('API_URLS', []),
            user_agent=CASABLANCA_CONFIG.get('USER_AGENT', '')
        )
        execution.add_log(f"🚀 Démarrage du scraper Casablanca - Mode test: {test_mode}")

    except Exception as e:
        logger.error(f"❌ Erreur création enregistrement d'exécution: {e}")
        # Continuer sans audit trail si nécessaire

    try:
        logger.info(f"🚀 Démarrage scraper Casablanca Stock Exchange - Task ID: {self.request.id}")

        # === ÉTAPE 1: Récupération des données API ===
        logger.info("📡 Récupération des données depuis l'API Casablanca...")

        api_data = _fetch_casablanca_api_data(execution)

        if not api_data:
            error_msg = "Aucune donnée récupérée depuis l'API"
            logger.error(f"❌ {error_msg}")
            if execution:
                execution.complete(success=False, error_message=error_msg)
            return {
                'status': 'failed',
                'error': error_msg,
                'task_id': self.request.id,
                'timestamp': timezone.now().isoformat(),
                'scraper_type': 'casablanca_api'
            }

        # === ÉTAPE 2: Traitement et sauvegarde ===
        logger.info(f"💾 Traitement de {len(api_data)} entreprises...")

        if test_mode:
            # En mode test, traiter seulement les 5 premières entreprises
            api_data = api_data[:5]
            logger.info(f"🧪 Mode test activé - traitement de {len(api_data)} entreprises")

        processing_result = _process_and_save_companies(api_data, execution, test_mode)

        # === ÉTAPE 3: Finalisation ===
        success = processing_result['companies_saved'] > 0

        if execution:
            execution.update_stats(
                total_companies_api=len(api_data),
                companies_processed=processing_result['companies_processed'],
                companies_saved=processing_result['companies_saved'],
                companies_failed=processing_result['companies_failed'],
                companies_skipped=processing_result['companies_skipped']
            )
            execution.complete(success=success)

        result = {
            'status': 'success' if success else 'failed',
            'task_id': self.request.id,
            'timestamp': timezone.now().isoformat(),
            'scraper_type': 'casablanca_api',
            'test_mode': test_mode,
            'execution_date': execution_date.isoformat() if execution_date else None,
            'total_companies_api': len(api_data),
            'companies_processed': processing_result['companies_processed'],
            'companies_saved': processing_result['companies_saved'],
            'companies_failed': processing_result['companies_failed'],
            'companies_skipped': processing_result['companies_skipped'],
            'processing_duration': processing_result.get('duration', 0),
            'execution_id': execution.id if execution else None
        }

        if success:
            logger.info(f"✅ Scraper Casablanca terminé avec succès - {result['companies_saved']} entreprises sauvegardées")
        else:
            logger.error(f"❌ Scraper Casablanca échoué - Aucune entreprise sauvegardée")

        return result

    except Exception as e:
        error_msg = f"Erreur fatale dans le scraper Casablanca: {str(e)}"
        logger.error(error_msg, exc_info=True)

        if execution:
            execution.complete(success=False, error_message=error_msg)

        return {
            'status': 'failed',
            'error': error_msg,
            'task_id': self.request.id,
            'timestamp': timezone.now().isoformat(),
            'scraper_type': 'casablanca_api'
        }

    finally:
        # Libérer le lock
        cache.delete(CASABLANCA_SCRAPER_LOCK_KEY)


def _fetch_casablanca_api_data(execution=None) -> List[Dict]:
    """
    Récupère les données depuis les API endpoints de la Bourse de Casablanca

    Returns:
        List[Dict]: Liste des données d'entreprises
    """
    api_urls = CASABLANCA_CONFIG.get('API_URLS', [])
    headers = {
        'User-Agent': CASABLANCA_CONFIG.get('USER_AGENT', 'XCapital-Bot/1.0'),
        **CASABLANCA_CONFIG.get('HEADERS', {})
    }
    timeout = CASABLANCA_CONFIG.get('REQUEST_TIMEOUT', 30)
    verify_ssl = CASABLANCA_CONFIG.get('VERIFY_SSL', False)
    rate_limit_delay = CASABLANCA_CONFIG.get('RATE_LIMIT_DELAY', 2)

    all_companies = []

    for i, url in enumerate(api_urls, 1):
        try:
            logger.info(f"📡 Récupération Page {i}: {url}")

            if execution:
                execution.add_log(f"Récupération Page {i}")

            start_time = time.time()
            response = requests.get(
                url,
                headers=headers,
                timeout=timeout,
                verify=verify_ssl
            )
            response_time = time.time() - start_time

            response.raise_for_status()

            data = response.json()
            logger.info(f"✅ Page {i} récupérée en {response_time:.2f}s - Status: {response.status_code}")

            # Extraire les données selon la structure de l'API
            companies_data = []
            if isinstance(data, dict):
                if 'data' in data:
                    if isinstance(data['data'], dict) and 'data' in data['data']:
                        companies_data = data['data']['data']
                    elif isinstance(data['data'], list):
                        companies_data = data['data']
                elif isinstance(data, list):
                    companies_data = data
            elif isinstance(data, list):
                companies_data = data

            logger.info(f"📊 Page {i}: {len(companies_data)} entreprises trouvées")
            all_companies.extend(companies_data)

            # Mettre à jour les statistiques d'exécution
            if execution:
                if i == 1:
                    execution.page1_companies = len(companies_data)
                    execution.page1_success = True
                elif i == 2:
                    execution.page2_companies = len(companies_data)
                    execution.page2_success = True

                # Sauvegarder les temps de réponse
                if not execution.api_response_times:
                    execution.api_response_times = {}
                execution.api_response_times[f'page_{i}'] = response_time
                execution.save()

            # Respecter le rate limiting
            if i < len(api_urls):
                time.sleep(rate_limit_delay)

        except requests.exceptions.RequestException as e:
            error_msg = f"Erreur requête API Page {i}: {e}"
            logger.error(f"❌ {error_msg}")
            if execution:
                execution.add_log(f"ERREUR Page {i}: {error_msg}")
        except json.JSONDecodeError as e:
            error_msg = f"Erreur décodage JSON Page {i}: {e}"
            logger.error(f"❌ {error_msg}")
            if execution:
                execution.add_log(f"ERREUR JSON Page {i}: {error_msg}")
        except Exception as e:
            error_msg = f"Erreur inattendue Page {i}: {e}"
            logger.error(f"❌ {error_msg}")
            if execution:
                execution.add_log(f"ERREUR Page {i}: {error_msg}")

    logger.info(f"📊 Total entreprises récupérées: {len(all_companies)}")
    return all_companies


def _process_and_save_companies(companies_data: List[Dict], execution=None, test_mode=False) -> Dict:
    """
    Traite et sauvegarde les données d'entreprises dans la base de données

    Args:
        companies_data: Liste des données d'entreprises depuis l'API
        execution: Instance CasablancaApiExecution pour l'audit
        test_mode: Mode test

    Returns:
        Dict: Statistiques de traitement
    """
    start_time = time.time()
    stats = {
        'companies_processed': 0,
        'companies_saved': 0,
        'companies_failed': 0,
        'companies_skipped': 0,
        'errors': []
    }

    batch_size = CASABLANCA_CONFIG.get('DB_BATCH_SIZE', 50)
    save_raw_data = CASABLANCA_CONFIG.get('SAVE_RAW_DATA', True)

    for i, company_data in enumerate(companies_data):
        try:
            stats['companies_processed'] += 1

            # Parser les données de l'entreprise
            parsed_data = _parse_company_data(company_data)

            if not parsed_data:
                stats['companies_skipped'] += 1
                continue

            # Sauvegarder dans la base de données
            success = _save_company_to_database(parsed_data, execution)

            if success:
                stats['companies_saved'] += 1

                # Créer un enregistrement d'audit si activé
                if save_raw_data and execution:
                    _create_audit_record(company_data, parsed_data, execution, 'success')
            else:
                stats['companies_failed'] += 1
                if save_raw_data and execution:
                    _create_audit_record(company_data, parsed_data, execution, 'failed')

            # Log de progression
            if (i + 1) % 10 == 0:
                logger.info(f"📊 Progression: {i + 1}/{len(companies_data)} entreprises traitées")

        except Exception as e:
            error_msg = f"Erreur traitement entreprise {i}: {e}"
            logger.error(f"❌ {error_msg}")
            stats['companies_failed'] += 1
            stats['errors'].append(error_msg)

            if execution:
                execution.add_log(f"ERREUR entreprise {i}: {error_msg}")

    duration = time.time() - start_time
    stats['duration'] = duration

    logger.info(f"✅ Traitement terminé en {duration:.2f}s - "
                f"Sauvées: {stats['companies_saved']}, "
                f"Échouées: {stats['companies_failed']}, "
                f"Ignorées: {stats['companies_skipped']}")

    return stats


def _parse_company_data(company_data: Dict) -> Optional[Dict]:
    """
    Parse les données d'une entreprise depuis l'API Casablanca

    Args:
        company_data: Données brutes de l'entreprise depuis l'API

    Returns:
        Dict: Données parsées et formatées pour la base de données
    """
    try:
        # Extraire les attributs selon la structure de l'API
        attributes = company_data.get('attributes', {})
        company_id = company_data.get('id', '')

        # Fonctions utilitaires pour la conversion sécurisée
        def safe_decimal(value, default='0.0000'):
            try:
                if value is None or value == '':
                    return Decimal(default)
                return Decimal(str(value))
            except (ValueError, TypeError, Exception):
                return Decimal(default)

        def safe_int(value, default=0):
            try:
                if value is None or value == '':
                    return default
                return int(float(value))
            except (ValueError, TypeError):
                return default

        # Extraire les données financières
        symbol = attributes.get('symbol', '').strip()
        company_name = attributes.get('instrument_name', '').strip()

        # Si pas de symbole, essayer d'autres champs
        if not symbol:
            symbol = attributes.get('code', '').strip()
        if not company_name:
            company_name = attributes.get('name', '').strip()

        # Validation des champs requis
        if not symbol and not company_id:
            logger.warning("Entreprise sans symbole ni ID - ignorée")
            return None

        # Construire les données formatées
        parsed_data = {
            'api_id': str(company_id),
            'symbol': symbol[:50] if symbol else f"ID_{company_id}"[:50],
            'company_name': company_name[:255] if company_name else f"Company {company_id}"[:255],
            'date_trade': date.today(),

            # Prix
            'open_price': safe_decimal(attributes.get('open_price')),
            'close_price': safe_decimal(attributes.get('close_price')),
            'high_price': safe_decimal(attributes.get('high_price')),
            'low_price': safe_decimal(attributes.get('low_price')),
            'current_price': safe_decimal(attributes.get('current_price')),

            # Volume et valeur
            'volume': safe_int(attributes.get('volume')),
            'shares_traded': safe_int(attributes.get('shares_traded')),
            'total_trades': safe_int(attributes.get('total_trades')),
            'market_cap': safe_decimal(attributes.get('market_cap')),

            # Variations
            'price_change': safe_decimal(attributes.get('variation')),
            'price_change_percent': safe_decimal(attributes.get('variation_percent')),

            # Métadonnées
            'currency': attributes.get('currency', 'MAD'),
            'api_source': 'casablanca_api',
            'last_update': datetime.now(),
            'raw_data': company_data  # Garder les données brutes
        }

        return parsed_data

    except Exception as e:
        logger.error(f"❌ Erreur parsing entreprise {company_data.get('id', 'Unknown')}: {e}")
        return None


def _save_company_to_database(parsed_data: Dict, execution=None) -> bool:
    """
    Sauvegarde une entreprise dans la base de données PostgreSQL

    Args:
        parsed_data: Données parsées de l'entreprise
        execution: Instance CasablancaApiExecution pour l'audit

    Returns:
        bool: True si sauvegarde réussie
    """
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:
                # 1. Insérer/Mettre à jour l'entreprise dans XCapitalTerminal_Companies
                cursor.execute("""
                    INSERT INTO "XCapitalTerminal_Companies"
                    (company_id, symbol, nom_francais, nom_anglais, nom_arabe, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
                    ON CONFLICT (company_id) DO UPDATE SET
                    symbol = EXCLUDED.symbol,
                    nom_francais = EXCLUDED.nom_francais,
                    nom_anglais = EXCLUDED.nom_anglais,
                    updated_at = NOW()
                    RETURNING id
                """, [
                    parsed_data['api_id'],
                    parsed_data['symbol'],
                    parsed_data['company_name'],
                    parsed_data['company_name'],
                    ""  # nom_arabe vide pour l'instant
                ])

                company_result = cursor.fetchone()
                if not company_result:
                    # Si INSERT a échoué, récupérer l'ID existant
                    cursor.execute("""
                        SELECT id FROM "XCapitalTerminal_Companies"
                        WHERE company_id = %s
                    """, [parsed_data['api_id']])
                    company_result = cursor.fetchone()

                if not company_result:
                    raise Exception(f"Impossible de créer/récupérer l'entreprise {parsed_data['api_id']}")

                company_pk = company_result[0]

                # 2. Insérer/Mettre à jour les données de prix dans XCapitalTerminal_CompanyBonds
                cursor.execute("""
                    INSERT INTO "XCapitalTerminal_CompanyBonds"
                    (company_id, date_trade, open_price, high_price, low_price, close_price,
                     current_price, volume, shares_traded, total_trades, market_cap,
                     created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                    ON CONFLICT (company_id, date_trade) DO UPDATE SET
                    open_price = EXCLUDED.open_price,
                    high_price = EXCLUDED.high_price,
                    low_price = EXCLUDED.low_price,
                    close_price = EXCLUDED.close_price,
                    current_price = EXCLUDED.current_price,
                    volume = EXCLUDED.volume,
                    shares_traded = EXCLUDED.shares_traded,
                    total_trades = EXCLUDED.total_trades,
                    market_cap = EXCLUDED.market_cap,
                    updated_at = NOW()
                    RETURNING id
                """, [
                    company_pk,
                    parsed_data['date_trade'],
                    parsed_data['open_price'],
                    parsed_data['high_price'],
                    parsed_data['low_price'],
                    parsed_data['close_price'],
                    parsed_data['current_price'],
                    parsed_data['volume'],
                    parsed_data['shares_traded'],
                    parsed_data['total_trades'],
                    parsed_data['market_cap']
                ])

                bond_result = cursor.fetchone()
                if bond_result:
                    logger.debug(f"💾 Sauvé: {parsed_data['symbol']} - {parsed_data['close_price']} MAD")
                    return True
                else:
                    logger.warning(f"⚠️ Échec sauvegarde bond pour {parsed_data['symbol']}")
                    return False

    except Exception as e:
        logger.error(f"❌ Erreur sauvegarde {parsed_data.get('symbol', 'Unknown')}: {e}")
        if execution:
            execution.add_log(f"ERREUR sauvegarde {parsed_data.get('symbol', 'Unknown')}: {e}")
        return False


def _create_audit_record(raw_data: Dict, parsed_data: Dict, execution, status: str):
    """
    Crée un enregistrement d'audit pour traçabilité

    Args:
        raw_data: Données brutes de l'API
        parsed_data: Données parsées
        execution: Instance CasablancaApiExecution
        status: Statut ('success' ou 'failed')
    """
    try:
        ScrapedItem.objects.create(
            source_url=execution.api_urls_used[0] if execution.api_urls_used else '',
            scraper_type='casablanca_api',
            title=parsed_data.get('company_name', ''),
            company_symbol=parsed_data.get('symbol', ''),
            company_id=parsed_data.get('api_id', ''),
            close_price=parsed_data.get('close_price'),
            volume=parsed_data.get('volume'),
            raw_data=raw_data,
            processed_data=parsed_data,
            status=status,
            execution_id=execution.task_id if execution else ''
        )
    except Exception as e:
        logger.warning(f"⚠️ Erreur création audit record: {e}")


@shared_task(bind=True, name='scraper.tasks.test_casablanca_api_connectivity')
def test_casablanca_api_connectivity(self):
    """
    Tâche pour tester la connectivité avec l'API Casablanca Stock Exchange
    Exécutée avant le scraping principal pour vérifier la disponibilité
    """
    try:
        logger.info(f"🔍 Test connectivité API Casablanca - Task ID: {self.request.id}")

        api_urls = CASABLANCA_CONFIG.get('API_URLS', [])
        headers = {
            'User-Agent': CASABLANCA_CONFIG.get('USER_AGENT', 'XCapital-Bot/1.0'),
            **CASABLANCA_CONFIG.get('HEADERS', {})
        }
        timeout = CASABLANCA_CONFIG.get('REQUEST_TIMEOUT', 30)
        verify_ssl = CASABLANCA_CONFIG.get('VERIFY_SSL', False)

        results = []
        all_success = True

        for i, url in enumerate(api_urls, 1):
            try:
                start_time = time.time()
                response = requests.get(
                    url,
                    headers=headers,
                    timeout=timeout,
                    verify=verify_ssl
                )
                response_time = time.time() - start_time

                success = response.status_code == 200
                all_success = all_success and success

                result = {
                    'page': i,
                    'url': url,
                    'status_code': response.status_code,
                    'response_time': round(response_time, 2),
                    'success': success,
                    'content_length': len(response.content) if success else 0
                }

                if success:
                    try:
                        data = response.json()
                        result['json_valid'] = True
                        result['data_count'] = len(data.get('data', {}).get('data', [])) if isinstance(data, dict) else 0
                    except json.JSONDecodeError:
                        result['json_valid'] = False
                        result['data_count'] = 0
                else:
                    result['json_valid'] = False
                    result['data_count'] = 0
                    result['error'] = f"HTTP {response.status_code}"

                results.append(result)
                logger.info(f"✅ Page {i}: {response.status_code} en {response_time:.2f}s")

            except Exception as e:
                all_success = False
                result = {
                    'page': i,
                    'url': url,
                    'success': False,
                    'error': str(e),
                    'response_time': 0
                }
                results.append(result)
                logger.error(f"❌ Page {i}: {e}")

        return {
            'status': 'success' if all_success else 'partial',
            'task_id': self.request.id,
            'timestamp': timezone.now().isoformat(),
            'all_apis_available': all_success,
            'total_apis_tested': len(api_urls),
            'successful_apis': sum(1 for r in results if r.get('success', False)),
            'results': results,
            'message': 'Toutes les APIs sont disponibles' if all_success else 'Certaines APIs ne sont pas disponibles'
        }

    except Exception as e:
        logger.error(f"❌ Erreur test connectivité: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'task_id': self.request.id,
            'timestamp': timezone.now().isoformat()
        }


@shared_task(bind=True, name='scraper.tasks.cleanup_old_data')
def cleanup_old_data(self, days_to_keep=30):
    """
    Tâche de nettoyage des anciennes données

    Args:
        days_to_keep (int): Nombre de jours de données à conserver
    """
    try:
        logger.info(f"🧹 Nettoyage des données anciennes - Garder {days_to_keep} jours")

        cutoff_date = timezone.now() - timezone.timedelta(days=days_to_keep)

        # Nettoyer les enregistrements ScrapedItem
        scraped_deleted = ScrapedItem.objects.filter(
            extracted_at__lt=cutoff_date
        ).delete()

        # Nettoyer les exécutions anciennes
        executions_deleted = CasablancaApiExecution.objects.filter(
            started_at__lt=cutoff_date,
            status__in=['completed', 'failed', 'cancelled']
        ).delete()

        logger.info(f"✅ Nettoyage terminé - "
                   f"ScrapedItems supprimés: {scraped_deleted[0]}, "
                   f"Exécutions supprimées: {executions_deleted[0]}")

        return {
            'status': 'success',
            'task_id': self.request.id,
            'timestamp': timezone.now().isoformat(),
            'days_to_keep': days_to_keep,
            'cutoff_date': cutoff_date.isoformat(),
            'scraped_items_deleted': scraped_deleted[0],
            'executions_deleted': executions_deleted[0]
        }

    except Exception as e:
        logger.error(f"❌ Erreur nettoyage: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'task_id': self.request.id,
            'timestamp': timezone.now().isoformat()
        }


@shared_task(bind=True, name='scraper.tasks.monthly_maintenance')
def monthly_maintenance(self):
    """
    Tâche de maintenance mensuelle
    """
    try:
        logger.info(f"🔧 Maintenance mensuelle - Task ID: {self.request.id}")

        # Statistiques générales
        total_companies = 0
        total_bonds = 0
        total_scraped_items = ScrapedItem.objects.count()
        total_executions = CasablancaApiExecution.objects.count()

        try:
            with connection.cursor() as cursor:
                cursor.execute('SELECT COUNT(*) FROM "XCapitalTerminal_Companies"')
                total_companies = cursor.fetchone()[0]

                cursor.execute('SELECT COUNT(*) FROM "XCapitalTerminal_CompanyBonds"')
                total_bonds = cursor.fetchone()[0]
        except Exception as e:
            logger.warning(f"⚠️ Erreur récupération statistiques DB: {e}")

        # Nettoyage approfondi (garder 90 jours)
        cleanup_result = cleanup_old_data.delay(days_to_keep=90)

        return {
            'status': 'success',
            'task_id': self.request.id,
            'timestamp': timezone.now().isoformat(),
            'statistics': {
                'total_companies': total_companies,
                'total_bonds': total_bonds,
                'total_scraped_items': total_scraped_items,
                'total_executions': total_executions
            },
            'cleanup_task_id': cleanup_result.id,
            'message': 'Maintenance mensuelle terminée'
        }

    except Exception as e:
        logger.error(f"❌ Erreur maintenance: {e}")
        return {
            'status': 'error',
            'error': str(e),
            'task_id': self.request.id,
            'timestamp': timezone.now().isoformat()
        }


# ===============================================================================
# LEGACY TASKS - Compatibilité avec l'ancien système
# ===============================================================================

@shared_task(bind=True, name='scraper.tasks.run_scraper')
def run_scraper(self):
    """
    Tâche legacy - redirige vers le nouveau scraper Casablanca
    """
    logger.info("🔄 Redirection vers le nouveau scraper Casablanca")
    return run_casablanca_scraper.delay()


@shared_task(bind=True, name='scraper.tasks.test_connection')
def test_connection(self):
    """
    Tâche simple pour tester la connexion Celery
    """
    try:
        return {
            'status': 'success',
            'message': 'Connexion Celery fonctionnelle!',
            'task_id': self.request.id,
            'timestamp': timezone.now().isoformat(),
            'worker_info': {
                'hostname': self.request.hostname,
                'delivery_info': self.request.delivery_info
            }
        }
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e),
            'task_id': self.request.id
        }


@shared_task(bind=True, name='scraper.tasks.check_tables')
def check_tables(self):
    """
    Tâche pour vérifier spécifiquement les tables XCapitalTerminal
    """
    try:
        with connection.cursor() as cursor:
            # Vérifier la structure des tables
            cursor.execute("""
                SELECT 
                    c.column_name,
                    c.data_type,
                    c.is_nullable,
                    c.column_default
                FROM information_schema.columns c
                WHERE c.table_name IN ('XCapitalTerminal_Companies', 'XCapitalTerminal_CompanyBonds')
                ORDER BY c.table_name, c.ordinal_position
            """)
            
            columns = cursor.fetchall()
            
            # Compter les colonnes par table
            companies_columns = [col for col in columns if 'Companies' in str(col)]
            bonds_columns = [col for col in columns if 'Bonds' in str(col)]

            # Vérifier la relation
            cursor.execute("""
                SELECT COUNT(*)
                FROM "XCapitalTerminal_CompanyBonds" cb
                INNER JOIN "XCapitalTerminal_Companies" c ON cb.company_id = c.id
            """)

            relation_count = cursor.fetchone()[0]

            # Compter les enregistrements
            cursor.execute('SELECT COUNT(*) FROM "XCapitalTerminal_Companies"')
            companies_count = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM "XCapitalTerminal_CompanyBonds"')
            bonds_count = cursor.fetchone()[0]

            return {
                'status': 'success',
                'message': 'Tables vérifiées avec succès',
                'companies_count': companies_count,
                'bonds_count': bonds_count,
                'relation_count': relation_count,
                'companies_columns': len(companies_columns),
                'bonds_columns': len(bonds_columns),
                'task_id': self.request.id,
                'timestamp': timezone.now().isoformat()
            }
            
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e),
            'task_id': self.request.id
        }


# ===============================================================================
# LEGACY PHASE 3 TASKS - Redirection vers le nouveau système
# ===============================================================================

@shared_task(bind=True, name='scraper.tasks.run_daily_update')
def run_daily_update(self, target_date=None):
    """
    Tâche legacy Phase 3 - Redirige vers le nouveau scraper Casablanca
    """
    logger.info("🔄 Redirection vers le nouveau scraper Casablanca (run_daily_update)")
    return run_casablanca_scraper.delay(target_date=target_date)


@shared_task(bind=True, name='scraper.tasks.run_test_update')
def run_test_update(self):
    """
    Tâche legacy Phase 3 - Redirige vers le nouveau scraper Casablanca en mode test
    """
    logger.info("🔄 Redirection vers le nouveau scraper Casablanca (mode test)")
    return run_casablanca_scraper.delay(test_mode=True)


@shared_task(bind=True, name='scraper.tasks.test_single_company')
def test_single_company(self, company_id: int):
    """
    Tâche legacy pour tester une seule entreprise - Redirige vers test de connectivité
    """
    logger.info(f"🔄 Redirection vers test de connectivité (company_id: {company_id})")
    return test_casablanca_api_connectivity.delay()


@shared_task(bind=True, name='scraper.tasks.get_system_status')
def get_system_status(self):
    """
    Tâche legacy pour récupérer le statut du système - Redirige vers test de connectivité
    """
    logger.info("🔄 Redirection vers test de connectivité (system status)")
    return test_casablanca_api_connectivity.delay()
