"""
Validateur de données pour la Bourse de Casablanca
Valide et nettoie les données récupérées
"""

import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import pandas as pd

from config import Config

class DataValidator:
    """
    Validateur et nettoyeur de données financières
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Statistiques de validation
        self.records_processed = 0
        self.records_valid = 0
        self.records_invalid = 0
        self.validation_errors = []
    
    def validate_single_record(self, record: Dict) -> Tuple[bool, Dict]:
        """
        Valide un enregistrement individuel
        
        Args:
            record: Enregistrement à valider
        
        Returns:
            Tuple (is_valid, cleaned_record)
        """
        self.records_processed += 1
        
        try:
            if 'attributes' not in record:
                self.validation_errors.append(f"Enregistrement sans 'attributes': {record.get('id', 'Unknown')}")
                self.records_invalid += 1
                return False, {}
            
            attrs = record['attributes']
            
            # Validation des champs obligatoires
            for field in Config.REQUIRED_FIELDS:
                if field not in attrs:
                    self.validation_errors.append(f"Champ manquant '{field}' dans {record.get('id', 'Unknown')}")
                    self.records_invalid += 1
                    return False, {}
            
            # Nettoyage et conversion des données
            cleaned_record = {
                'id': record.get('id', ''),
                'date': self.clean_date(attrs.get('dateTime', '')),
                'opening_price': self.clean_price(attrs.get('openingPrice')),
                'closing_price': self.clean_price(attrs.get('closingPrice')),
                'high_price': self.clean_price(attrs.get('highPrice')),
                'low_price': self.clean_price(attrs.get('lowPrice')),
                'quantity': self.clean_quantity(attrs.get('quantity')),
                'volume': self.clean_quantity(attrs.get('cumulVolumeEchange')),
                'total_trades': self.clean_quantity(attrs.get('totalTrades', 0)),
                'variation': self.clean_price(attrs.get('variation', 0)),
                'variation_percent': self.clean_price(attrs.get('variationPourcent', 0)),
                'last_price': self.clean_price(attrs.get('lastPrice', 0)),
                'average_price': self.clean_price(attrs.get('averagePrice', 0))
            }
            
            # Validation des valeurs
            if not self.validate_prices(cleaned_record):
                self.records_invalid += 1
                return False, {}
            
            if not self.validate_volume(cleaned_record):
                self.records_invalid += 1
                return False, {}
            
            self.records_valid += 1
            return True, cleaned_record
            
        except Exception as e:
            self.validation_errors.append(f"Erreur validation: {e}")
            self.records_invalid += 1
            return False, {}
    
    def clean_date(self, date_str: str) -> str:
        """Nettoie et formate les dates"""
        try:
            if not date_str:
                return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # Conversion de différents formats
            if 'T' in date_str:
                dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            else:
                dt = datetime.strptime(date_str, "%Y-%m-%d")
            
            return dt.strftime("%Y-%m-%d %H:%M:%S")
            
        except Exception as e:
            self.logger.warning(f"Erreur nettoyage date '{date_str}': {e}")
            return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def clean_price(self, price_value: Any) -> float:
        """Nettoie et convertit les prix"""
        try:
            if price_value is None or price_value == '':
                return 0.0
            
            # Conversion en float
            if isinstance(price_value, str):
                # Supprimer les espaces et remplacer les virgules
                price_str = price_value.strip().replace(',', '.')
                return float(price_str)
            
            return float(price_value)
            
        except Exception as e:
            self.logger.warning(f"Erreur nettoyage prix '{price_value}': {e}")
            return 0.0
    
    def clean_quantity(self, quantity_value: Any) -> int:
        """Nettoie et convertit les quantités"""
        try:
            if quantity_value is None or quantity_value == '':
                return 0
            
            # Conversion en int
            if isinstance(quantity_value, str):
                quantity_str = quantity_value.strip().replace(',', '').replace('.', '')
                return int(float(quantity_str))
            
            return int(float(quantity_value))
            
        except Exception as e:
            self.logger.warning(f"Erreur nettoyage quantité '{quantity_value}': {e}")
            return 0
    
    def validate_prices(self, record: Dict) -> bool:
        """Valide la cohérence des prix"""
        try:
            opening = record['opening_price']
            closing = record['closing_price']
            high = record['high_price']
            low = record['low_price']
            
            # Vérifier que les prix sont positifs
            if any(price < 0 for price in [opening, closing, high, low]):
                self.validation_errors.append(f"Prix négatifs détectés: {record['id']}")
                return False
            
            # Vérifier la cohérence des prix
            if high > 0 and low > 0:
                if high < low:
                    self.validation_errors.append(f"Prix haut < prix bas: {record['id']}")
                    return False
                
                if opening > 0 and (opening > high or opening < low):
                    self.validation_errors.append(f"Prix ouverture incohérent: {record['id']}")
                    return False
                
                if closing > 0 and (closing > high or closing < low):
                    self.validation_errors.append(f"Prix fermeture incohérent: {record['id']}")
                    return False
            
            # Vérifier les variations extrêmes
            if opening > 0 and closing > 0:
                variation = abs((closing - opening) / opening) * 100
                if variation > Config.MAX_PRICE_VARIATION:
                    self.validation_errors.append(f"Variation extrême {variation:.2f}%: {record['id']}")
                    return False
            
            return True
            
        except Exception as e:
            self.validation_errors.append(f"Erreur validation prix: {e}")
            return False
    
    def validate_volume(self, record: Dict) -> bool:
        """Valide les volumes et quantités"""
        try:
            quantity = record['quantity']
            volume = record['volume']
            
            # Vérifier que les volumes sont positifs ou nuls
            if quantity < 0 or volume < 0:
                self.validation_errors.append(f"Volume négatif: {record['id']}")
                return False
            
            return True
            
        except Exception as e:
            self.validation_errors.append(f"Erreur validation volume: {e}")
            return False
    
    def validate_company_data(self, company_id: int, company_name: str, 
                            raw_data: List[Dict]) -> Tuple[List[Dict], Dict]:
        """
        Valide toutes les données d'une entreprise
        
        Args:
            company_id: ID de l'entreprise
            company_name: Nom de l'entreprise
            raw_data: Données brutes
        
        Returns:
            Tuple (validated_records, validation_summary)
        """
        self.logger.info(f"🔍 Validation des données - {company_name} ({len(raw_data)} enregistrements)")
        
        validated_records = []
        
        for record in raw_data:
            is_valid, cleaned_record = self.validate_single_record(record)
            
            if is_valid:
                # Ajouter les métadonnées
                cleaned_record.update({
                    'company_id': company_id,
                    'company_name': company_name,
                    'validation_timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })
                validated_records.append(cleaned_record)
        
        # Résumé de validation
        validation_summary = {
            'company_id': company_id,
            'company_name': company_name,
            'total_records': len(raw_data),
            'valid_records': len(validated_records),
            'invalid_records': len(raw_data) - len(validated_records),
            'validation_rate': (len(validated_records) / max(1, len(raw_data))) * 100,
            'errors': self.validation_errors[-10:]  # Dernières 10 erreurs
        }
        
        self.logger.info(f"✅ Validation terminée - {len(validated_records)}/{len(raw_data)} valides ({validation_summary['validation_rate']:.1f}%)")
        
        return validated_records, validation_summary
    
    def create_validation_dataframe(self, validated_records: List[Dict]) -> pd.DataFrame:
        """Convertit les enregistrements validés en DataFrame"""
        if not validated_records:
            return pd.DataFrame()
        
        df = pd.DataFrame(validated_records)
        
        # Conversion des types
        df['date'] = pd.to_datetime(df['date'])
        
        # Colonnes numériques
        numeric_columns = ['opening_price', 'closing_price', 'high_price', 'low_price',
                          'quantity', 'volume', 'total_trades', 'variation', 
                          'variation_percent', 'last_price', 'average_price']
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Tri par date
        df = df.sort_values('date')
        
        return df
    
    def get_validation_statistics(self) -> Dict:
        """Retourne les statistiques de validation"""
        validation_rate = (self.records_valid / max(1, self.records_processed)) * 100
        
        return {
            'total_processed': self.records_processed,
            'valid_records': self.records_valid,
            'invalid_records': self.records_invalid,
            'validation_rate': round(validation_rate, 2),
            'total_errors': len(self.validation_errors),
            'recent_errors': self.validation_errors[-5:] if self.validation_errors else []
        }
