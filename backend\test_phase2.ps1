# Phase 2 Test Script for Windows PowerShell
# This script tests the complete Phase 2 setup

Write-Host "🎯 XCapital Phase 2 - Complete Setup Test" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Blue

# Check if we're in the right directory
if (-not (Test-Path "manage.py")) {
    Write-Host "❌ Please run this script from the backend directory" -ForegroundColor Red
    exit 1
}

# Check if Red<PERSON> is running
Write-Host "`n🔍 Step 1: Checking Redis..." -ForegroundColor Yellow
try {
    $redisCheck = netstat -an | findstr "6379"
    if ($redisCheck) {
        Write-Host "✅ Redis is running on port 6379" -ForegroundColor Green
    } else {
        Write-Host "❌ Redis is not running. Please start Redis first." -ForegroundColor Red
        Write-Host "Start Redis with: .\redis-windows\redis-server.exe" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "❌ Error checking Redis: $_" -ForegroundColor Red
}

# Run the Python test script
Write-Host "`n🔍 Step 2: Running Python Tests..." -ForegroundColor Yellow
try {
    $pythonPath = "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/python.exe"
    & $pythonPath "final_test.py"
} catch {
    Write-Host "❌ Error running Python tests: $_" -ForegroundColor Red
}

Write-Host "`n🎯 Manual Testing Steps:" -ForegroundColor Green
Write-Host "1. Start Celery worker in a new terminal:" -ForegroundColor White
Write-Host "   celery -A xcapital_backend worker --loglevel=info --pool=solo" -ForegroundColor Cyan

Write-Host "`n2. Start Django server in another terminal:" -ForegroundColor White
Write-Host "   python manage.py runserver" -ForegroundColor Cyan

Write-Host "`n3. Test the API endpoints:" -ForegroundColor White
Write-Host "   Invoke-WebRequest -Uri 'http://localhost:8000/api/scraper/health/'" -ForegroundColor Cyan
Write-Host "   Invoke-WebRequest -Uri 'http://localhost:8000/api/scraper/test-celery/' -Method POST" -ForegroundColor Cyan
Write-Host "   Invoke-WebRequest -Uri 'http://localhost:8000/api/scraper/trigger-test/' -Method POST" -ForegroundColor Cyan

Write-Host "`n✨ Phase 2 test complete!" -ForegroundColor Green
