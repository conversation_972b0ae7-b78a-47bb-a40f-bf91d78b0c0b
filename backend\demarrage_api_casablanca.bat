@echo off
REM ========================================
REM DÉMARRAGE AUTOMATIQUE XCAPITAL TERMINAL
REM API Casablanca + Base de données
REM ========================================

echo.
echo 🚀 DÉMARRAGE XCAPITAL TERMINAL - API CASABLANCA
echo ================================================
echo.

REM Vérifier si on est dans le bon répertoire
if not exist "casablanca_api_client.py" (
    echo ❌ Erreur: Fichier casablanca_api_client.py non trouvé
    echo    Vous devez être dans le répertoire backend/
    pause
    exit /b 1
)

echo ✅ Fichiers système détectés
echo.

REM Test rapide de Python
echo 🔍 Vérification Python...
python -c "print('Python OK')" 2>nul
if errorlevel 1 (
    echo ❌ Python non trouvé ou non fonctionnel
    echo    Installer Python ou vérifier le PATH
    pause
    exit /b 1
)
echo ✅ Python fonctionnel

REM Test des modules requis
echo 🔍 Vérification modules Python...
python -c "import django, requests, celery" 2>nul
if errorlevel 1 (
    echo ❌ Modules manquants (Django, Requests, ou Celery)
    echo    Installer avec: pip install -r requirements.txt
    pause
    exit /b 1
)
echo ✅ Modules Python OK

echo.
echo 📋 MENU DE DÉMARRAGE
echo ====================
echo.
echo 1. Démarrer Redis + Celery (complet)
echo 2. Test rapide API Casablanca
echo 3. Test complet avec base de données  
echo 4. Mise à jour manuelle immédiate
echo 5. Voir les logs récents
echo 6. État du système
echo 7. Quitter
echo.

set /p choice="Votre choix (1-7): "

if "%choice%"=="1" goto start_full
if "%choice%"=="2" goto test_api
if "%choice%"=="3" goto test_complete
if "%choice%"=="4" goto manual_update
if "%choice%"=="5" goto show_logs
if "%choice%"=="6" goto system_status
if "%choice%"=="7" goto end
goto invalid_choice

:start_full
echo.
echo 🚀 DÉMARRAGE COMPLET DU SYSTÈME
echo ===============================
echo.
echo IMPORTANT: Ce script va ouvrir 3 fenêtres:
echo   1. Redis Server
echo   2. Celery Worker  
echo   3. Celery Beat (planificateur)
echo.
echo ⚠️  GARDEZ CES 3 FENÊTRES OUVERTES pour que le système fonctionne!
echo.
pause

REM Démarrer Redis
echo 🔴 Démarrage Redis...
start "Redis Server" cmd /k "cd redis-windows && redis-server.exe"
timeout /t 3 /nobreak >nul

REM Démarrer Celery Worker
echo 👷 Démarrage Celery Worker...
start "Celery Worker" cmd /k "celery -A xcapital_backend worker --loglevel=info --pool=solo"
timeout /t 3 /nobreak >nul

REM Démarrer Celery Beat
echo ⏰ Démarrage Celery Beat...
start "Celery Beat" cmd /k "celery -A xcapital_backend beat --loglevel=info"

echo.
echo ✅ SYSTÈME DÉMARRÉ!
echo.
echo 📋 3 fenêtres ouvertes:
echo    • Redis Server (port 6379)
echo    • Celery Worker (traitement des tâches)
echo    • Celery Beat (planificateur 19:00)
echo.
echo 🕕 Prochaine mise à jour automatique: Aujourd'hui 19:00
echo.
echo ℹ️  Pour arrêter: Fermer les 3 fenêtres
pause
goto end

:test_api
echo.
echo 🧪 TEST RAPIDE API CASABLANCA
echo =============================
echo.
python test_ultra_rapide.py
echo.
pause
goto end

:test_complete
echo.
echo 🧪 TEST COMPLET AVEC BASE DE DONNÉES
echo ====================================
echo.
echo ⚠️  Ce test peut prendre quelques minutes...
echo.
python test_api_complete.py
echo.
pause
goto end

:manual_update
echo.
echo 🔄 MISE À JOUR MANUELLE IMMÉDIATE
echo =================================
echo.
echo Lancement de la mise à jour des données...
echo.
python improved_daily_orchestrator.py
echo.
echo ✅ Mise à jour terminée!
pause
goto end

:show_logs
echo.
echo 📋 LOGS RÉCENTS
echo ===============
echo.
if exist "logs\daily_update.log" (
    echo Dernières lignes du log:
    echo ------------------------
    type logs\daily_update.log | find /V ""
) else (
    echo ℹ️  Aucun log trouvé (normal si pas encore exécuté)
)
echo.
pause
goto end

:system_status
echo.
echo 📊 ÉTAT DU SYSTÈME
echo ==================
echo.

REM Vérifier Redis
echo 🔴 Redis:
tasklist /FI "IMAGENAME eq redis-server.exe" 2>nul | find /i "redis-server.exe" >nul
if errorlevel 1 (
    echo    ❌ Non démarré
) else (
    echo    ✅ En cours d'exécution
)

REM Vérifier si les fichiers clés existent
echo.
echo 📁 Fichiers système:
if exist "casablanca_api_client.py" (echo    ✅ API Client) else (echo    ❌ API Client manquant)
if exist "improved_daily_orchestrator.py" (echo    ✅ Orchestrateur) else (echo    ❌ Orchestrateur manquant)
if exist "improved_database_saver.py" (echo    ✅ Sauvegarde BDD) else (echo    ❌ Sauvegarde BDD manquante)

echo.
echo 🗄️  Base de données:
python -c "
import os, sys
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
try:
    import django
    django.setup()
    from market_data.models import XCapitalTerminal_Companies, XCapitalTerminal_CompanyBonds
    companies = XCapitalTerminal_Companies.objects.count()
    bonds = XCapitalTerminal_CompanyBonds.objects.count()
    print(f'    ✅ Entreprises: {companies}')
    print(f'    ✅ Données prix: {bonds}')
except Exception as e:
    print(f'    ❌ Erreur BDD: {e}')
" 2>nul

echo.
pause
goto end

:invalid_choice
echo.
echo ❌ Choix invalide. Veuillez choisir 1-7.
echo.
pause
goto end

:end
echo.
echo 👋 Au revoir!
echo.
pause
