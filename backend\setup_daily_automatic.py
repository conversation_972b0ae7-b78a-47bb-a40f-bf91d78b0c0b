#!/usr/bin/env python3
"""
Complete Setup for Daily Automatic Data Fetching at 19:00
This script configures everything needed for automatic daily updates
"""

import os
import sys
import django
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
django.setup()

def setup_automatic_daily_fetching():
    """Configure automatic daily data fetching at 19:00"""
    
    print("=" * 70)
    print("🚀 SETTING UP AUTOMATIC DAILY DATA FETCHING")
    print("=" * 70)
    print(f"📅 Setup Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. Test system components
    print("1️⃣ Testing system components...")
    
    try:
        import daily_update_orchestrator
        from daily_updater.config import MOROCCAN_COMPANIES
        print(f"✅ Phase 3 components available")
        print(f"📊 Companies configured: {len(MOROCCAN_COMPANIES)}")
    except ImportError as e:
        print(f"❌ Phase 3 components missing: {e}")
        return False
    
    # 2. Test database connection
    print("\n2️⃣ Testing database connection...")
    try:
        from django.db import connection
        from market_data.postgres_models import XCapitalTerminal_Companies
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            
        print("✅ Database connection working")
        
        # Check tables
        companies_count = XCapitalTerminal_Companies.objects.count()
        print(f"📊 Current companies in database: {companies_count}")
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False
    
    # 3. Setup django-celery-beat schedule
    print("\n3️⃣ Setting up daily schedule (19:00)...")
    try:
        from django_celery_beat.models import PeriodicTask, CrontabSchedule
        import json
        
        # Create cron schedule for 19:00 daily
        schedule, created = CrontabSchedule.objects.get_or_create(
            minute=0,
            hour=19,
            day_of_week='*',
            day_of_month='*',
            month_of_year='*',
        )
        
        schedule_status = "Created new" if created else "Using existing"
        print(f"✅ {schedule_status} cron schedule: Daily at 19:00")
        
        # Remove existing daily task
        existing_tasks = PeriodicTask.objects.filter(
            name='Daily Casablanca Stock Exchange Update'
        )
        if existing_tasks.exists():
            existing_tasks.delete()
            print("🗑️ Removed existing daily task")
        
        # Create new daily task
        daily_task = PeriodicTask.objects.create(
            crontab=schedule,
            name='Daily Casablanca Stock Exchange Update',
            task='scraper.tasks.run_daily_update',
            kwargs=json.dumps({}),
            enabled=True,
        )
        
        print(f"✅ Created daily task: '{daily_task.name}'")
        print(f"📅 Schedule: Every day at 19:00 (7 PM)")
        print(f"🏢 Will update {len(MOROCCAN_COMPANIES)} Moroccan companies")
        
    except Exception as e:
        print(f"❌ Schedule setup error: {e}")
        return False
    
    # 4. Create test schedule (optional, disabled by default)
    print("\n4️⃣ Setting up test schedule (disabled by default)...")
    try:
        # Test schedule every 10 minutes (disabled)
        test_schedule, test_created = CrontabSchedule.objects.get_or_create(
            minute='*/10',
            hour='*',
            day_of_week='*',
            day_of_month='*',
            month_of_year='*',
        )
        
        # Remove existing test task
        PeriodicTask.objects.filter(
            name='Test Casablanca Stock Exchange (Every 10 min)'
        ).delete()
        
        # Create test task (disabled)
        test_task = PeriodicTask.objects.create(
            crontab=test_schedule,
            name='Test Casablanca Stock Exchange (Every 10 min)',
            task='scraper.tasks.run_test_update',
            kwargs=json.dumps({}),
            enabled=False,  # Disabled by default
        )
        
        print(f"✅ Created test task (disabled): '{test_task.name}'")
        print("🔧 To enable test task, set enabled=True in Django admin")
        
    except Exception as e:
        print(f"⚠️ Test schedule setup warning: {e}")
    
    # 5. Test single company to verify system
    print("\n5️⃣ Testing with Bank of Africa (391)...")
    try:
        # Essayer d'abord l'orchestrateur amélioré
        try:
            from improved_daily_orchestrator import test_single_company, get_system_status
            orchestrator_type = "amélioré"
            print(f"📊 Utilisation de l'orchestrateur {orchestrator_type} avec BDD intégrée")
        except ImportError:
            from daily_update_orchestrator import test_single_company, get_system_status
            orchestrator_type = "standard"
            print(f"📊 Utilisation de l'orchestrateur {orchestrator_type}")
        
        # Test du statut système
        status = get_system_status()
        print(f"   Django: {status.get('django_available', False)}")
        print(f"   Database: {status.get('database_available', False)}")
        print(f"   Redis: {status.get('redis_available', False)}")
        
        # Test avec Bank of Africa
        result = test_single_company(391)
        
        if result.get('success', False):
            print("✅ Bank of Africa test successful")
            print(f"💰 Price: {result.get('closing_price', 'N/A')} MAD")
            print(f"📊 Volume: {result.get('volume', 'N/A')}")
            
            if orchestrator_type == "amélioré":
                print(f"💾 Saved to Database: {result.get('database_saved', 'N/A')}")
                print(f"🔄 Data Source: {result.get('data_source', 'N/A')}")
        else:
            print(f"⚠️ Bank of Africa test warning: {result.get('error', 'Unknown')}")
            print("   (This might be normal if markets are closed)")
            
    except Exception as e:
        print(f"⚠️ Test error: {e}")
        print("   (System setup continues...)")
    
    # 6. Show current schedule
    print("\n6️⃣ Current periodic tasks:")
    try:
        tasks = PeriodicTask.objects.all().order_by('name')
        
        for task in tasks:
            status = "🟢 ENABLED" if task.enabled else "🔴 DISABLED"
            print(f"   📝 {task.name}")
            print(f"      Status: {status}")
            print(f"      Task: {task.task}")
            print(f"      Schedule: {task.crontab}")
            print()
            
    except Exception as e:
        print(f"⚠️ Cannot display tasks: {e}")
    
    print("=" * 70)
    print("🎉 AUTOMATIC DAILY FETCHING CONFIGURED SUCCESSFULLY!")
    print("=" * 70)
    print()
    print("✅ WHAT'S CONFIGURED:")
    print("   📅 Daily update at 19:00 (7 PM) every day")
    print("   🏢 77 Moroccan companies from Casablanca Stock Exchange")
    print("   💾 Data saved to XCapitalTerminal_Companies & XCapitalTerminal_CompanyBonds")
    print("   🔄 Automatic retry logic and error handling")
    print("   📊 Complete data validation and processing")
    print()
    print("🚀 TO START THE SYSTEM:")
    print("   1. Start Redis:")
    print("      cd redis-windows")
    print("      .\\redis-server.exe")
    print()
    print("   2. Start Celery Worker (Terminal 1):")
    print("      celery -A xcapital_backend worker --loglevel=info --pool=solo")
    print()
    print("   3. Start Celery Beat Scheduler (Terminal 2):")
    print("      celery -A xcapital_backend beat --loglevel=info")
    print()
    print("   4. Optional - Start Django Server (Terminal 3):")
    print("      python manage.py runserver")
    print()
    print("🧪 MANUAL TEST COMMANDS:")
    print("   Test single company:")
    print("   python manage.py shell -c \"from scraper.tasks import test_single_company; print(test_single_company.delay(391).get())\"")
    print()
    print("   Test 5 companies:")
    print("   python manage.py shell -c \"from scraper.tasks import run_test_update; print(run_test_update.delay().get())\"")
    print()
    print("   Manual daily update:")
    print("   python manage.py shell -c \"from scraper.tasks import run_daily_update; print(run_daily_update.delay().get())\"")
    print()
    print("💡 MONITORING:")
    print("   - Check logs in daily_updater/logs/")
    print("   - Monitor tasks in Django Admin: /admin/django_celery_beat/")
    print("   - System will automatically run every day at 19:00")
    print()
    print("=" * 70)
    
    return True

if __name__ == "__main__":
    success = setup_automatic_daily_fetching()
    if success:
        print("🎉 Setup completed successfully!")
        print("📅 Your system will now automatically fetch data daily at 19:00")
    else:
        print("❌ Setup failed. Please check the errors above.")
    
    exit(0 if success else 1)
