#!/usr/bin/env python3
"""
Test simple de connectivité API avec les URLs exactes fournies
"""

import requests
import json
from datetime import datetime

def test_api_connectivity():
    """Test simple de connectivité"""
    print("🧪 TEST CONNECTIVITÉ API CASABLANCA")
    print("=" * 50)
    
    # URLs exactes fournies par l'utilisateur
    urls = [
        "https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action?page%5Blimit%5D=50&page%5Boffset%5D=0",
        "https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action?page%5Blimit%5D=50&page%5Boffset%5D=50"
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'fr-FR,fr;q=0.9',
        'Referer': 'https://www.casablanca-bourse.com/bourseweb/Negociation-Marche.aspx?Cat=24'
    }
    
    for i, url in enumerate(urls, 1):
        print(f"\n📄 TEST PAGE {i}")
        print("-" * 20)
        print(f"URL: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            print(f"Status: {response.status_code}")
            print(f"Taille: {len(response.content)} bytes")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ JSON valide")
                    
                    if isinstance(data, dict):
                        print(f"Clés: {list(data.keys())}")
                        
                        if 'data' in data:
                            companies = data['data']
                            print(f"🏢 Entreprises: {len(companies)}")
                            
                            if len(companies) > 0:
                                first = companies[0]
                                print(f"Exemple ID: {first.get('id')}")
                                
                                if 'attributes' in first:
                                    attrs = first['attributes']
                                    print(f"Attributs: {list(attrs.keys())[:5]}...")
                                    
                                    # Afficher quelques valeurs importantes
                                    symbol = attrs.get('symbol', 'N/A')
                                    price = attrs.get('close_price', 'N/A')
                                    volume = attrs.get('volume', 'N/A')
                                    print(f"Sample: {symbol} - {price} MAD (Vol: {volume})")
                        
                        if 'meta' in data:
                            meta = data['meta']
                            print(f"Meta: {meta}")
                            
                    return True
                    
                except json.JSONDecodeError:
                    print(f"❌ Réponse non-JSON")
                    print(f"Contenu: {response.text[:100]}...")
                    
            else:
                print(f"❌ Erreur HTTP")
                
        except requests.exceptions.Timeout:
            print(f"❌ Timeout")
        except requests.exceptions.ConnectionError:
            print(f"❌ Connexion impossible")
        except Exception as e:
            print(f"❌ Erreur: {e}")
    
    return False

def show_field_mapping():
    """Afficher le mapping des champs pour la base de données"""
    print(f"\n🗺️ MAPPING CHAMPS API -> BASE DE DONNÉES")
    print("=" * 50)
    
    mapping = {
        "API Field": "Database Column",
        "----------": "---------------",
        "id": "→ company_id (relation)",
        "attributes.symbol": "→ XCapitalTerminal_Companies.symbol",
        "attributes.instrument_name": "→ XCapitalTerminal_Companies.name",
        "attributes.close_price": "→ XCapitalTerminal_CompanyBonds.close_price",
        "attributes.open_price": "→ XCapitalTerminal_CompanyBonds.open_price",
        "attributes.high_price": "→ XCapitalTerminal_CompanyBonds.high_price",
        "attributes.low_price": "→ XCapitalTerminal_CompanyBonds.low_price",
        "attributes.volume": "→ XCapitalTerminal_CompanyBonds.volume",
        "attributes.value": "→ XCapitalTerminal_CompanyBonds.value_mad",
        "attributes.variation": "→ XCapitalTerminal_CompanyBonds.price_change",
        "attributes.variation_percent": "→ XCapitalTerminal_CompanyBonds.price_change_percent",
        "date.today()": "→ XCapitalTerminal_CompanyBonds.date_trade"
    }
    
    for api_field, db_column in mapping.items():
        print(f"   {api_field:25} {db_column}")

def main():
    """Test principal"""
    print(f"🔍 ANALYSE API CASABLANCA")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # Test de connectivité
    api_working = test_api_connectivity()
    
    # Afficher le mapping
    show_field_mapping()
    
    # Résumé
    print(f"\n📊 RÉSUMÉ")
    print("=" * 20)
    
    if api_working:
        print("✅ API accessible et fonctionnelle")
        print("✅ Structure JSON compatible")
        print("✅ Données exploitables pour la BDD")
        print()
        print("🚀 PROCHAINES ÉTAPES:")
        print("   1. Le casablanca_api_client_v2.py est prêt")
        print("   2. Mapping API -> DB configuré")
        print("   3. Prêt pour l'insertion en base")
    else:
        print("❌ API inaccessible ou problématique")
        print("⚠️ Utiliser le système de fallback mock")
    
    print(f"\n📋 COMMANDES UTILES:")
    print("   python casablanca_api_client_v2.py  # Test complet")
    print("   python db_analyzer.py              # Analyser la BDD")

if __name__ == "__main__":
    main()
