"""
Management command to set up daily data fetching at 19:00
Run with: python manage.py setup_daily_schedule
"""

from django.core.management.base import BaseCommand
from django_celery_beat.models import PeriodicTask, CrontabSchedule
import json


class Command(BaseCommand):
    help = 'Set up daily data fetching schedule at 19:00 (7 PM)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--remove',
            action='store_true',
            help='Remove existing daily schedule',
        )

    def handle(self, *args, **options):
        if options['remove']:
            self.remove_daily_schedule()
        else:
            self.setup_daily_schedule()

    def setup_daily_schedule(self):
        """Set up daily data fetching at 19:00 (7 PM)"""
        
        self.stdout.write("🕰️ Setting up daily data fetching schedule...")
        
        # Create or get the cron schedule for 19:00 every day
        schedule, created = CrontabSchedule.objects.get_or_create(
            minute=0,      # At minute 0
            hour=19,       # At 19:00 (7 PM)
            day_of_week='*',   # Every day of the week
            day_of_month='*',  # Every day of the month
            month_of_year='*', # Every month
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f"✅ Created new cron schedule: Daily at 19:00")
            )
        else:
            self.stdout.write(
                self.style.WARNING(f"⚠️ Cron schedule already exists: Daily at 19:00")
            )

        # Remove existing daily update task if it exists
        PeriodicTask.objects.filter(name='Daily Casablanca Stock Exchange Update').delete()

        # Create the periodic task
        task = PeriodicTask.objects.create(
            crontab=schedule,
            name='Daily Casablanca Stock Exchange Update',
            task='scraper.tasks.run_daily_update',
            kwargs=json.dumps({
                'target_date': None,  # Use current date
            }),
            enabled=True,
        )

        self.stdout.write(
            self.style.SUCCESS(
                f"🎉 Successfully created daily task: '{task.name}'"
            )
        )
        self.stdout.write(
            self.style.SUCCESS(
                f"📅 Schedule: Every day at 19:00 (7 PM)"
            )
        )
        self.stdout.write(
            self.style.SUCCESS(
                f"🏢 Task: Fetch data for 77 Moroccan companies"
            )
        )
        
        # Also create a test task that runs every 5 minutes for testing
        test_schedule, test_created = CrontabSchedule.objects.get_or_create(
            minute='*/5',  # Every 5 minutes
            hour='*',
            day_of_week='*',
            day_of_month='*',
            month_of_year='*',
        )
        
        # Remove existing test task if it exists
        PeriodicTask.objects.filter(name='Test Casablanca Stock Exchange (Every 5 min)').delete()

        test_task = PeriodicTask.objects.create(
            crontab=test_schedule,
            name='Test Casablanca Stock Exchange (Every 5 min)',
            task='scraper.tasks.run_test_update',
            kwargs=json.dumps({}),
            enabled=False,  # Disabled by default
        )

        self.stdout.write(
            self.style.WARNING(
                f"🧪 Also created test task (disabled): '{test_task.name}'"
            )
        )
        self.stdout.write(
            self.style.WARNING(
                f"🔧 To enable test task: python manage.py shell -c \"from django_celery_beat.models import PeriodicTask; PeriodicTask.objects.filter(name='Test Casablanca Stock Exchange (Every 5 min)').update(enabled=True)\""
            )
        )

        # Show current tasks
        self.show_current_tasks()

    def remove_daily_schedule(self):
        """Remove daily schedule"""
        
        self.stdout.write("🗑️ Removing daily data fetching schedule...")
        
        # Remove tasks
        daily_tasks = PeriodicTask.objects.filter(
            name__in=[
                'Daily Casablanca Stock Exchange Update',
                'Test Casablanca Stock Exchange (Every 5 min)'
            ]
        )
        
        count = daily_tasks.count()
        daily_tasks.delete()
        
        self.stdout.write(
            self.style.SUCCESS(f"✅ Removed {count} periodic tasks")
        )
        
        # Clean up unused schedules
        unused_schedules = CrontabSchedule.objects.filter(periodictask=None)
        schedule_count = unused_schedules.count()
        unused_schedules.delete()
        
        self.stdout.write(
            self.style.SUCCESS(f"✅ Cleaned up {schedule_count} unused schedules")
        )

    def show_current_tasks(self):
        """Show current periodic tasks"""
        
        self.stdout.write("\n📋 CURRENT PERIODIC TASKS:")
        self.stdout.write("=" * 50)
        
        tasks = PeriodicTask.objects.all().order_by('name')
        
        if not tasks:
            self.stdout.write(self.style.WARNING("No periodic tasks found"))
            return
            
        for task in tasks:
            status = "🟢 ENABLED" if task.enabled else "🔴 DISABLED"
            
            self.stdout.write(f"\n📝 {task.name}")
            self.stdout.write(f"   Status: {status}")
            self.stdout.write(f"   Task: {task.task}")
            self.stdout.write(f"   Schedule: {task.crontab}")
            
            if task.kwargs:
                self.stdout.write(f"   Arguments: {task.kwargs}")
                
        self.stdout.write("\n" + "=" * 50)
        self.stdout.write(
            self.style.SUCCESS(
                "🚀 To start the scheduler: celery -A xcapital_backend beat --loglevel=info"
            )
        )
        self.stdout.write(
            self.style.SUCCESS(
                "🔧 To start the worker: celery -A xcapital_backend worker --loglevel=info --pool=solo"
            )
        )
