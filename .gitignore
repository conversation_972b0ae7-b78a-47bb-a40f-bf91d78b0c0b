# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
node_modules/
/.pnp
.pnp.js

# testing
/coverage

# production
/build
build/
dist/

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

#for the backend 
xcapitalbackend/.env

# Django
*.log
*.pot
*.pyc
__pycache__/
local_settings.py
db.sqlite3
db.sqlite3-journal
media/

# Python
*.py[cod]
*$py.class
*.so
.Python
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Cache files - IMPORTANT: These are causing the large file issues
.cache/
*.cache
*.pack
*.idx

# Frontend specific large files
xcapital-terminal-frontend/frontend/node_modules/
xcapital-terminal-frontend/frontend/.cache/
xcapital-terminal-frontend/frontend/build/
xcapital-terminal-frontend/frontend/dist/

# Data files that are too large for git
*.pkl
public/data_visualisation/
public/updated_predictions/
