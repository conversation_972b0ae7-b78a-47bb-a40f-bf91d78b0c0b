from django.contrib import admin
# Legacy market models (removed APIs) – keeping import commented for potential future cleanup
from .models import Stock, StockPrice, MarketIndex, IndexPrice, TradingSession
from .postgres_models import XCapitalCompany, XCapitalCompanyBond, XCapitalIndex, XCapitalIndexValue

# Register your models here.
# (Legacy) désactivé: retirer l'enregistrement admin des anciens modèles
# admin.site.register(Stock)
# admin.site.register(StockPrice)
# admin.site.register(MarketIndex)
# admin.site.register(IndexPrice)
# admin.site.register(TradingSession)

# PostgreSQL models for XCapital
@admin.register(XCapitalCompany)
class XCapitalCompanyAdmin(admin.ModelAdmin):
    list_display = ['id', 'symbol', 'nom_francais', 'company_id', 'created_at']
    list_filter = ['created_at']
    search_fields = ['symbol', 'nom_francais', 'nom_anglais', 'company_id']
    ordering = ['symbol']

@admin.register(XCapitalCompanyBond)
class XCapitalCompanyBondAdmin(admin.ModelAdmin):
    list_display = ['id', 'company', 'date_trade', 'close_price', 'volume', 'price_change']
    list_filter = ['company', 'date_trade']
    search_fields = ['company__symbol', 'company__nom_francais']
    ordering = ['-date_trade']
    
    def price_change(self, obj):
        return f"{obj.daily_change_pct:.2f}%" if obj.daily_change_pct else "N/A"
    price_change.short_description = 'Price Change (%)'


@admin.register(XCapitalIndex)
class XCapitalIndexAdmin(admin.ModelAdmin):
    list_display = ['index_id', 'index_name', 'created_at', 'updated_at']
    list_filter = ['created_at', 'updated_at']
    search_fields = ['index_id', 'index_name']
    ordering = ['index_name']


@admin.register(XCapitalIndexValue)
class XCapitalIndexValueAdmin(admin.ModelAdmin):
    list_display = ['id', 'index_table', 'date_value', 'value', 'daily_change', 'daily_change_pct', 'value_change']
    list_filter = ['index_table', 'date_value']
    search_fields = ['index_table__index_id', 'index_table__index_name']
    ordering = ['-date_value']
    
    def value_change(self, obj):
        if obj.daily_change_pct:
            return f"{obj.daily_change_pct:.2f}%"
        elif obj.calculated_daily_change_pct:
            return f"{obj.calculated_daily_change_pct:.2f}%"
        return "N/A"
    value_change.short_description = 'Value Change (%)'
