#!/usr/bin/env python3
"""
Test complet Phase 3 - XCapital Terminal Backend
Teste toutes les fonctionnalités d'intégration avec la Bourse de Casablanca
"""

import os
import sys
import django
import time
import json
from datetime import datetime

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
django.setup()

def test_phase3_comprehensive():
    """Test complet de Phase 3"""
    
    print("=" * 70)
    print("🚀 TEST COMPLET PHASE 3 - XCAPITAL TERMINAL BACKEND")
    print("=" * 70)
    print(f"📅 Date du test: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. Test des imports
    print("1️⃣ Test des imports...")
    try:
        from scraper.tasks import (
            run_daily_update, 
            run_test_update, 
            test_single_company, 
            get_system_status
        )
        import daily_update_orchestrator
        print("✅ Tous les modules Phase 3 importés avec succès")
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False
    
    # 2. Test Redis
    print("\n2️⃣ Test de Redis...")
    try:
        from django.core.cache import cache
        cache.set('test_key', 'test_value', timeout=5)
        if cache.get('test_key') == 'test_value':
            print("✅ Redis fonctionne correctement")
        else:
            print("❌ Redis ne fonctionne pas")
            return False
    except Exception as e:
        print(f"❌ Erreur Redis: {e}")
        return False
    
    # 3. Test du statut du système
    print("\n3️⃣ Test du statut du système...")
    try:
        status = daily_update_orchestrator.get_system_status()
        print("📊 Statut du système:")
        for key, value in status.items():
            print(f"   {key}: {'✅' if value else '❌'} {value}")
        
        if not status.get('daily_updater_available', False):
            print("⚠️  Système de mise à jour non disponible")
            return False
    except Exception as e:
        print(f"❌ Erreur statut système: {e}")
        return False
    
    # 4. Test de l'API Casablanca
    print("\n4️⃣ Test de connexion API Casablanca...")
    try:
        from daily_updater.casablanca_api_client import CasablancaAPIClient
        api_client = CasablancaAPIClient()
        
        if api_client.test_connection():
            print("✅ Connexion API Casablanca réussie")
        else:
            print("❌ Connexion API Casablanca échouée")
            return False
    except Exception as e:
        print(f"❌ Erreur API: {e}")
        return False
    
    # 5. Test d'une entreprise spécifique
    print("\n5️⃣ Test avec Attijariwafa Bank (ID: 511)...")
    try:
        result = daily_update_orchestrator.test_single_company(511)
        print("🏦 Résultat test Attijariwafa Bank:")
        print(f"   Succès: {'✅' if result.get('success', False) else '❌'} {result.get('success', False)}")
        
        if result.get('success', False):
            print(f"   Entreprise: {result.get('company_name', 'N/A')}")
            print(f"   Prix de clôture: {result.get('closing_price', 'N/A')} MAD")
            print(f"   Volume: {result.get('volume', 'N/A')}")
            print(f"   Nombre d'enregistrements: {result.get('records_count', 0)}")
        else:
            print(f"   Erreur: {result.get('error', 'Erreur inconnue')}")
    except Exception as e:
        print(f"❌ Erreur test entreprise: {e}")
        return False
    
    # 6. Test des tâches Celery
    print("\n6️⃣ Test des tâches Celery...")
    try:
        # Test statut système via Celery
        print("   Test get_system_status...")
        status_task = get_system_status.delay()
        status_result = status_task.get(timeout=10)
        
        if status_result.get('status') == 'success':
            print("   ✅ Tâche get_system_status réussie")
        else:
            print(f"   ❌ Tâche get_system_status échouée: {status_result}")
            return False
            
        # Test entreprise via Celery
        print("   Test test_single_company...")
        company_task = test_single_company.delay(511)
        company_result = company_task.get(timeout=15)
        
        if company_result.get('status') == 'success':
            print("   ✅ Tâche test_single_company réussie")
            test_res = company_result.get('test_result', {})
            if test_res.get('success', False):
                print(f"   💰 Prix Attijariwafa: {test_res.get('closing_price', 'N/A')} MAD")
        else:
            print(f"   ❌ Tâche test_single_company échouée: {company_result}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur tâches Celery: {e}")
        return False
    
    # 7. Test de mise à jour en mode test
    print("\n7️⃣ Test de mise à jour en mode test (5 entreprises)...")
    try:
        test_task = run_test_update.delay()
        test_result = test_task.get(timeout=30)
        
        if test_result.get('status') == 'success':
            print("✅ Mise à jour test réussie")
            print(f"   Entreprises traitées: {test_result.get('companies_processed', 0)}")
            print(f"   Entreprises réussies: {test_result.get('companies_successful', 0)}")
            print(f"   Durée: {test_result.get('duration_seconds', 0):.2f} secondes")
        else:
            print(f"⚠️  Mise à jour test partiellement échouée: {test_result}")
    except Exception as e:
        print(f"❌ Erreur mise à jour test: {e}")
        return False
    
    # 8. Vérification des modèles Django
    print("\n8️⃣ Test des modèles Django...")
    try:
        from market_data.postgres_models import XCapitalTerminal_Companies, XCapitalTerminal_CompanyBonds
        
        companies_count = XCapitalTerminal_Companies.objects.count()
        bonds_count = XCapitalTerminal_CompanyBonds.objects.count()
        
        print(f"   Entreprises en base: {companies_count}")
        print(f"   Obligations en base: {bonds_count}")
        
        if companies_count > 0:
            print("✅ Modèles Django fonctionnels")
        else:
            print("⚠️  Aucune donnée en base (normal pour un premier test)")
            
    except Exception as e:
        print(f"❌ Erreur modèles Django: {e}")
        return False
    
    print("\n" + "=" * 70)
    print("🎉 PHASE 3 TESTÉE AVEC SUCCÈS!")
    print("=" * 70)
    print("✅ Système de récupération Bourse de Casablanca opérationnel")
    print("✅ 77 entreprises marocaines configurées")
    print("✅ API, validation, traitement et sauvegarde fonctionnels")
    print("✅ Intégration Celery et Redis réussie")
    print("✅ Modèles Django XCapitalTerminal prêts")
    print()
    print("🚀 Phase 3 prête pour la production!")
    print("=" * 70)
    
    return True

if __name__ == "__main__":
    success = test_phase3_comprehensive()
    exit(0 if success else 1)
