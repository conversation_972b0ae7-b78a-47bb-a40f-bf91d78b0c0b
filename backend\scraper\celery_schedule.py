"""
Configuration des tâches périodiques pour Celery Beat
"""

from celery.schedules import crontab
from django.conf import settings

# Configuration des tâches périodiques
CELERY_BEAT_SCHEDULE = {
    # Tâche principale de scraping quotidien à 19:00
    'daily-scraper': {
        'task': 'scraper.tasks.run_scraper',
        'schedule': crontab(hour=19, minute=0),  # Tous les jours à 19:00
        'options': {
            'expires': 3600,  # Expirer après 1 heure si pas exécutée
        }
    },
    
    # Tâche de nettoyage hebdomadaire (dimanche à 02:00)
    'weekly-cleanup': {
        'task': 'scraper.tasks.cleanup_old_data',
        'schedule': crontab(hour=2, minute=0, day_of_week=0),  # Dimanche à 02:00
        'kwargs': {'days_to_keep': 30},  # Garder 30 jours de données
    },
    
    # Tâche de health check toutes les heures
    'hourly-health-check': {
        'task': 'scraper.tasks.test_scraper_task',
        'schedule': crontab(minute=0),  # Toutes les heures
        'kwargs': {'test_url': 'https://httpbin.org/html'},
        'options': {
            'expires': 3600,
        }
    },
}

# Fusionner avec la configuration existante si elle existe
if hasattr(settings, 'CELERY_BEAT_SCHEDULE'):
    CELERY_BEAT_SCHEDULE.update(settings.CELERY_BEAT_SCHEDULE)

# Configuration du timezone
CELERY_TIMEZONE = getattr(settings, 'TIME_ZONE', 'UTC')
