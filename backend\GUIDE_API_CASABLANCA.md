# 🚀 GUIDE DE DÉMARRAGE RAPIDE - API CASABLANCA
===============================================

## ✅ SYSTÈME PRÊT

Votre système a été configuré avec succès pour récupérer automatiquement les données de la Bourse de Casablanca à 19:00 chaque jour!

## 📁 FICHIERS CRÉÉS

### 1. **casablanca_api_client.py** - Client API principal
- **Fonction principale**: `update_from_casablanca_api()`
- **API URL**: `https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action`
- **Pagination**: Support complet (offset 0, 50, 100...)
- **Base de données**: Sauvegarde directe dans PostgreSQL
- **Fallback**: Gestion des erreurs avec système de récupération

### 2. **improved_daily_orchestrator.py** - Orchestrateur mis à jour
- **Priorité API**: Essaie l'API Casablanca en premier
- **Fallback Mock**: Utilise les données mock si l'API échoue
- **Planification**: Configuré pour 19:00 via Django-Celery-Beat
- **Logging**: Journalisation complète des opérations

### 3. **test_api_complete.py** - Tests complets
- Tests de connexion API
- Tests de parsing des données
- Tests de sauvegarde en base de données
- Tests d'intégration complète

## 🏃‍♂️ DÉMARRAGE EN 3 ÉTAPES

### Étape 1: Démarrer Redis
```powershell
cd redis-windows
redis-server.exe
```

### Étape 2: Démarrer Celery Worker (nouveau terminal)
```powershell
cd backend
celery -A xcapital_backend worker --loglevel=info --pool=solo
```

### Étape 3: Démarrer Celery Beat (nouveau terminal)
```powershell
cd backend
celery -A xcapital_backend beat --loglevel=info
```

## 🧪 TESTS DISPONIBLES

### Test API uniquement (sans Django)
```powershell
python test_simple_api.py
```

### Test complet avec base de données
```powershell
python test_api_complete.py
```

### Test ultra-rapide
```powershell
python test_ultra_rapide.py
```

### Test manuel de l'API client
```powershell
python casablanca_api_client.py
```

### Test de l'orchestrateur
```powershell
python improved_daily_orchestrator.py
```

## 📊 FONCTIONNEMENT DU SYSTÈME

### 1. **Récupération des données** (19:00 automatique)
- Connexion à l'API Casablanca Bourse
- Récupération par pages (50 entreprises par page)
- Headers de navigateur pour éviter les blocages

### 2. **Parsing intelligent**
- Extraction des données financières (prix, volume, etc.)
- Validation des types de données
- Gestion des erreurs de format

### 3. **Sauvegarde PostgreSQL**
- Table `XCapitalTerminal_Companies` (informations entreprises)
- Table `XCapitalTerminal_CompanyBonds` (données de marché)
- Relations via `company_id`
- SQL direct pour performance optimale

### 4. **Système de fallback**
- Si API inaccessible → Utilise données mock
- Si parsing échoue → Passe à l'entreprise suivante
- Si sauvegarde échoue → Log l'erreur et continue

## 🔧 CONFIGURATION

### Variables d'environnement importantes:
```python
DJANGO_SETTINGS_MODULE = 'xcapital_backend.settings'
```

### Paramètres API:
```python
API_URL = "https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action"
PAGE_LIMIT = 50
TIMEOUT = 10 secondes
```

### Headers requis:
```python
User-Agent: Mozilla/5.0 (simulation navigateur)
Accept: application/json
Referer: https://www.casablanca-bourse.com/bourseweb/Negociation-Marche.aspx?Cat=24
```

## 📈 DONNÉES RÉCUPÉRÉES

Pour chaque entreprise:
- **Identifiant**: ID unique de l'entreprise
- **Symbole**: Code boursier (ex: ATW, IAM, etc.)
- **Nom**: Nom complet de l'entreprise
- **Prix**: Ouverture, fermeture, haut, bas
- **Volume**: Nombre de titres échangés
- **Valeur**: Valeur totale en MAD
- **Transactions**: Nombre total de transactions

## 🚨 DÉPANNAGE

### Si l'API ne répond pas:
1. Vérifier la connexion Internet
2. Tester avec `python test_ultra_rapide.py`
3. Le système utilisera automatiquement les données mock

### Si Django ne démarre pas:
1. Vérifier que PostgreSQL fonctionne
2. Exécuter: `python manage.py migrate`
3. Vérifier la configuration dans `xcapital_backend/settings.py`

### Si Celery ne fonctionne pas:
1. Vérifier que Redis fonctionne
2. Redémarrer les workers Celery
3. Vérifier les logs dans `logs/`

## 📋 VÉRIFICATION DU FONCTIONNEMENT

### 1. Vérifier que les données sont sauvées:
```python
from market_data.models import XCapitalTerminal_Companies, XCapitalTerminal_CompanyBonds
print(f"Entreprises: {XCapitalTerminal_Companies.objects.count()}")
print(f"Données prix: {XCapitalTerminal_CompanyBonds.objects.count()}")
```

### 2. Vérifier les tâches planifiées:
```python
from django_celery_beat.models import PeriodicTask
for task in PeriodicTask.objects.all():
    print(f"Tâche: {task.name}, Planification: {task.crontab}")
```

### 3. Vérifier les logs:
```powershell
type logs\daily_update.log
```

## 🎯 STATUT ACTUEL

✅ **API Client**: Créé et configuré  
✅ **Orchestrateur**: Mis à jour avec API  
✅ **Base de données**: PostgreSQL intégré  
✅ **Planification**: Django-Celery-Beat configuré  
✅ **Fallback**: Système mock fonctionnel  
✅ **Tests**: Suite de tests disponible  

## 🔄 MAINTENANCE

### Mise à jour manuelle (test):
```powershell
python improved_daily_orchestrator.py
```

### Forcer une mise à jour API:
```powershell
python casablanca_api_client.py
```

### Voir les dernières données:
```powershell
python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
django.setup()
from market_data.models import XCapitalTerminal_CompanyBonds
for bond in XCapitalTerminal_CompanyBonds.objects.order_by('-date_trade')[:5]:
    print(f'{bond.company.symbol}: {bond.close_price} MAD')
"
```

---

## 🎉 FÉLICITATIONS!

Votre système XCapital Terminal est maintenant configuré pour récupérer automatiquement les données de la Bourse de Casablanca chaque jour à 19:00!

Le système fonctionne de manière autonome avec:
- 🔄 Récupération automatique via l'API officielle
- 🛡️ Système de fallback avec données mock
- 📊 Sauvegarde directe en PostgreSQL
- 📝 Journalisation complète des opérations
- ⏰ Planification via Celery Beat

**Prochaine mise à jour**: Aujourd'hui à 19:00 🕕
