"""
URLs optimisées pour les APIs XCapital avec performance améliorée
"""
from django.urls import path
from . import optimized_views

app_name = 'xcapital_optimized'

urlpatterns = [
    # =========================
    # APIS OPTIMISÉES
    # =========================
    
    # API principale optimisée pour les données de prix
    path('v2/company-data/', optimized_views.optimized_company_price_data, name='optimized-company-data'),
    
    # Vue d'ensemble du marché optimisée
    path('v2/market/overview/', optimized_views.optimized_market_overview, name='optimized-market-overview'),
    
    # Top Gainers et Losers
    path('v2/market/top-gainers-losers/', optimized_views.top_gainers_losers, name='top-gainers-losers'),
    
    # Nouvelles APIs de statistiques du marché
    path('v2/market/total-capitalization/', optimized_views.total_market_capitalization, name='total-market-cap'),
    path('v2/market/avg-daily-volume/', optimized_views.avg_daily_volume, name='avg-daily-volume'),
    path('v2/market/top-volume-analysis/', optimized_views.top_volume_analysis, name='top-volume-analysis'),
    path('v2/market/sector-comparison/', optimized_views.sector_comparison, name='sector-comparison'),
    path('v2/market/sector-performance/', optimized_views.sector_performance, name='sector-performance'),
    path('v2/market/statistics/', optimized_views.market_statistics, name='market-statistics'),
    
    # Gestion du cache
    path('v2/admin/clear-cache/', optimized_views.clear_cache, name='clear-cache'),
]
