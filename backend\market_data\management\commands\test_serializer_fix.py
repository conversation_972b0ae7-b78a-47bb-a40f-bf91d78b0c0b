from django.core.management.base import BaseCommand
from market_data.postgres_serializers import XCapitalIndexDataRequestSerializer
from market_data.models import XCapitalIndex

class Command(BaseCommand):
    help = 'Test les corrections du serializer'
    
    def handle(self, *args, **options):
        self.stdout.write("=== Test des corrections du serializer ===\n")
        
        # Vérifier que l'indice existe
        try:
            index = XCapitalIndex.objects.get(index_id="512336")
            self.stdout.write(f"✅ Indice trouvé: {index.index_name}")
        except XCapitalIndex.DoesNotExist:
            self.stdout.write("❌ Indice 512336 non trouvé dans la base")
            return
        
        # Test 1: Avec 'indices'
        self.stdout.write("\n1. Test avec 'indices':")
        data1 = {
            "indices": "512336",
            "period": "ALL"
        }
        
        serializer1 = XCapitalIndexDataRequestSerializer(data=data1)
        if serializer1.is_valid():
            self.stdout.write("✅ Validé avec succès")
            self.stdout.write(f"   index_id résultant: {serializer1.validated_data.get('index_id')}")
        else:
            self.stdout.write("❌ Erreurs de validation:")
            for field, errors in serializer1.errors.items():
                self.stdout.write(f"   {field}: {errors}")
        
        # Test 2: Avec 'index_id'
        self.stdout.write("\n2. Test avec 'index_id':")
        data2 = {
            "index_id": "512336",
            "period": "ALL"
        }
        
        serializer2 = XCapitalIndexDataRequestSerializer(data=data2)
        if serializer2.is_valid():
            self.stdout.write("✅ Validé avec succès")
            self.stdout.write(f"   index_id résultant: {serializer2.validated_data.get('index_id')}")
        else:
            self.stdout.write("❌ Erreurs de validation:")
            for field, errors in serializer2.errors.items():
                self.stdout.write(f"   {field}: {errors}")
        
        # Test 3: Période CUSTOM
        self.stdout.write("\n3. Test avec période CUSTOM:")
        data3 = {
            "indices": "512336",
            "period": "CUSTOM",
            "start_date": "2024-08-20",
            "end_date": "2024-08-30"
        }
        
        serializer3 = XCapitalIndexDataRequestSerializer(data=data3)
        if serializer3.is_valid():
            self.stdout.write("✅ Validé avec succès")
            self.stdout.write(f"   Période: {serializer3.validated_data.get('start_date')} à {serializer3.validated_data.get('end_date')}")
        else:
            self.stdout.write("❌ Erreurs de validation:")
            for field, errors in serializer3.errors.items():
                self.stdout.write(f"   {field}: {errors}")
        
        # Test 4: Sans index (doit échouer)
        self.stdout.write("\n4. Test sans index (doit échouer):")
        data4 = {
            "period": "ALL"
        }
        
        serializer4 = XCapitalIndexDataRequestSerializer(data=data4)
        if serializer4.is_valid():
            self.stdout.write("❌ Ne devrait pas être valide")
        else:
            self.stdout.write("✅ Échec attendu:")
            for field, errors in serializer4.errors.items():
                self.stdout.write(f"   {field}: {errors}")
        
        self.stdout.write("\n=== Tests terminés ===")
