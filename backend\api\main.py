#!/usr/bin/env python3
"""
XCapital Terminal - ARIMA Prediction API
FastAPI backend for serving ARIMA time series predictions
"""

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import pandas as pd
import numpy as np
import pickle
import os
from datetime import datetime, timedelta
import logging
from pathlib import Path
import warnings
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.stattools import adfuller
from statsmodels.stats.diagnostic import acorr_ljungbox
import itertools
warnings.filterwarnings('ignore')

# Statistical modeling libraries
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.stattools import adfuller
from statsmodels.stats.diagnostic import acorr_ljungbox
import joblib

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="XCapital Terminal - ARIMA Prediction API",
    description="Advanced ARIMA time series forecasting for Moroccan stock market",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
MODELS_DIR = Path("../models/Arima")  # This resolves to C:\Users\<USER>\Desktop\xcapitalterminalfv\xcapital-terminal-frontend\models\Arima
DATA_DIR = Path("../data")

class PredictionRequest(BaseModel):
    ticker: str
    start_date: str
    end_date: str
    forecast_days: int = 30
    confidence_interval: float = 0.95

class PredictionResponse(BaseModel):
    ticker: str
    model_type: str
    forecast_dates: List[str]
    forecast_values: List[float]
    confidence_lower: List[float]
    confidence_upper: List[float]
    test_dates: List[str]
    test_actuals: List[float]
    test_predictions: List[float]
    model_metrics: Dict[str, float]
    status: str
    message: str

class ARIMAModelService:
    """Service for loading and managing ARIMA models"""
    
    def __init__(self):
        self.models_cache = {}
        self.data_cache = {}
        self.models_dir = MODELS_DIR
        self.data_dir = DATA_DIR
        
    def get_available_tickers(self) -> List[str]:
        """Get list of available tickers based on data files"""
        try:
            # Check both model files and data files
            data_files = list(self.data_dir.glob("*_historical_data.csv"))
            tickers = [f.stem.replace("_historical_data", "") for f in data_files]
            return sorted(tickers)
        except Exception as e:
            logger.error(f"Error getting available tickers: {e}")
            return []
    
    def find_optimal_arima_order(self, data: pd.Series, max_p=5, max_d=2, max_q=5):
        """Find optimal ARIMA order using AIC criterion"""
        logger.info(f"Finding optimal ARIMA order for {len(data)} data points")
        
        # Check stationarity
        adf_result = adfuller(data.dropna())
        is_stationary = adf_result[1] < 0.05
        
        if not is_stationary:
            # Try differencing
            diff_data = data.diff().dropna()
            adf_result_diff = adfuller(diff_data)
            if adf_result_diff[1] < 0.05:
                d = 1
                logger.info("Data is stationary after first differencing")
            else:
                d = 2
                logger.info("Using second order differencing")
        else:
            d = 0
            logger.info("Data is already stationary")
        
        # Grid search for optimal p and q
        best_aic = float('inf')
        best_order = (1, d, 1)
        
        p_values = range(0, min(max_p + 1, 6))
        q_values = range(0, min(max_q + 1, 6))
        
        for p, q in itertools.product(p_values, q_values):
            try:
                if p == 0 and q == 0:
                    continue
                
                order = (p, d, q)
                model = ARIMA(data, order=order)
                fitted_model = model.fit()
                
                if fitted_model.aic < best_aic:
                    best_aic = fitted_model.aic
                    best_order = order
                    
            except Exception as e:
                continue
        
        logger.info(f"Optimal ARIMA order: {best_order} with AIC: {best_aic:.2f}")
        return best_order
    
    def train_arima_model(self, ticker: str, data: pd.Series):
        """Train a new ARIMA model from historical data"""
        logger.info(f"Training new ARIMA model for {ticker}")
        
        try:
            # Ensure we have enough data
            if len(data) < 50:
                raise ValueError(f"Insufficient data for ARIMA training: {len(data)} points")
            
            # Remove any missing values
            clean_data = data.dropna()
            
            if len(clean_data) < 30:
                raise ValueError(f"Insufficient clean data for ARIMA training: {len(clean_data)} points")
            
            # Find optimal order
            order = self.find_optimal_arima_order(clean_data)
            
            # Train the model
            model = ARIMA(clean_data, order=order)
            fitted_model = model.fit()
            
            # Validate the model
            residuals = fitted_model.resid
            ljung_box = acorr_ljungbox(residuals, lags=10, return_df=True)
            
            logger.info(f"ARIMA model trained successfully for {ticker}")
            logger.info(f"Model order: {order}")
            logger.info(f"AIC: {fitted_model.aic:.2f}")
            logger.info(f"Log-likelihood: {fitted_model.llf:.2f}")
            
            # Save the model
            self.save_model(ticker, fitted_model)
            
            return fitted_model
            
        except Exception as e:
            logger.error(f"Error training ARIMA model for {ticker}: {e}")
            raise
    
    def save_model(self, ticker: str, model):
        """Save ARIMA model to pickle file"""
        try:
            model_path = self.models_dir / f"{ticker}_arima_model.pkl"
            
            # Create backup if file exists
            if model_path.exists():
                backup_path = self.models_dir / f"{ticker}_arima_model_backup.pkl"
                model_path.rename(backup_path)
                logger.info(f"Created backup: {backup_path}")
            
            # Save new model
            with open(model_path, 'wb') as f:
                pickle.dump(model, f, protocol=pickle.HIGHEST_PROTOCOL)
            
            logger.info(f"Saved ARIMA model for {ticker} to {model_path}")
            
        except Exception as e:
            logger.error(f"Error saving model for {ticker}: {e}")
            raise
    
    def load_model(self, ticker: str):
        """Load ARIMA model for given ticker, retrain if corrupted"""
        if ticker in self.models_cache:
            return self.models_cache[ticker]
            
        model_path = self.models_dir / f"{ticker}_arima_model.pkl"
        
        # Try to load existing model first
        if model_path.exists():
            try:
                # Try multiple loading strategies for different pickle formats
                loading_strategies = [
                    ("standard", lambda f: pickle.load(f)),
                    ("latin1_encoding", lambda f: pickle.load(f, encoding='latin1')),
                    ("bytes_encoding", lambda f: pickle.load(f, encoding='bytes')),
                ]
                
                # Also try joblib if available
                try:
                    import joblib
                    loading_strategies.insert(0, ("joblib", lambda f: joblib.load(model_path)))
                except ImportError:
                    pass
                
                model = None
                error_messages = []
                
                for strategy_name, load_func in loading_strategies:
                    try:
                        if strategy_name == "joblib":
                            model = load_func(None)  # joblib.load doesn't need file handle
                        else:
                            with open(model_path, 'rb') as f:
                                model = load_func(f)
                        
                        # Validate that this is actually an ARIMA model
                        if hasattr(model, 'forecast') or hasattr(model, 'predict'):
                            logger.info(f"Successfully loaded ARIMA model for {ticker} using {strategy_name}")
                            self.models_cache[ticker] = model
                            return model
                        else:
                            logger.warning(f"Loaded object for {ticker} is not a valid ARIMA model")
                            
                    except Exception as e:
                        error_msg = f"{strategy_name}: {str(e)[:100]}"
                        error_messages.append(error_msg)
                        logger.warning(f"Loading strategy {strategy_name} failed for {ticker}: {e}")
                        continue
                
                # If we get here, all loading strategies failed
                logger.warning(f"All loading strategies failed for {ticker}. Will retrain model.")
                logger.warning(f"Errors: {'; '.join(error_messages)}")
                
            except Exception as e:
                logger.warning(f"Unexpected error loading existing model for {ticker}: {e}")
        
        # Model doesn't exist or is corrupted - train new one
        logger.info(f"Training new ARIMA model for {ticker}")
        try:
            # Load historical data
            df = self.load_historical_data(ticker)
            price_series = self.prepare_training_data(df)
            
            # Train new ARIMA model
            model = self.train_arima_model(ticker, price_series)
            self.models_cache[ticker] = model
            return model
            
        except Exception as e:
            logger.error(f"Failed to train ARIMA model for {ticker}: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to load or train ARIMA model for {ticker}: {str(e)}"
            )
    
    def load_historical_data(self, ticker: str) -> pd.DataFrame:
        """Load historical data for given ticker"""
        if ticker in self.data_cache:
            return self.data_cache[ticker]
            
        data_path = self.data_dir / f"{ticker}_historical_data.csv"
        
        if not data_path.exists():
            raise HTTPException(
                status_code=404,
                detail=f"Historical data not found for ticker: {ticker}"
            )
        
        try:
            df = pd.read_csv(data_path)
            # Ensure proper date formatting
            if 'Date' in df.columns:
                df['Date'] = pd.to_datetime(df['Date'])
                df = df.set_index('Date')
            elif 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                df = df.set_index('date')
            
            # Sort by date
            df = df.sort_index()
            
            self.data_cache[ticker] = df
            logger.info(f"Loaded historical data for {ticker}: {len(df)} records")
            return df
            
        except Exception as e:
            logger.error(f"Error loading data for {ticker}: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to load historical data for {ticker}: {str(e)}"
            )
    
    def prepare_training_data(self, df: pd.DataFrame, target_column: str = 'Close') -> pd.Series:
        """Prepare data for ARIMA training/prediction"""
        if target_column not in df.columns:
            # Try common variations
            possible_columns = ['close', 'Close', 'CLOSE', 'price', 'Price']
            target_column = next((col for col in possible_columns if col in df.columns), df.columns[0])
        
        return df[target_column].dropna()
    
    def load_prediction_csv(self, ticker: str) -> pd.DataFrame:
        """Load prediction CSV file for a given ticker"""
        try:
            # Path to the prediction CSV file
            prediction_path = Path("../data/updated_predictions") / f"{ticker}_predictions.csv"
            
            if not prediction_path.exists():
                raise FileNotFoundError(f"Prediction file not found for {ticker}")
            
            # Load the CSV file
            df = pd.read_csv(prediction_path)
            
            # Ensure required columns exist
            required_columns = ['Date', 'Predicted_Price', 'Lower_CI', 'Upper_CI']
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"Missing required column: {col}")
            
            # Convert date column to datetime
            df['Date'] = pd.to_datetime(df['Date'])
            
            logger.info(f"Loaded {len(df)} predictions for {ticker}")
            return df
            
        except Exception as e:
            logger.error(f"Error loading prediction CSV for {ticker}: {e}")
            raise
    
    def make_prediction(self, ticker: str, start_date: str, end_date: str, forecast_days: int = 30) -> Dict[str, Any]:
        """Make ARIMA prediction for given ticker using precomputed CSV predictions"""
        try:
            # Try to load precomputed predictions first
            try:
                predictions_df = self.load_prediction_csv(ticker)
                
                # Filter predictions based on forecast_days
                if len(predictions_df) > forecast_days:
                    predictions_df = predictions_df.head(forecast_days)
                
                # Extract data from CSV
                forecast_dates = predictions_df['Date'].dt.strftime('%Y-%m-%d').tolist()
                forecast_values = predictions_df['Predicted_Price'].tolist()
                confidence_lower = predictions_df['Lower_CI'].tolist()
                confidence_upper = predictions_df['Upper_CI'].tolist()
                
                # Load historical data for test metrics
                try:
                    df = self.load_historical_data(ticker)
                    price_series = self.prepare_training_data(df)
                    
                    # Filter data for the requested date range
                    end_dt = pd.to_datetime(end_date)
                    historical_data = price_series[price_series.index <= end_dt]
                    
                    # Generate test data (last 20% for metrics calculation)
                    test_size = max(1, min(10, int(len(historical_data) * 0.2)))
                    test_data = historical_data[-test_size:]
                    
                    test_actuals = test_data.tolist()
                    test_dates = [dt.strftime('%Y-%m-%d') for dt in test_data.index]
                    
                    # Simple moving average as test predictions for metrics
                    test_predictions = []
                    for i in range(len(test_data)):
                        if i == 0:
                            test_predictions.append(historical_data.iloc[-(test_size + 1)])
                        else:
                            test_predictions.append(test_actuals[i-1])
                    
                    # Calculate metrics
                    mse = np.mean((np.array(test_predictions) - np.array(test_actuals)) ** 2)
                    rmse = np.sqrt(mse)
                    mae = np.mean(np.abs(np.array(test_predictions) - np.array(test_actuals)))
                    mape = np.mean(np.abs((np.array(test_actuals) - np.array(test_predictions)) / np.array(test_actuals))) * 100 if all(x != 0 for x in test_actuals) else 0.0
                    
                except Exception as e:
                    logger.warning(f"Could not calculate test metrics: {e}")
                    test_dates = []
                    test_actuals = []
                    test_predictions = []
                    mse = rmse = mae = mape = 0.0
                
                logger.info(f"Using precomputed predictions for {ticker}: {len(forecast_values)} future predictions")
                
                return {
                    "ticker": ticker,
                    "model_type": "ARIMA (Precomputed)",
                    "forecast_dates": forecast_dates,
                    "forecast_values": [float(v) for v in forecast_values],
                    "confidence_lower": [float(v) for v in confidence_lower],
                    "confidence_upper": [float(v) for v in confidence_upper],
                    "test_dates": test_dates,
                    "test_actuals": [float(v) for v in test_actuals],
                    "test_predictions": [float(v) for v in test_predictions],
                    "model_metrics": {
                        "mse": float(mse),
                        "rmse": float(rmse),
                        "mae": float(mae),
                        "mape": float(mape)
                    },
                    "status": "success",
                    "message": f"Loaded precomputed ARIMA predictions for {ticker} with {len(forecast_values)} future price forecasts"
                }
                
            except Exception as e:
                logger.warning(f"Could not load precomputed predictions for {ticker}: {e}")
                # Fall back to original method
                
            # Original prediction method as fallback
            # Load model and data
            model = self.load_model(ticker)
            df = self.load_historical_data(ticker)
            
            # Prepare data
            price_series = self.prepare_training_data(df)
            
            # Filter data for the requested date range
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            # Get data up to start date for model context
            historical_data = price_series[price_series.index <= end_dt]
            
            if len(historical_data) < 10:
                raise HTTPException(
                    status_code=400,
                    detail="Insufficient historical data for prediction"
                )
            
            # Split data for testing (last 20% for test)
            test_size = max(1, int(len(historical_data) * 0.2))
            train_data = historical_data[:-test_size]
            test_data = historical_data[-test_size:]
            
            # Make in-sample predictions for test period
            test_predictions = []
            test_actuals = []
            test_dates = []
            
            try:
                # Use the loaded/trained model for predictions
                if hasattr(model, 'get_prediction'):
                    # For statsmodels ARIMA models
                    pred_result = model.get_prediction(start=len(train_data), end=len(historical_data)-1)
                    test_predictions = pred_result.predicted_mean.tolist()
                elif hasattr(model, 'forecast'):
                    # Generate step-by-step predictions
                    for i in range(len(test_data)):
                        pred = model.forecast(steps=1)[0]
                        test_predictions.append(pred)
                else:
                    # Last resort prediction method
                    pred_values = model.predict(start=len(train_data), end=len(historical_data)-1)
                    test_predictions = pred_values.tolist()
                
                test_actuals = test_data.tolist()
                test_dates = [dt.strftime('%Y-%m-%d') for dt in test_data.index]
                
            except Exception as e:
                logger.warning(f"In-sample prediction failed: {e}, using simple approach")
                # Simple fallback approach
                test_predictions = [historical_data.iloc[-1]] * len(test_data)
                test_actuals = test_data.tolist()
                test_dates = [dt.strftime('%Y-%m-%d') for dt in test_data.index]
            
            # Generate future forecast
            try:
                if hasattr(model, 'get_forecast'):
                    # Preferred method for statsmodels ARIMA
                    forecast_result = model.get_forecast(steps=forecast_days)
                    forecast_values = forecast_result.predicted_mean.tolist()
                    conf_int = forecast_result.conf_int()
                    confidence_lower = conf_int.iloc[:, 0].tolist()
                    confidence_upper = conf_int.iloc[:, 1].tolist()
                elif hasattr(model, 'forecast'):
                    # Alternative forecast method
                    forecast_result = model.forecast(steps=forecast_days, alpha=0.05)
                    if isinstance(forecast_result, tuple):
                        forecast_values, conf_int = forecast_result
                        if hasattr(conf_int, 'shape') and conf_int.ndim > 1:
                            confidence_lower = conf_int[:, 0].tolist()
                            confidence_upper = conf_int[:, 1].tolist()
                        else:
                            confidence_lower = (forecast_values * 0.95).tolist()
                            confidence_upper = (forecast_values * 1.05).tolist()
                    else:
                        forecast_values = forecast_result.tolist()
                        confidence_lower = (forecast_values * 0.95).tolist()
                        confidence_upper = (forecast_values * 1.05).tolist()
                else:
                    # Fallback forecasting
                    last_value = historical_data.iloc[-1]
                    trend = (historical_data.iloc[-1] - historical_data.iloc[-min(5, len(historical_data))]) / min(5, len(historical_data))
                    forecast_values = [last_value + trend * (i + 1) for i in range(forecast_days)]
                    confidence_lower = [v * 0.95 for v in forecast_values]
                    confidence_upper = [v * 1.05 for v in forecast_values]
                
            except Exception as e:
                logger.warning(f"Forecast failed: {e}, using trend-based forecast")
                # Trend-based fallback
                recent_trend = (historical_data.iloc[-1] - historical_data.iloc[-min(5, len(historical_data))]) / min(5, len(historical_data))
                last_value = historical_data.iloc[-1]
                forecast_values = [last_value + recent_trend * (i + 1) for i in range(forecast_days)]
                confidence_lower = [v * 0.95 for v in forecast_values]
                confidence_upper = [v * 1.05 for v in forecast_values]
            
            # Generate forecast dates
            last_date = historical_data.index[-1]
            forecast_dates = [(last_date + timedelta(days=i+1)).strftime('%Y-%m-%d') for i in range(forecast_days)]
            
            # Calculate metrics
            if len(test_predictions) > 0 and len(test_actuals) > 0:
                mse = np.mean((np.array(test_predictions) - np.array(test_actuals)) ** 2)
                rmse = np.sqrt(mse)
                mae = np.mean(np.abs(np.array(test_predictions) - np.array(test_actuals)))
                mape = np.mean(np.abs((np.array(test_actuals) - np.array(test_predictions)) / np.array(test_actuals))) * 100
            else:
                mse = rmse = mae = mape = 0.0
            
            return {
                "ticker": ticker,
                "model_type": "ARIMA",
                "forecast_dates": forecast_dates,
                "forecast_values": [float(v) for v in forecast_values],
                "confidence_lower": [float(v) for v in confidence_lower],
                "confidence_upper": [float(v) for v in confidence_upper],
                "test_dates": test_dates,
                "test_actuals": [float(v) for v in test_actuals],
                "test_predictions": [float(v) for v in test_predictions],
                "model_metrics": {
                    "mse": float(mse),
                    "rmse": float(rmse),
                    "mae": float(mae),
                    "mape": float(mape)
                },
                "status": "success",
                "message": f"ARIMA prediction completed for {ticker}"
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Prediction error for {ticker}: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Prediction failed for {ticker}: {str(e)}"
            )

# Initialize model service
arima_service = ARIMAModelService()

@app.get("/")
async def root():
    """API health check"""
    return {
        "service": "XCapital Terminal - ARIMA Prediction API",
        "status": "operational",
        "version": "1.0.0",
        "available_tickers": len(arima_service.get_available_tickers())
    }

@app.get("/api/v1/health")
async def health_check():
    """Detailed health check"""
    try:
        available_tickers = arima_service.get_available_tickers()
        return {
            "status": "healthy",
            "models_directory": str(arima_service.models_dir),
            "data_directory": str(arima_service.data_dir),
            "available_tickers": len(available_tickers),
            "sample_tickers": available_tickers[:5]
        }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "unhealthy", "error": str(e)}
        )

@app.get("/api/v1/tickers")
async def get_available_tickers():
    """Get list of available tickers for prediction"""
    try:
        tickers = arima_service.get_available_tickers()
        return {
            "tickers": tickers,
            "count": len(tickers)
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get available tickers: {str(e)}"
        )

@app.get("/api/v1/arima-predict")
async def arima_predict(
    ticker: str = Query(..., description="Stock ticker symbol"),
    start_date: str = Query(..., description="Start date (YYYY-MM-DD)"),
    end_date: str = Query(..., description="End date (YYYY-MM-DD)"),
    forecast_days: int = Query(30, description="Number of days to forecast"),
    list_exog: Optional[str] = Query(None, description="External factors (comma-separated)")
):
    """
    Generate ARIMA prediction for specified ticker and date range
    """
    try:
        # Validate inputs
        if ticker not in arima_service.get_available_tickers():
            raise HTTPException(
                status_code=404,
                detail=f"Ticker {ticker} not available. Use /api/v1/tickers to see available tickers."
            )
        
        # Validate date format
        try:
            pd.to_datetime(start_date)
            pd.to_datetime(end_date)
        except:
            raise HTTPException(
                status_code=400,
                detail="Invalid date format. Use YYYY-MM-DD."
            )
        
        # Make prediction
        result = arima_service.make_prediction(
            ticker=ticker,
            start_date=start_date,
            end_date=end_date,
            forecast_days=forecast_days
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Prediction API error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@app.get("/api/v1/model-info/{ticker}")
async def get_model_info(ticker: str):
    """Get information about ARIMA model for specific ticker"""
    try:
        if ticker not in arima_service.get_available_tickers():
            raise HTTPException(
                status_code=404,
                detail=f"Model not found for ticker: {ticker}"
            )
        
        # Load model to get info
        model = arima_service.load_model(ticker)
        df = arima_service.load_historical_data(ticker)
        
        return {
            "ticker": ticker,
            "model_type": "ARIMA",
            "data_points": len(df),
            "date_range": {
                "start": df.index.min().strftime('%Y-%m-%d'),
                "end": df.index.max().strftime('%Y-%m-%d')
            },
            "model_loaded": True,
            "available_columns": list(df.columns)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get model info: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
