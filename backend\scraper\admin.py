"""
Configuration de l'interface d'administration pour le scraper Casablanca
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Count, Avg
from django.utils import timezone
from datetime import <PERSON>el<PERSON>
from .models import ScraperTarget, ScrapedItem, ScraperExecution, CasablancaApiExecution


@admin.register(ScraperTarget)
class ScraperTargetAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'url', 'is_active', 'scraper_type', 
        'items_count', 'last_scrape', 'created_at'
    ]
    list_filter = ['is_active', 'scraper_type', 'created_at']
    search_fields = ['name', 'url']
    readonly_fields = ['created_at', 'updated_at', 'items_count', 'last_scrape']
    
    fieldsets = (
        ('Informations de base', {
            'fields': ('name', 'url', 'is_active', 'scraper_type')
        }),
        ('Configuration avancée', {
            'fields': ('css_selectors', 'headers'),
            'classes': ('collapse',)
        }),
        ('Statistiques', {
            'fields': ('items_count', 'last_scrape', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def items_count(self, obj):
        """Nombre d'items scrapés pour cette cible"""
        return obj.scraped_items.count()
    items_count.short_description = 'Items scrapés'
    
    def last_scrape(self, obj):
        """Date du dernier scraping"""
        last_item = obj.scraped_items.first()
        if last_item:
            return last_item.extracted_at
        return "Jamais"
    last_scrape.short_description = 'Dernier scraping'


@admin.register(ScrapedItem)
class ScrapedItemAdmin(admin.ModelAdmin):
    list_display = [
        'title_short', 'company_symbol', 'close_price', 'volume',
        'scraper_type', 'status', 'extracted_at', 'target_link'
    ]
    list_filter = ['status', 'scraper_type', 'extracted_at', 'target']
    search_fields = ['title', 'company_symbol', 'company_id', 'source_url']
    readonly_fields = ['extracted_at', 'processing_duration']
    date_hierarchy = 'extracted_at'

    fieldsets = (
        ('Contenu de base', {
            'fields': ('title', 'summary', 'source_url', 'target', 'scraper_type')
        }),
        ('Données Casablanca (si applicable)', {
            'fields': ('company_symbol', 'company_id', 'close_price', 'volume'),
            'classes': ('collapse',)
        }),
        ('Statut et performance', {
            'fields': ('status', 'error_message', 'retry_count', 'extracted_at', 'processing_duration')
        }),
        ('Données brutes et traitées', {
            'fields': ('raw_data', 'processed_data'),
            'classes': ('collapse',)
        }),
        ('Debug (uniquement si DEBUG=True)', {
            'fields': ('raw_html',),
            'classes': ('collapse',)
        }),
    )
    
    def title_short(self, obj):
        """Titre tronqué pour l'affichage"""
        return obj.title[:50] + "..." if len(obj.title) > 50 else obj.title
    title_short.short_description = 'Titre'
    
    def source_url_short(self, obj):
        """URL source tronquée avec lien"""
        url = obj.source_url
        if len(url) > 50:
            display_url = url[:47] + "..."
        else:
            display_url = url
        return format_html('<a href="{}" target="_blank">{}</a>', url, display_url)
    source_url_short.short_description = 'URL Source'
    source_url_short.allow_tags = True
    
    def target_link(self, obj):
        """Lien vers la cible associée"""
        if obj.target:
            url = reverse('admin:scraper_scrapertarget_change', args=[obj.target.pk])
            return format_html('<a href="{}">{}</a>', url, obj.target.name)
        return "Aucune"
    target_link.short_description = 'Cible'
    target_link.allow_tags = True
    
    actions = ['mark_as_pending', 'mark_as_success', 'delete_failed_items']
    
    def mark_as_pending(self, request, queryset):
        """Marquer les items sélectionnés comme en attente"""
        updated = queryset.update(status='pending')
        self.message_user(request, f'{updated} items marqués comme en attente.')
    mark_as_pending.short_description = 'Marquer comme en attente'
    
    def mark_as_success(self, request, queryset):
        """Marquer les items sélectionnés comme réussis"""
        updated = queryset.update(status='success')
        self.message_user(request, f'{updated} items marqués comme réussis.')
    mark_as_success.short_description = 'Marquer comme réussis'
    
    def delete_failed_items(self, request, queryset):
        """Supprimer les items échoués"""
        failed_items = queryset.filter(status='failed')
        count = failed_items.count()
        failed_items.delete()
        self.message_user(request, f'{count} items échoués supprimés.')
    delete_failed_items.short_description = 'Supprimer les items échoués'


@admin.register(ScraperExecution)
class ScraperExecutionAdmin(admin.ModelAdmin):
    list_display = [
        'task_id_short', 'status', 'started_at', 'duration_display',
        'total_urls', 'successful_scrapes', 'failed_scrapes', 'success_rate_display'
    ]
    list_filter = ['status', 'started_at']
    search_fields = ['task_id']
    readonly_fields = [
        'task_id', 'started_at', 'completed_at', 'duration_display',
        'success_rate_display', 'logs_display'
    ]
    date_hierarchy = 'started_at'
    
    fieldsets = (
        ('Informations de base', {
            'fields': ('task_id', 'status', 'started_at', 'completed_at', 'duration_display')
        }),
        ('Statistiques', {
            'fields': ('total_urls', 'successful_scrapes', 'failed_scrapes', 'success_rate_display')
        }),
        ('Logs', {
            'fields': ('logs_display', 'error_details'),
            'classes': ('collapse',)
        }),
    )
    
    def task_id_short(self, obj):
        """ID de tâche tronqué"""
        return obj.task_id[:8] + "..." if len(obj.task_id) > 8 else obj.task_id
    task_id_short.short_description = 'Task ID'
    
    def duration_display(self, obj):
        """Durée formatée"""
        duration = obj.duration
        if duration:
            minutes = int(duration // 60)
            seconds = int(duration % 60)
            return f"{minutes}m {seconds}s"
        return "En cours" if obj.status == 'running' else "N/A"
    duration_display.short_description = 'Durée'
    
    def success_rate_display(self, obj):
        """Taux de succès formaté avec couleur"""
        rate = obj.success_rate
        if rate >= 80:
            color = 'green'
        elif rate >= 50:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color, rate
        )
    success_rate_display.short_description = 'Taux de succès'
    success_rate_display.allow_tags = True
    
    def logs_display(self, obj):
        """Logs formatés pour l'affichage"""
        if obj.logs:
            # Limiter aux 50 dernières lignes
            lines = obj.logs.split('\n')[-50:]
            logs_text = '\n'.join(lines)
            return format_html('<pre style="max-height: 300px; overflow-y: scroll;">{}</pre>', logs_text)
        return "Aucun log disponible"
    logs_display.short_description = 'Logs d\'exécution'
    logs_display.allow_tags = True


@admin.register(CasablancaApiExecution)
class CasablancaApiExecutionAdmin(admin.ModelAdmin):
    list_display = [
        'task_id_short', 'execution_date', 'status', 'companies_processed',
        'companies_saved', 'success_rate_display', 'api_success_rate_display',
        'duration_display'
    ]
    list_filter = ['status', 'execution_date', 'page1_success', 'page2_success']
    search_fields = ['task_id', 'user_agent']
    readonly_fields = [
        'task_id', 'started_at', 'completed_at', 'duration_display',
        'success_rate_display', 'api_success_rate_display', 'logs_display'
    ]
    date_hierarchy = 'execution_date'

    fieldsets = (
        ('Informations de base', {
            'fields': ('task_id', 'execution_date', 'status', 'started_at', 'completed_at', 'duration_display')
        }),
        ('Statistiques API', {
            'fields': (
                'total_companies_api', 'page1_companies', 'page2_companies',
                'page1_success', 'page2_success', 'api_success_rate_display'
            )
        }),
        ('Statistiques de traitement', {
            'fields': (
                'companies_processed', 'companies_saved', 'companies_failed',
                'success_rate_display'
            )
        }),
        ('Configuration', {
            'fields': ('api_urls_used', 'user_agent'),
            'classes': ('collapse',)
        }),
        ('Logs et erreurs', {
            'fields': ('logs_display', 'error_details'),
            'classes': ('collapse',)
        }),
    )

    def task_id_short(self, obj):
        """ID de tâche tronqué"""
        return obj.task_id[:12] + "..." if len(obj.task_id) > 12 else obj.task_id
    task_id_short.short_description = 'Task ID'

    def duration_display(self, obj):
        """Durée formatée"""
        duration = obj.duration
        if duration:
            minutes = int(duration // 60)
            seconds = int(duration % 60)
            return f"{minutes}m {seconds}s"
        return "En cours" if obj.status == 'running' else "N/A"
    duration_display.short_description = 'Durée'

    def success_rate_display(self, obj):
        """Taux de succès de traitement formaté avec couleur"""
        rate = obj.success_rate
        if rate >= 90:
            color = 'green'
        elif rate >= 70:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span>',
            color, rate
        )
    success_rate_display.short_description = 'Taux de succès'
    success_rate_display.allow_tags = True

    def api_success_rate_display(self, obj):
        """Taux de succès API formaté avec couleur"""
        rate = obj.api_success_rate
        if rate >= 100:
            color = 'green'
        elif rate >= 50:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}%</span>',
            color, rate
        )
    api_success_rate_display.short_description = 'Succès API'
    api_success_rate_display.allow_tags = True

    def logs_display(self, obj):
        """Logs formatés pour l'affichage"""
        if obj.logs:
            # Limiter aux 100 dernières lignes
            lines = obj.logs.split('\n')[-100:]
            logs_text = '\n'.join(lines)
            return format_html('<pre style="max-height: 400px; overflow-y: scroll; font-size: 12px;">{}</pre>', logs_text)
        return "Aucun log disponible"
    logs_display.short_description = 'Logs d\'exécution'
    logs_display.allow_tags = True

    actions = ['mark_as_completed', 'mark_as_failed', 'retry_failed_executions']

    def mark_as_completed(self, request, queryset):
        """Marquer les exécutions sélectionnées comme terminées"""
        updated = queryset.update(status='completed')
        self.message_user(request, f'{updated} exécutions marquées comme terminées.')
    mark_as_completed.short_description = 'Marquer comme terminées'

    def mark_as_failed(self, request, queryset):
        """Marquer les exécutions sélectionnées comme échouées"""
        updated = queryset.update(status='failed')
        self.message_user(request, f'{updated} exécutions marquées comme échouées.')
    mark_as_failed.short_description = 'Marquer comme échouées'

    def retry_failed_executions(self, request, queryset):
        """Relancer les exécutions échouées"""
        from .tasks import run_casablanca_scraper

        failed_executions = queryset.filter(status='failed')
        count = 0
        for execution in failed_executions:
            # Relancer la tâche
            run_casablanca_scraper.delay()
            count += 1

        self.message_user(request, f'{count} exécutions relancées.')
    retry_failed_executions.short_description = 'Relancer les exécutions échouées'


# Configuration globale de l'admin
admin.site.site_header = "XCapital - Administration du Scraper Casablanca"
admin.site.site_title = "XCapital Casablanca Scraper Admin"
admin.site.index_title = "Gestion du système de scraping Bourse de Casablanca"
