#!/usr/bin/env python3
"""
CASABLANCA BOURSE API CLIENT - INTÉGRATION BASE DE DONNÉES
=========================================================

Client pour récupérer les données depuis l'API de la Bourse de Casablanca
et les sauvegarder directement dans PostgreSQL.

URLs API utilisées:
- Page 1: https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action?page%5Blimit%5D=50&page%5Boffset%5D=0
- Page 2: https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action?page%5Blimit%5D=50&page%5Boffset%5D=50
"""

import os
import sys
from datetime import datetime, date
from decimal import Decimal
from typing import Dict, List, Optional
import requests
import json
import time
import logging
from pathlib import Path
import urllib3

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

try:
    import django
    django.setup()
    from django.db import transaction, connection
    DJANGO_AVAILABLE = True
    print("✅ Django configuré avec succès")
except Exception as e:
    print(f"❌ Erreur configuration Django: {e}")
    DJANGO_AVAILABLE = False

# Désactiver les avertissements SSL
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class CasablancaApiClient:
    """
    Client API pour la Bourse de Casablanca avec intégration PostgreSQL
    """
    
    def __init__(self):
        self.base_url = "https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action"
        self.logs_path = Path("./logs/")
        self.logs_path.mkdir(exist_ok=True)
        
        # Configuration du logging
        self.setup_logging()
        
        # Statistiques
        self.stats = {
            'api_calls': 0,
            'companies_fetched': 0,
            'companies_saved': 0,
            'companies_failed': 0,
            'errors': []
        }
        
        # Headers pour l'API
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Referer': 'https://www.casablanca-bourse.com/',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
    
    def setup_logging(self):
        """Configure le système de logging"""
        log_file = self.logs_path / f"casablanca_api_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def fetch_page_data(self, page_offset: int = 0, page_limit: int = 50) -> List[Dict]:
        """
        Récupère une page de données depuis l'API
        """
        try:
            url = f"{self.base_url}?page%5Blimit%5D={page_limit}&page%5Boffset%5D={page_offset}"
            
            self.logger.info(f"📡 Récupération page - Offset: {page_offset}, Limit: {page_limit}")
            self.stats['api_calls'] += 1
            
            response = requests.get(url, headers=self.headers, timeout=30, verify=False)
            response.raise_for_status()
            
            data = response.json()
            
            if 'data' in data and 'data' in data['data']:
                companies_data = data['data']['data']
                self.logger.info(f"✅ Récupéré {len(companies_data)} entreprises")
                self.stats['companies_fetched'] += len(companies_data)
                return companies_data
            else:
                self.logger.warning("⚠️ Structure de données inattendue dans la réponse API")
                return []
                
        except requests.exceptions.RequestException as e:
            error_msg = f"Erreur requête API (offset {page_offset}): {e}"
            self.logger.error(f"❌ {error_msg}")
            self.stats['errors'].append(error_msg)
            return []
        except json.JSONDecodeError as e:
            error_msg = f"Erreur décodage JSON (offset {page_offset}): {e}"
            self.logger.error(f"❌ {error_msg}")
            self.stats['errors'].append(error_msg)
            return []
        except Exception as e:
            error_msg = f"Erreur inattendue (offset {page_offset}): {e}"
            self.logger.error(f"❌ {error_msg}")
            self.stats['errors'].append(error_msg)
            return []
    
    def fetch_all_companies(self) -> List[Dict]:
        """
        Récupère toutes les données en utilisant la pagination
        """
        all_companies = []
        
        # Page 1 (offset=0)
        self.logger.info("📡 Récupération Page 1...")
        page1_data = self.fetch_page_data(page_offset=0)
        if page1_data:
            all_companies.extend(page1_data)
            time.sleep(2)  # Respecter l'API
        
        # Page 2 (offset=50)
        self.logger.info("📡 Récupération Page 2...")
        page2_data = self.fetch_page_data(page_offset=50)
        if page2_data:
            all_companies.extend(page2_data)
        
        # Pages supplémentaires si nécessaire
        if len(page2_data) == 50:  # Si la page 2 est pleine, il y a peut-être plus
            self.logger.info("📡 Récupération Page 3...")
            time.sleep(2)
            page3_data = self.fetch_page_data(page_offset=100)
            if page3_data:
                all_companies.extend(page3_data)
        
        self.logger.info(f"📊 Total entreprises récupérées: {len(all_companies)}")
        return all_companies
    
    def parse_company_data(self, company_data: Dict) -> Optional[Dict]:
        """
        Parse les données d'une entreprise depuis l'API en format compatible base de données
        """
        try:
            attributes = company_data.get('attributes', {})
            
            # Récupérer l'ID de l'entreprise
            company_id = company_data.get('id', '')
            
            # Récupérer le symbole depuis les relations
            symbol_data = company_data.get('relationships', {}).get('symbol', {}).get('data', {})
            symbol_id = symbol_data.get('id', '') if symbol_data else ''
            
            # Données financières (avec gestion des valeurs nulles)
            def safe_float(value, default=0.0):
                try:
                    return float(value) if value is not None else default
                except (ValueError, TypeError):
                    return default
            
            def safe_int(value, default=0):
                try:
                    return int(value) if value is not None else default
                except (ValueError, TypeError):
                    return default
            
            open_price = safe_float(attributes.get('openingPrice'))
            high_price = safe_float(attributes.get('highPrice'))
            low_price = safe_float(attributes.get('lowPrice'))
            close_price = safe_float(attributes.get('closingPrice'))
            volume = safe_int(attributes.get('volume'))
            value_mad = safe_float(attributes.get('capitalisation'))
            
            # Essayer de récupérer des informations supplémentaires
            shares_traded = safe_float(attributes.get('sharesTraded'))
            total_trades = safe_int(attributes.get('totalTrades'))
            
            # Déterminer le symbole et le nom
            if symbol_id:
                symbol = symbol_id[:10].upper()  # Limiter à 10 caractères
                company_name = f"Company {symbol}"
            else:
                symbol = f"ID_{company_id}"[:10]
                company_name = f"Company {company_id}"
            
            # Construire les données formatées
            parsed_data = {
                'company_id': int(company_id) if company_id.isdigit() else hash(company_id) % 1000000,
                'symbol': symbol,
                'company_name': company_name,
                'date_trade': date.today(),
                'open_price': Decimal(str(open_price)),
                'high_price': Decimal(str(high_price)),
                'low_price': Decimal(str(low_price)),
                'close_price': Decimal(str(close_price)),
                'volume': volume,
                'value_mad': Decimal(str(value_mad)),
                'shares_traded': shares_traded,
                'total_trades': total_trades,
                'api_source': 'casablanca_bourse',
                'raw_data': json.dumps(company_data)  # Garder les données brutes pour debug
            }
            
            return parsed_data
            
        except Exception as e:
            error_msg = f"Erreur parsing données entreprise {company_data.get('id', 'Unknown')}: {e}"
            self.logger.error(f"❌ {error_msg}")
            self.stats['errors'].append(error_msg)
            return None
    
    def save_company_to_database(self, company_data: Dict) -> bool:
        """
        Sauvegarde une entreprise dans PostgreSQL
        """
        if not DJANGO_AVAILABLE:
            self.logger.warning("Django non disponible - saut sauvegarde")
            return False
        
        try:
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # 1. Insérer/Mettre à jour l'entreprise
                    cursor.execute("""
                        INSERT INTO "XCapitalTerminal_Companies" 
                        (company_id, symbol, nom_francais, nom_anglais, nom_arabe)
                        VALUES (%s, %s, %s, %s, %s)
                        ON CONFLICT (company_id) DO UPDATE SET
                        symbol = EXCLUDED.symbol,
                        nom_francais = EXCLUDED.nom_francais,
                        nom_anglais = EXCLUDED.nom_anglais
                        RETURNING id
                    """, [
                        str(company_data['company_id']),
                        company_data['symbol'],
                        company_data['company_name'],
                        company_data['company_name'],
                        ""
                    ])
                    
                    company_result = cursor.fetchone()
                    if not company_result:
                        # Si INSERT a échoué, essayer de récupérer l'ID existant
                        cursor.execute("""
                            SELECT id FROM "XCapitalTerminal_Companies" 
                            WHERE company_id = %s
                        """, [str(company_data['company_id'])])
                        company_result = cursor.fetchone()
                    
                    if not company_result:
                        raise Exception(f"Impossible de créer/récupérer l'entreprise {company_data['company_id']}")
                    
                    company_pk = company_result[0]
                    
                    # 2. Insérer/Mettre à jour les données de prix
                    cursor.execute("""
                        INSERT INTO "XCapitalTerminal_CompanyBonds"
                        (company_id, date_trade, open_price, high_price, low_price, close_price, volume, value_mad)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (company_id, date_trade) DO UPDATE SET
                        open_price = EXCLUDED.open_price,
                        high_price = EXCLUDED.high_price,
                        low_price = EXCLUDED.low_price,
                        close_price = EXCLUDED.close_price,
                        volume = EXCLUDED.volume,
                        value_mad = EXCLUDED.value_mad
                        RETURNING id
                    """, [
                        company_pk,
                        company_data['date_trade'],
                        company_data['open_price'],
                        company_data['high_price'],
                        company_data['low_price'],
                        company_data['close_price'],
                        company_data['volume'],
                        company_data['value_mad']
                    ])
                    
                    bond_result = cursor.fetchone()
                    if bond_result:
                        self.logger.info(f"💾 Sauvé: {company_data['symbol']} - {company_data['close_price']} MAD")
                        self.stats['companies_saved'] += 1
                        return True
                    else:
                        self.stats['companies_failed'] += 1
                        return False
                        
        except Exception as e:
            error_msg = f"Erreur sauvegarde {company_data['symbol']}: {e}"
            self.logger.error(f"❌ {error_msg}")
            self.stats['errors'].append(error_msg)
            self.stats['companies_failed'] += 1
            return False
    
    def update_all_from_api(self) -> Dict:
        """
        Met à jour toutes les entreprises depuis l'API vers la base de données
        """
        start_time = datetime.now()
        self.logger.info("🚀 DÉBUT MISE À JOUR DEPUIS API CASABLANCA")
        
        # Réinitialiser les statistiques
        self.stats = {
            'api_calls': 0,
            'companies_fetched': 0,
            'companies_saved': 0,
            'companies_failed': 0,
            'errors': []
        }
        
        try:
            # 1. Récupérer toutes les données depuis l'API
            all_companies = self.fetch_all_companies()
            
            if not all_companies:
                self.logger.error("❌ Aucune donnée récupérée depuis l'API")
                return {
                    'success': False,
                    'error': 'Aucune donnée récupérée',
                    'stats': self.stats
                }
            
            # 2. Parser et sauvegarder chaque entreprise
            for i, company_raw in enumerate(all_companies, 1):
                try:
                    # Parser les données
                    parsed_data = self.parse_company_data(company_raw)
                    
                    if parsed_data:
                        # Sauvegarder en base
                        success = self.save_company_to_database(parsed_data)
                        
                        if success:
                            self.logger.info(f"✅ [{i}/{len(all_companies)}] {parsed_data['symbol']}: {parsed_data['close_price']} MAD")
                        else:
                            self.logger.warning(f"⚠️ [{i}/{len(all_companies)}] Échec sauvegarde {parsed_data['symbol']}")
                    else:
                        self.logger.warning(f"⚠️ [{i}/{len(all_companies)}] Échec parsing entreprise")
                        self.stats['companies_failed'] += 1
                
                except Exception as e:
                    error_msg = f"Erreur traitement entreprise {i}: {e}"
                    self.logger.error(f"❌ {error_msg}")
                    self.stats['errors'].append(error_msg)
                    self.stats['companies_failed'] += 1
                
                # Pause entre les traitements
                if i % 10 == 0:
                    time.sleep(1)
            
            # 3. Résultat final
            duration = (datetime.now() - start_time).total_seconds()
            
            result = {
                'success': self.stats['companies_saved'] > 0,
                'companies_processed': len(all_companies),
                'companies_saved': self.stats['companies_saved'],
                'companies_failed': self.stats['companies_failed'],
                'api_calls': self.stats['api_calls'],
                'duration_seconds': duration,
                'errors': self.stats['errors'],
                'timestamp': datetime.now().isoformat(),
                'data_source': 'casablanca_bourse_api'
            }
            
            self.logger.info(f"🎯 RÉSULTAT: {self.stats['companies_saved']}/{len(all_companies)} entreprises sauvées")
            
            return result
            
        except Exception as e:
            error_msg = f"Erreur globale mise à jour API: {e}"
            self.logger.error(f"❌ {error_msg}")
            
            return {
                'success': False,
                'error': error_msg,
                'stats': self.stats,
                'timestamp': datetime.now().isoformat()
            }
    
    def get_latest_data_from_db(self, limit: int = 10) -> List[Dict]:
        """
        Récupère les dernières données depuis la base
        """
        if not DJANGO_AVAILABLE:
            return []
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT c.symbol, c.nom_francais, b.date_trade, b.close_price, b.volume
                    FROM "XCapitalTerminal_CompanyBonds" b
                    JOIN "XCapitalTerminal_Companies" c ON c.id = b.company_id
                    ORDER BY b.date_trade DESC, b.id DESC
                    LIMIT %s
                """, [limit])
                
                results = []
                for row in cursor.fetchall():
                    results.append({
                        'symbol': row[0],
                        'name': row[1],
                        'date': row[2],
                        'price': float(row[3]),
                        'volume': row[4]
                    })
                
                return results
                
        except Exception as e:
            self.logger.error(f"Erreur récupération données BDD: {e}")
            return []

# Instance globale
casablanca_client = CasablancaApiClient()

def update_from_casablanca_api() -> Dict:
    """Fonction helper pour mise à jour depuis l'API"""
    return casablanca_client.update_all_from_api()

def get_latest_market_data(limit: int = 10) -> List[Dict]:
    """Fonction helper pour récupérer les dernières données"""
    return casablanca_client.get_latest_data_from_db(limit)

if __name__ == "__main__":
    print("🧪 TEST CLIENT API CASABLANCA")
    print("=" * 50)
    
    # Test de récupération et sauvegarde
    result = update_from_casablanca_api()
    
    if result['success']:
        print(f"✅ Mise à jour réussie!")
        print(f"   Entreprises sauvées: {result['companies_saved']}")
        print(f"   Durée: {result['duration_seconds']:.2f}s")
        
        # Afficher les dernières données
        latest_data = get_latest_market_data(5)
        print(f"\n📊 Dernières données:")
        for data in latest_data:
            print(f"   {data['symbol']}: {data['price']} MAD ({data['date']})")
    else:
        print(f"❌ Mise à jour échouée: {result.get('error', 'Erreur inconnue')}")
        if result.get('errors'):
            print(f"   Erreurs: {len(result['errors'])}")
    
    print(f"\n🎉 Test terminé!")
