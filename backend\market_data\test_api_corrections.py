from django.test import TestCase, Client
from django.urls import reverse
import json

class IndexAPITest(TestCase):
    
    def setUp(self):
        self.client = Client()
        self.url = '/api/v1/xcapital/indices/form-data/'
        
    def test_api_with_indices_parameter(self):
        """Test avec le paramètre 'indices' au lieu de 'index_id'"""
        data = {
            "indices": "512336",
            "period": "ALL"
        }
        
        response = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type='application/json'
        )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code != 200:
            print(f"Response: {response.json()}")
        else:
            result = response.json()
            print(f"Success: {result.get('success')}")
            if 'summary' in result:
                print(f"Data points: {result['summary'].get('data_points')}")
        
        self.assertIn(response.status_code, [200, 404])  # 404 si pas de données
        
    def test_api_with_custom_period(self):
        """Test avec période CUSTOM"""
        data = {
            "indices": "512336", 
            "period": "CUSTOM",
            "start_date": "2024-08-20",
            "end_date": "2024-08-30"
        }
        
        response = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type='application/json'
        )
        
        print(f"\nCustom period - Status Code: {response.status_code}")
        if response.status_code != 200:
            print(f"Response: {response.json()}")
        else:
            result = response.json()
            print(f"Success: {result.get('success')}")
            if 'summary' in result:
                print(f"Period: {result['summary'].get('start_date')} to {result['summary'].get('end_date')}")
        
        self.assertIn(response.status_code, [200, 404])  # 404 si pas de données
