from django.urls import path
from . import csv_views

app_name = 'csv_data'

urlpatterns = [
    # =========================
    # INSTRUMENTS FINANCIERS
    # =========================
    
    # Liste et détails des instruments
    path('instruments/', csv_views.FinancialInstrumentListView.as_view(), name='instruments-list'),
    path('instruments/<str:symbol>/', csv_views.FinancialInstrumentDetailView.as_view(), name='instrument-detail'),
    
    # Prix des instruments
    path('instruments/<str:symbol>/prices/', csv_views.InstrumentPriceListView.as_view(), name='instrument-prices'),
    path('instruments/<str:symbol>/chart/', csv_views.get_instrument_chart_data, name='instrument-chart'),
    path('instruments/<str:symbol>/variations/', csv_views.get_instrument_price_variations, name='instrument-variations'),
    
    # API POST pour récupérer des données via formulaire
    path('data/request/', csv_views.get_instrument_data_form, name='instrument-data-form'),
    
    # Prédictions
    path('instruments/<str:symbol>/predictions/', csv_views.InstrumentPredictionListView.as_view(), name='instrument-predictions'),
    path('instruments/<str:symbol>/predictions/chart/', csv_views.get_prediction_chart_data, name='prediction-chart'),
    
    # =========================
    # INDICES MASI
    # =========================
    
    # Liste et détails des indices
    path('indices/', csv_views.MASIIndexListView.as_view(), name='masi-indices-list'),
    path('indices/<str:symbol>/', csv_views.MASIIndexDetailView.as_view(), name='masi-index-detail'),
    
    # Valeurs des indices
    path('indices/<str:symbol>/values/', csv_views.MASIIndexValueListView.as_view(), name='masi-index-values'),
    path('indices/<str:symbol>/chart/', csv_views.get_masi_chart_data, name='masi-chart'),
    
    # =========================
    # ANALYSES ET VUES D'ENSEMBLE
    # =========================
    
    # Vue d'ensemble du marché
    path('market/overview/', csv_views.get_market_overview, name='market-overview'),
    
    # Analyses sectorielles
    path('sectors/', csv_views.get_sector_analysis, name='sector-analysis'),
    path('sectors/instruments/', csv_views.get_instruments_by_sector, name='instruments-by-sector'),
    
    # Symboles disponibles
    path('symbols/', csv_views.get_available_symbols, name='available-symbols'),
    
    # Vérification de santé
    path('health/', csv_views.health_check_csv_data, name='health-check'),
]
