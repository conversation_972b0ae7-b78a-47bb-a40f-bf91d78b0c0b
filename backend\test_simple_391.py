#!/usr/bin/env python3
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
django.setup()

# Test simple
try:
    import daily_update_orchestrator
    result = daily_update_orchestrator.test_single_company(391)
    
    # Écrire le résultat dans un fichier
    with open('test_result_391.txt', 'w', encoding='utf-8') as f:
        f.write("🏦 TEST BANK OF AFRICA (ID: 391)\n")
        f.write("=" * 40 + "\n\n")
        
        f.write(f"Succès: {result.get('success', False)}\n")
        
        if result.get('success', False):
            f.write(f"Entreprise: {result.get('company_name', 'Bank of Africa')}\n")
            f.write(f"ID: {result.get('company_id', 391)}\n")
            f.write(f"Prix de clôture: {result.get('closing_price', 'N/A')} MAD\n")
            f.write(f"Volume: {result.get('volume', 'N/A')}\n")
            f.write(f"Nombre d'enregistrements: {result.get('records_count', 0)}\n")
            f.write(f"Date des données: {result.get('data_date', 'N/A')}\n")
        else:
            f.write(f"Erreur: {result.get('error', 'Erreur inconnue')}\n")
        
        f.write("\n✅ Test terminé!\n")
    
    print("Résultat écrit dans test_result_391.txt")
    
except Exception as e:
    with open('test_result_391.txt', 'w', encoding='utf-8') as f:
        f.write(f"❌ Erreur: {e}\n")
    print(f"Erreur: {e}")
