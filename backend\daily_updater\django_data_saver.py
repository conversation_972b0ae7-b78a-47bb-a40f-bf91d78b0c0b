"""
Intégration Django pour sauvegarder les données dans XCapitalTerminal_Companies et XCapitalTerminal_CompanyBonds
"""

import os
import sys
import django
import logging
from datetime import datetime, date
from typing import Dict, List, Optional, Tuple
from decimal import Decimal
from pathlib import Path

# Configuration Django
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')

try:
    django.setup()
    from market_data.postgres_models import XCapitalCompany, XCapitalCompanyBond
    from django.db import transaction, connection
    DJANGO_AVAILABLE = True
except Exception as e:
    print(f"⚠️ Django non disponible: {e}")
    DJANGO_AVAILABLE = False

class DjangoDataSaver:
    """
    Classe pour sauvegarder les données dans les tables Django XCapital
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        if not DJANGO_AVAILABLE:
            self.logger.error("❌ Django non disponible - Sauvegarde désactivée")
            return
        
        # Statistiques
        self.companies_created = 0
        self.companies_updated = 0
        self.bonds_created = 0
        self.bonds_updated = 0
        self.errors = []
    
    def create_or_update_company(self, company_id: int, company_name: str) -> Optional[XCapitalCompany]:
        """
        Crée ou met à jour une entreprise dans XCapitalTerminal_Companies
        
        Args:
            company_id: ID de l'entreprise depuis l'API Casablanca
            company_name: Nom de l'entreprise
            
        Returns:
            Instance XCapitalCompany ou None
        """
        if not DJANGO_AVAILABLE:
            return None
        
        try:
            # Générer un symbol basé sur l'ID si pas de symbol spécifique
            symbol = f"CB{company_id}"  # CB = Casablanca Bourse
            company_id_str = str(company_id)
            
            # Chercher d'abord par company_id
            try:
                company = XCapitalCompany.objects.get(company_id=company_id_str)
                # Mettre à jour le nom si nécessaire
                if company.nom_francais != company_name:
                    company.nom_francais = company_name
                    company.save(update_fields=['nom_francais', 'updated_at'])
                    self.companies_updated += 1
                    self.logger.debug(f"✅ Entreprise mise à jour: {company_name}")
                return company
                
            except XCapitalCompany.DoesNotExist:
                # Créer nouvelle entreprise
                company = XCapitalCompany.objects.create(
                    symbol=symbol,
                    company_id=company_id_str,
                    nom_francais=company_name,
                    nom_anglais=company_name,  # Utiliser le même nom par défaut
                    nom_arabe=None
                )
                self.companies_created += 1
                self.logger.info(f"✅ Nouvelle entreprise créée: {company_name} (ID: {company_id})")
                return company
                
        except Exception as e:
            error_msg = f"Erreur création/mise à jour entreprise {company_id}: {e}"
            self.errors.append(error_msg)
            self.logger.error(f"❌ {error_msg}")
            return None
    
    def save_company_bond_data(self, company: XCapitalCompany, daily_summary: Dict) -> bool:
        """
        Sauvegarde les données de bond d'une entreprise
        
        Args:
            company: Instance XCapitalCompany
            daily_summary: Résumé des données quotidiennes
            
        Returns:
            bool: Succès de la sauvegarde
        """
        if not DJANGO_AVAILABLE or not company:
            return False
        
        try:
            # Convertir la date
            trade_date = datetime.strptime(daily_summary['date'], '%Y-%m-%d').date()
            
            # Préparer les données
            bond_data = {
                'date_trade': trade_date,
                'open_price': self.convert_to_decimal(daily_summary.get('opening_price', 0)),
                'high_price': self.convert_to_decimal(daily_summary.get('high_price', 0)),
                'low_price': self.convert_to_decimal(daily_summary.get('low_price', 0)),
                'close_price': self.convert_to_decimal(daily_summary.get('closing_price', 0)),
                'current_price': self.convert_to_decimal(daily_summary.get('closing_price', 0)),  # Utilisé comme prix actuel
                'volume': daily_summary.get('volume', 0),
                'shares_traded': daily_summary.get('quantity', 0),
                'total_trades': daily_summary.get('total_trades', 0),
                'adjusted_close': self.convert_to_decimal(daily_summary.get('closing_price', 0)),
            }
            
            # Calculer market_cap si possible (approximatif)
            if bond_data['close_price'] and bond_data['shares_traded']:
                estimated_market_cap = bond_data['close_price'] * bond_data['shares_traded']
                bond_data['market_cap'] = estimated_market_cap
            
            # Chercher un enregistrement existant
            try:
                existing_bond = XCapitalCompanyBond.objects.get(
                    company=company,
                    date_trade=trade_date
                )
                
                # Mettre à jour l'enregistrement existant
                for field, value in bond_data.items():
                    setattr(existing_bond, field, value)
                
                existing_bond.save()
                self.bonds_updated += 1
                self.logger.debug(f"✅ Bond mis à jour: {company.symbol} - {trade_date}")
                return True
                
            except XCapitalCompanyBond.DoesNotExist:
                # Créer un nouvel enregistrement
                XCapitalCompanyBond.objects.create(
                    company=company,
                    **bond_data
                )
                self.bonds_created += 1
                self.logger.debug(f"✅ Nouveau bond créé: {company.symbol} - {trade_date}")
                return True
                
        except Exception as e:
            error_msg = f"Erreur sauvegarde bond {company.symbol}: {e}"
            self.errors.append(error_msg)
            self.logger.error(f"❌ {error_msg}")
            return False
    
    def convert_to_decimal(self, value) -> Optional[Decimal]:
        """Convertit une valeur en Decimal pour la base de données"""
        try:
            if value is None or value == '':
                return None
            
            if isinstance(value, Decimal):
                return value
            
            return Decimal(str(value))
            
        except Exception:
            return None
    
    def save_all_data(self, all_summaries: List[Dict]) -> Dict:
        """
        Sauvegarde toutes les données dans Django
        
        Args:
            all_summaries: Liste de tous les résumés quotidiens
            
        Returns:
            Dict: Statistiques de sauvegarde
        """
        if not DJANGO_AVAILABLE:
            return {
                'success': False,
                'error': 'Django non disponible',
                'companies_processed': 0,
                'bonds_saved': 0
            }
        
        self.logger.info(f"💾 Sauvegarde Django - {len(all_summaries)} entreprises")
        
        companies_processed = 0
        bonds_saved = 0
        
        try:
            with transaction.atomic():
                for summary in all_summaries:
                    # Ignorer les résumés sans données
                    if summary.get('status') == 'no_data' or summary.get('closing_price', 0) <= 0:
                        continue
                    
                    # Créer/mettre à jour l'entreprise
                    company = self.create_or_update_company(
                        summary['company_id'],
                        summary['company_name']
                    )
                    
                    if company:
                        companies_processed += 1
                        
                        # Sauvegarder les données de bond
                        if self.save_company_bond_data(company, summary):
                            bonds_saved += 1
            
            # Statistiques finales
            stats = {
                'success': True,
                'companies_processed': companies_processed,
                'companies_created': self.companies_created,
                'companies_updated': self.companies_updated,
                'bonds_saved': bonds_saved,
                'bonds_created': self.bonds_created,
                'bonds_updated': self.bonds_updated,
                'total_errors': len(self.errors),
                'errors': self.errors[-5:] if self.errors else []
            }
            
            self.logger.info(f"✅ Sauvegarde Django terminée - {companies_processed} entreprises, {bonds_saved} bonds")
            return stats
            
        except Exception as e:
            error_msg = f"Erreur sauvegarde Django: {e}"
            self.logger.error(f"❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg,
                'companies_processed': companies_processed,
                'bonds_saved': bonds_saved
            }
    
    def verify_database_connection(self) -> bool:
        """Vérifie la connexion à la base de données"""
        if not DJANGO_AVAILABLE:
            return False
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
                # Vérifier l'existence des tables
                cursor.execute("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name IN ('XCapitalTerminal_Companies', 'XCapitalTerminal_CompanyBonds')
                """)
                tables = cursor.fetchall()
                
                if len(tables) >= 2:
                    self.logger.info("✅ Connexion base de données et tables XCapital vérifiées")
                    return True
                else:
                    self.logger.error("❌ Tables XCapital manquantes")
                    return False
                    
        except Exception as e:
            self.logger.error(f"❌ Erreur vérification base de données: {e}")
            return False
    
    def get_latest_data_info(self) -> Dict:
        """Retourne des informations sur les dernières données"""
        if not DJANGO_AVAILABLE:
            return {'error': 'Django non disponible'}
        
        try:
            # Compter les entreprises
            companies_count = XCapitalCompany.objects.count()
            
            # Dernière date de trading
            latest_bond = XCapitalCompanyBond.objects.order_by('-date_trade').first()
            latest_date = latest_bond.date_trade if latest_bond else None
            
            # Compter les bonds du jour le plus récent
            bonds_today = 0
            if latest_date:
                bonds_today = XCapitalCompanyBond.objects.filter(date_trade=latest_date).count()
            
            return {
                'companies_total': companies_count,
                'latest_trading_date': latest_date.strftime('%Y-%m-%d') if latest_date else None,
                'bonds_latest_date': bonds_today,
                'database_status': 'connected'
            }
            
        except Exception as e:
            return {
                'error': f"Erreur récupération info: {e}",
                'database_status': 'error'
            }
