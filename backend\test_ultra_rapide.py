#!/usr/bin/env python3
"""
Test ultra-rapide de l'API Casablanca
"""

import requests
import json

print("🧪 TEST ULTRA-RAPIDE API CASABLANCA")
print("=" * 40)

try:
    url = "https://www.casablanca-bourse.com/api/proxy/fr/api/bourse_data/last_market_watches/action"
    
    # Headers minimal
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        'Accept': 'application/json'
    }
    
    # Params minimal
    params = {
        'page[limit]': 1,
        'page[offset]': 0
    }
    
    print("🔗 Tentative de connexion...")
    
    # Timeout très court pour éviter l'attente
    response = requests.get(url, params=params, headers=headers, timeout=3)
    
    print(f"📊 Status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ API ACCESSIBLE!")
        try:
            data = response.json()
            print(f"📦 Type réponse: {type(data)}")
            if isinstance(data, dict) and 'data' in data:
                print(f"🏢 Entreprises: {len(data['data'])}")
            print("🎉 API FONCTIONNE!")
        except:
            print("⚠️ Réponse non-JSON")
    else:
        print(f"❌ Erreur: {response.status_code}")
        
except requests.exceptions.Timeout:
    print("❌ Timeout - API lente ou inaccessible")
except requests.exceptions.ConnectionError:
    print("❌ Erreur de connexion - Vérifier Internet")
except Exception as e:
    print(f"❌ Erreur: {e}")

print("\n📋 RÉSULTAT:")
print("Si vous voyez '✅ API ACCESSIBLE!', l'intégration devrait fonctionner.")
print("Sinon, il faudra utiliser les données mock en attendant.")
